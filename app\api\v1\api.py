from fastapi import APIRouter

from app.api.v1.endpoints import server
from app.api.v1.endpoints.sys import (
    admin, auth, bconf, bot, character, clazz, exercise, framework,
    line, module, plan, question, scene, scene_speech, student,
    subject, teacher, tenant, tnt_admin, tnt_bconf, track, user,
    worksheet, worksheet_answer
)
from app.api.v1.endpoints.tnt import (
    auth as tnt_auth, character as tnt_character, clazz as tnt_clazz,
    exercise as tnt_exercise, framework as tnt_framework, line as tnt_line,
    module as tnt_module, plan as tnt_plan, question as tnt_question,
    scene as tnt_scene, scene_speech as tnt_scene_speech, student as tnt_student,
    subject as tnt_subject, teacher as tnt_teacher, unit as tnt_unit,
    worksheet as tnt_worksheet, worksheet_answer as tnt_worksheet_answer
)

api_router = APIRouter()

# Server endpoints
api_router.include_router(server.router, prefix="/srv", tags=["srv"])

# Sys endpoints
api_router.include_router(admin.router, prefix="/sys/admin", tags=["sys"])
api_router.include_router(auth.router, prefix="/sys/auth", tags=["sys"])
api_router.include_router(bconf.router, prefix="/sys/bconf", tags=["sys"])
api_router.include_router(bot.router, prefix="/sys/bot", tags=["sys"])
api_router.include_router(character.router, prefix="/sys/character", tags=["sys"])
api_router.include_router(clazz.router, prefix="/sys/clazz", tags=["sys"])
api_router.include_router(exercise.router, prefix="/sys/exercise", tags=["sys"])
api_router.include_router(framework.router, prefix="/sys/framework", tags=["sys"])
api_router.include_router(line.router, prefix="/sys/line", tags=["sys"])
api_router.include_router(module.router, prefix="/sys/module", tags=["sys"])
api_router.include_router(plan.router, prefix="/sys/plan", tags=["sys"])
api_router.include_router(question.router, prefix="/sys/question", tags=["sys"])
api_router.include_router(scene.router, prefix="/sys/scene", tags=["sys"])
api_router.include_router(scene_speech.router, prefix="/sys/scene-speech", tags=["sys"])
api_router.include_router(student.router, prefix="/sys/student", tags=["sys"])
api_router.include_router(subject.router, prefix="/sys/subject", tags=["sys"])
api_router.include_router(teacher.router, prefix="/sys/teacher", tags=["sys"])
api_router.include_router(tenant.router, prefix="/sys/tenant", tags=["sys"])
api_router.include_router(tnt_admin.router, prefix="/sys/tnt-admin", tags=["sys"])
api_router.include_router(tnt_bconf.router, prefix="/sys/tnt-bconf", tags=["sys"])
api_router.include_router(user.router, prefix="/sys/user", tags=["sys"])
api_router.include_router(worksheet.router, prefix="/sys/worksheet", tags=["sys"])
api_router.include_router(worksheet_answer.router, prefix="/sys/worksheet-answer", tags=["sys"])
api_router.include_router(track.router, prefix="/sys/track", tags=["sys"])

# Tenant endpoints
api_router.include_router(tnt_auth.router, prefix="/tnt/auth", tags=["tnt"])
api_router.include_router(tnt_character.router, prefix="/tnt/character", tags=["tnt"])
api_router.include_router(tnt_clazz.router, prefix="/tnt/clazz", tags=["tnt"])
api_router.include_router(tnt_exercise.router, prefix="/tnt/exercise", tags=["tnt"])
api_router.include_router(tnt_framework.router, prefix="/tnt/framework", tags=["tnt"])
api_router.include_router(tnt_line.router, prefix="/tnt/line", tags=["tnt"])
api_router.include_router(tnt_module.router, prefix="/tnt/module", tags=["tnt"])
api_router.include_router(tnt_plan.router, prefix="/tnt/plan", tags=["tnt"])
api_router.include_router(tnt_question.router, prefix="/tnt/question", tags=["tnt"])
api_router.include_router(tnt_scene.router, prefix="/tnt/scene", tags=["tnt"])
api_router.include_router(tnt_scene_speech.router, prefix="/tnt/scene-speech", tags=["tnt"])
api_router.include_router(tnt_student.router, prefix="/tnt/student", tags=["tnt"])
api_router.include_router(tnt_subject.router, prefix="/tnt/subject", tags=["tnt"])
api_router.include_router(tnt_teacher.router, prefix="/tnt/teacher", tags=["tnt"])
api_router.include_router(tnt_unit.router, prefix="/tnt/unit", tags=["tnt"])
api_router.include_router(tnt_worksheet.router, prefix="/tnt/worksheet", tags=["tnt"])
api_router.include_router(tnt_worksheet_answer.router, prefix="/tnt/worksheet-answer", tags=["tnt"])
