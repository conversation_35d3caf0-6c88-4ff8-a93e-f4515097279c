from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.base import DeleteResponse
from app.schemas.sys.admin import (
    SysAdminCreate,
    SysAdminListResponse,
    SysAdminResponse,
    SysAdminUpdate,
)
from app.services.sys.admin import (
    create_admin,
    delete_admin,
    get_admin,
    get_admins,
    update_admin,
)
from app.services.sys.auth import get_current_sys_admin

router = APIRouter()


@router.post("", response_model=SysAdminResponse)
def create_admin_api(
    admin: SysAdminCreate,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    创建管理员

    ## 功能描述
    创建新的系统管理员账户。

    ## 请求参数
    - **admin** (SysAdminCreate): 管理员创建信息，请求体

    ## 响应
    - **200**: 成功创建管理员，返回 SysAdminResponse

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 可能的业务异常由服务层处理
    """
    db_admin = create_admin(db, admin)
    return SysAdminResponse(
        id=db_admin.id,
        uid=db_admin.uid,
        username=db_admin.user.username,
        name=db_admin.name,
        role=db_admin.role,
        token_version=db_admin.user.token_version,
        ctime=db_admin.user.ctime,
        active=db_admin.user.active,
    )


@router.get("/{admin_id}", response_model=SysAdminResponse)
def get_admin_api(
    admin_id: int,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    获取管理员

    ## 功能描述
    根据管理员ID获取管理员的详细信息。

    ## 请求参数
    - **admin_id** (int): 管理员ID，路径参数

    ## 响应
    - **200**: 成功返回管理员信息，返回 SysAdminResponse
    - **404**: 管理员不存在

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 当管理员不存在时，返回404错误
    """
    db_admin = get_admin(db, admin_id)
    if not db_admin:
        raise HTTPException(status_code=404, detail="管理员不存在")
    return SysAdminResponse(
        id=db_admin.id,
        uid=db_admin.uid,
        username=db_admin.user.username,
        name=db_admin.name,
        role=db_admin.role,
        token_version=db_admin.user.token_version,
        ctime=db_admin.user.ctime,
        active=db_admin.user.active,
    )


@router.get("", response_model=SysAdminListResponse)
def get_admins_api(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    username: Optional[str] = Query(None, description="按用户名搜索"),
    name: Optional[str] = Query(None, description="按姓名搜索"),
    active: Optional[int] = Query(None, description="按用户状态筛选，可选值为0(禁用)或1(启用)"),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    start_time: Optional[datetime] = Query(
        None, description="创建时间的开始时间，格式为ISO 8601"
    ),
    end_time: Optional[datetime] = Query(
        None, description="创建时间的结束时间，格式为ISO 8601"
    ),
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    获取管理员列表

    ## 功能描述
    获取系统管理员列表，支持分页查询、分别按用户名和姓名搜索、按用户状态筛选、排序和按创建时间区间筛选。

    ## 请求参数
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 返回的记录数限制，默认为100，范围1-100
    - **username** (str, optional): 按用户名搜索，支持模糊匹配
    - **name** (str, optional): 按姓名搜索，支持模糊匹配
    - **active** (int, optional): 按用户状态筛选，可选值为0(禁用)或1(启用)，不传则返回所有状态
    - **sort_by** (str, optional): 排序字段，可选值：id, ctime
    - **sort_order** (str, optional): 排序方式，可选值：asc, desc
    - **start_time** (datetime, optional): 创建时间的开始时间，格式为ISO 8601
    - **end_time** (datetime, optional): 创建时间的结束时间，格式为ISO 8601

    ## 响应
    - **200**: 成功返回管理员列表，返回 SysAdminListResponse

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误由FastAPI自动处理
    """
    admins, total = get_admins(
        db, skip, limit, username, name, active, sort_by, sort_order, start_time, end_time
    )
    sys_admin_resps = [
        SysAdminResponse(
            id=admin.id,
            uid=admin.uid,
            username=admin.user.username,
            name=admin.name,
            role=admin.role,
            token_version=admin.user.token_version,
            ctime=admin.user.ctime,
            active=admin.user.active,
        )
        for admin in admins
    ]
    return {"total": total, "items": sys_admin_resps}


@router.put("/{admin_id}", response_model=SysAdminResponse)
def update_admin_api(
    admin_id: int,
    admin: SysAdminUpdate,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    更新管理员

    ## 功能描述
    更新指定管理员的信息。

    ## 请求参数
    - **admin_id** (int): 管理员ID，路径参数
    - **admin** (SysAdminUpdate): 管理员更新信息，请求体

    ## 响应
    - **200**: 成功更新管理员信息，返回 SysAdminResponse
    - **404**: 管理员不存在

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 当管理员不存在时，返回404错误
    """
    db_admin = update_admin(db, admin_id, admin)
    if not db_admin:
        raise HTTPException(status_code=404, detail="管理员不存在")
    return SysAdminResponse(
        id=db_admin.id,
        uid=db_admin.uid,
        username=db_admin.user.username,
        name=db_admin.name,
        role=db_admin.role,
        token_version=db_admin.user.token_version,
        ctime=db_admin.user.ctime,
        active=db_admin.user.active,
    )


@router.delete("/{admin_id}", response_model=DeleteResponse)
def delete_admin_api(
    admin_id: int,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    删除管理员

    ## 功能描述
    删除指定的系统管理员账户。

    ## 请求参数
    - **admin_id** (int): 管理员ID，路径参数

    ## 响应
    - **200**: 成功删除管理员，返回删除成功消息
    - **404**: 管理员不存在

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 当管理员不存在时，返回404错误
    """
    if not delete_admin(db, admin_id):
        raise HTTPException(status_code=404, detail="管理员不存在")
    return {"message": "删除成功"}
