from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.base import DeleteResponse
from app.schemas.sys.auth import (
    LoginRequest,
    LoginResponse,
    LogoutResponse,
    SysAdminPasswordUpdate,
    SysAdminProfile,
)
from app.services.sys.auth import (
    get_current_sys_admin,
    get_sys_admin_profile,
    login,
    logout,
    update_sys_admin_password,
)

router = APIRouter()


@router.post("/login", response_model=LoginResponse)
def login_api(request: LoginRequest, db: Session = Depends(get_db)):
    """
    系统管理员登录

    ## 功能描述
    系统管理员使用用户名和密码进行身份验证登录。

    ## 请求参数
    - **request** (LoginRequest): 登录请求信息，请求体
        - username: 管理员用户名
        - password: 管理员密码

    ## 响应
    - **200**: 登录成功
        - 返回类型: LoginResponse
        - 包含访问令牌和令牌类型
            - token: JWT访问令牌
            - token_type: 令牌类型，固定为"bearer"
            - uid: 管理员ID
            - username: 管理员用户名
            - name: 管理员姓名
            - role: 管理员角色

    ## 权限要求
    - 无需身份验证（这是登录接口）

    ## 错误处理
    - **400**: 登录失败
        - 用户名或密码错误时返回此错误
    """
    admin, token, error = login(db, request.username, request.password)
    if error:
        raise HTTPException(status_code=400, detail=error)
    return {
        "token": token,
        "token_type": "bearer",
        "uid": admin.uid if admin else None,
        "username": admin.user.username if admin else None,
        "name": admin.name if admin else None,
        "role": admin.role if admin else None,
    }


@router.post("/logout", response_model=LogoutResponse)
def logout_api(
    db: Session = Depends(get_db), current_admin=Depends(get_current_sys_admin)
):
    """
    系统管理员登出

    ## 功能描述
    系统管理员注销登录，使当前访问令牌失效。

    ## 请求参数
    - 无需额外参数，通过Authorization header传递Bearer token

    ## 响应
    - **200**: 登出成功
        - 返回类型: LogoutResponse
        - 包含登出成功的消息

    ## 权限要求
    - 需要有效的系统管理员身份令牌

    ## 错误处理
    - **401**: 未授权访问，当令牌无效或过期时返回此错误
    """
    logout(db, current_admin.uid)
    return {"message": "登出成功"}


@router.get("/profile", response_model=SysAdminProfile)
def get_profile(
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    db: Session = Depends(get_db),
):
    """
    获取系统管理员个人信息

    ## 功能描述
    获取当前登录系统管理员的个人资料信息。

    ## 请求参数
    - 无需额外参数，通过Authorization header传递Bearer token

    ## 响应
    - **200**: 成功返回个人信息
        - 返回类型: SysAdminProfile
        - 包含管理员的个人资料信息
            - id: 管理员ID
            - username: 用户名
            - name: 姓名
            - role: 角色

    ## 权限要求
    - 需要有效的系统管理员身份令牌

    ## 错误处理
    - **404**: 管理员信息不存在
        - 当管理员记录在数据库中不存在时返回此错误
    """
    admin = get_sys_admin_profile(db, current_admin.id)
    if not admin:
        raise HTTPException(status_code=404, detail="Admin not found")

    # 构造响应对象，包含用户信息
    return SysAdminProfile(
        uid=admin.uid,
        username=admin.user.username,
        name=admin.name,
        role=admin.role,
    )


@router.put("/password", response_model=DeleteResponse)
def update_password(
    password_update: SysAdminPasswordUpdate,
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    db: Session = Depends(get_db),
):
    """
    更新系统管理员密码

    ## 功能描述
    允许系统管理员更新自己的登录密码。

    ## 请求参数
    - **password_update** (SysAdminPasswordUpdate): 密码更新信息，请求体
        - old_password: 当前密码
        - new_password: 新密码
        - confirm_password: 确认新密码

    ## 响应
    - **200**: 密码更新成功
        - 返回成功消息

    ## 权限要求
    - 需要有效的系统管理员身份令牌

    ## 错误处理
    - **400**: 密码更新失败
        - 当前密码错误或管理员不存在时返回此错误
    """
    if not update_sys_admin_password(db, current_admin.id, password_update):
        raise HTTPException(
            status_code=400, detail="Incorrect password or admin not found"
        )
    return {"message": "密码更新成功"}
