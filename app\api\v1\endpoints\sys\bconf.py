from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.base import DeleteResponse
from app.schemas.sys.bconf import (
    BconfCreate,
    BconfListResponse,
    BconfResponse,
    BconfUpdate,
)
from app.services.sys.auth import get_current_sys_admin
from app.services.sys.bconf import (
    create_bconf,
    delete_bconf,
    get_bconf,
    get_bconfs,
    update_bconf,
)

router = APIRouter()


@router.post("", response_model=BconfResponse)
def create_bconf_api(
    bconf: BconfCreate,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    创建机器人设置

    ## 功能描述
    创建一个新的机器人配置项。

    ## 请求参数
    - **bconf** (BconfCreate): 机器人配置创建数据，包含配置键名、值、描述等信息

    ## 响应
    - **200**: 创建成功
        - 返回类型: BconfResponse
        - 包含创建成功的机器人配置信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 配置键名已存在或参数验证失败
    """
    db_bconf = create_bconf(db, bconf)
    return db_bconf


@router.get("/{key}", response_model=BconfResponse)
def get_bconf_api(
    key: str,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    获取机器人设置

    ## 功能描述
    根据配置键名获取指定的机器人配置信息。

    ## 请求参数
    - **key** (str): 配置键名，路径参数

    ## 响应
    - **200**: 获取成功
        - 返回类型: BconfResponse
        - 包含机器人配置的详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 机器人设置不存在
    """
    db_bconf = get_bconf(db, key)
    if not db_bconf:
        raise HTTPException(status_code=404, detail="机器人设置不存在")
    return db_bconf


@router.get("", response_model=BconfListResponse)
def get_bconfs_api(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    keyword: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    获取机器人设置列表

    ## 功能描述
    获取所有机器人配置信息，支持分页、关键词搜索。

    ## 请求参数
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 每页返回的记录数，默认为100，范围为1-100
    - **keyword** (str): 关键词搜索，可选，用于搜索配置键名或描述

    ## 响应
    - **200**: 获取成功
        - 返回类型: BconfListResponse
        - 包含总数和配置列表的分页响应数据

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    bconfs, total = get_bconfs(db, skip, limit, keyword)
    return {"total": total, "items": bconfs}


@router.put("/{key}", response_model=BconfResponse)
def update_bconf_api(
    key: str,
    bconf: BconfUpdate,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    更新机器人设置

    ## 功能描述
    根据配置键名更新指定的机器人配置信息。

    ## 请求参数
    - **key** (str): 原配置键名，路径参数
    - **bconf** (BconfUpdate): 机器人配置更新数据，包含需要更新的字段
        - **key** (str): 新的配置键名，可选
        - **bid** (int): 机器人ID，可选
        - **notes** (str): 备注，可选

    ## 响应
    - **200**: 更新成功
        - 返回类型: BconfResponse
        - 包含更新后的机器人配置信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 新配置键名已存在或参数验证失败
    - **404**: 机器人设置不存在

    ## 注意事项
    - 如果更新配置键名，系统会删除旧记录并创建新记录
    - 确保新的机器人ID (bid) 对应的机器人存在
    """
    try:
        db_bconf = update_bconf(db, key, bconf)
        if not db_bconf:
            raise HTTPException(status_code=404, detail="机器人设置不存在")
        return db_bconf
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/{key}", response_model=DeleteResponse)
def delete_bconf_api(
    key: str,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    删除机器人设置

    ## 功能描述
    根据配置键名删除指定的机器人配置。

    ## 请求参数
    - **key** (str): 配置键名，路径参数

    ## 响应
    - **200**: 删除成功
        - 返回类型: DeleteResponse
        - 包含删除成功的状态信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 机器人设置不存在
    """
    if not delete_bconf(db, key):
        raise HTTPException(status_code=404, detail="机器人设置不存在")
    return {"message": "删除成功"}
