from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.base import DeleteResponse
from app.schemas.sys.bot import BotCreate, BotListResponse, BotResponse, BotUpdate
from app.services.sys.auth import get_current_sys_admin
from app.services.sys.bot import create_bot, delete_bot, get_bot, get_bots, update_bot

router = APIRouter()


@router.post("", response_model=BotResponse)
def create_bot_api(
    bot: BotCreate,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    创建机器人

    ## 功能描述
    创建一个新的机器人实例。

    ## 请求参数
    - **bot** (BotCreate): 机器人创建数据，包含机器人名称、类型、配置等基本信息

    ## 响应
    - **200**: 创建成功
        - 返回类型: BotResponse
        - 包含创建成功的机器人信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    db_bot = create_bot(db, bot)
    return db_bot


@router.get("/{bot_id}", response_model=BotResponse)
def get_bot_api(
    bot_id: int,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    获取机器人

    ## 功能描述
    根据机器人ID获取指定机器人的详细信息。

    ## 请求参数
    - **bot_id** (int): 机器人ID，路径参数

    ## 响应
    - **200**: 获取成功
        - 返回类型: BotResponse
        - 包含机器人的详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 机器人不存在
    """
    db_bot = get_bot(db, bot_id)
    if not db_bot:
        raise HTTPException(status_code=404, detail="机器人不存在")
    return db_bot


@router.get("", response_model=BotListResponse)
def get_bots_api(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    keyword: Optional[str] = Query(None),
    active: Optional[int] = Query(None, description="按机器人状态筛选，可选值为0(禁用)或1(启用)"),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    获取机器人列表

    ## 功能描述
    获取所有机器人信息，支持分页、关键词搜索、按状态筛选和排序功能。

    ## 请求参数
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 每页返回的记录数，默认为100，范围为1-100
    - **keyword** (str): 关键词搜索，可选，用于搜索机器人名称或描述
    - **active** (int): 按机器人状态筛选，可选值为0(禁用)或1(启用)，不传则返回所有状态
    - **sort_by** (str): 排序字段，可选值：id
    - **sort_order** (str): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 获取成功
        - 返回类型: BotListResponse
        - 包含总数和机器人列表的分页响应数据

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    bots, total = get_bots(db, skip, limit, keyword, active, sort_by, sort_order)
    return {"total": total, "items": bots}


@router.put("/{bot_id}", response_model=BotResponse)
def update_bot_api(
    bot_id: int,
    bot: BotUpdate,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    更新机器人

    ## 功能描述
    根据机器人ID更新指定机器人的信息。

    ## 请求参数
    - **bot_id** (int): 机器人ID，路径参数
    - **bot** (BotUpdate): 机器人更新数据，包含需要更新的字段

    ## 响应
    - **200**: 更新成功
        - 返回类型: BotResponse
        - 包含更新后的机器人信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 机器人不存在
    """
    db_bot = update_bot(db, bot_id, bot)
    if not db_bot:
        raise HTTPException(status_code=404, detail="机器人不存在")
    return db_bot


@router.delete("/{bot_id}", response_model=DeleteResponse)
def delete_bot_api(
    bot_id: int,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    删除机器人

    ## 功能描述
    根据机器人ID删除指定的机器人。

    ## 请求参数
    - **bot_id** (int): 机器人ID，路径参数

    ## 响应
    - **200**: 删除成功
        - 返回类型: DeleteResponse
        - 包含删除成功的状态信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 机器人不存在
    """
    if not delete_bot(db, bot_id):
        raise HTTPException(status_code=404, detail="机器人不存在")
    return {"message": "删除成功"}
