from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.constants import OSS_UPLOAD_PATH_CHARACTER_AVATAR
from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.character import CharacterCreate, CharacterResponse, CharacterUpdate, CharacterListItemResponse, CharacterListResponse, AvatarUploadURL, AvatarDeleteRequest
from app.services import character as character_service
from app.services.sys.auth import get_current_sys_admin
from app.utils.oss import get_oss_url, generate_oss_image_upload_url, get_file_extension, delete_oss_file, extract_object_name_from_url

router = APIRouter()


@router.get("", response_model=CharacterListResponse)
def get_characters(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active: Optional[int] = Query(None),
    published: Optional[int] = Query(None),
    name: Optional[str] = Query(None, description="按姓名搜索"),
    profile: Optional[str] = Query(None, description="按人物资料搜索"),
    notes: Optional[str] = Query(None, description="按备注搜索"),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取人物列表

    ## 功能描述
    获取指定租户下的所有人物信息，支持分页、多种筛选条件、搜索和排序。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填，用于指定查询哪个租户的人物
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 每页返回的记录数，默认为100，范围为1-100
    - **active** (int): 人物状态筛选，可选值为0(禁用)或1(启用)，不传则返回所有状态
    - **published** (int): 发布状态筛选，可选值为0(未发布)或1(已发布)，不传则返回所有状态
    - **name** (str): 按姓名搜索，支持模糊匹配
    - **profile** (str): 按人物资料搜索，支持模糊匹配
    - **notes** (str): 按备注搜索，支持模糊匹配
    - **sort_by** (str): 排序字段，可选值：id, ctime
    - **sort_order** (str): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 获取成功
        - 返回类型: CharacterListResponse
        - 包含人物信息列表（不包含后台字段）

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    characters, total = character_service.get_characters(
        db=db,
        tenant_id=tenant_id,
        skip=skip,
        limit=limit,
        active=active,
        published=published,
        name=name,
        profile=profile,
        notes=notes,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    
    # 转换为列表项响应模型（不包含后台字段）
    character_items = []
    for character in characters:
        # 处理avatar字段
        avatar_url = get_oss_url(character.avatar) if character.avatar else None
        
        character_items.append(CharacterListItemResponse(
            id=character.id,
            tenant_id=character.tenant_id,
            name=character.name,
            gender=character.gender,
            avatar=avatar_url,
            profile=character.profile,
            timbre_type=character.timbre_type,
            timbre=character.timbre,
            notes=character.notes,
            published=character.published,
            ctime=character.ctime,
            active=character.active,
        ))
    
    return CharacterListResponse(total=total, items=character_items)


@router.post("", response_model=CharacterResponse)
def create_character(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    character_in: CharacterCreate,
):
    """
    创建人物

    ## 功能描述
    创建一个新的人物角色。

    ## 请求参数
    - **character_in** (CharacterCreate): 人物创建数据，包含人物名称、描述、所属租户等信息

    ## 响应
    - **200**: 创建成功
        - 返回类型: CharacterResponse
        - 包含创建成功的人物信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    character = character_service.create_character(db=db, character_in=character_in)
    if character.avatar:
        character.avatar = get_oss_url(character.avatar)
    return character


@router.get("/{character_id}", response_model=CharacterResponse)
def get_character(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    character_id: int,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    获取人物信息

    ## 功能描述
    根据人物ID获取单个人物的详细信息。

    ## 请求参数
    - **character_id** (int): 人物ID，路径参数
    - **tenant_id** (int): 租户ID，必填，用于权限验证

    ## 响应
    - **200**: 获取成功
        - 返回类型: CharacterResponse
        - 包含人物的详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 人物不存在
    - **403**: 无权访问该人物（人物不属于指定租户）
    """
    character = character_service.get_character(db=db, character_id=character_id)
    if not character:
        raise HTTPException(status_code=404, detail="人物不存在")
    if character.tenant_id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问该人物")
    if character.avatar:
        character.avatar = get_oss_url(character.avatar)
    return character


@router.put("/{character_id}", response_model=CharacterResponse)
def update_character(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    character_id: int,
    character_in: CharacterUpdate,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    更新人物信息

    ## 功能描述
    根据人物ID更新人物的信息。

    ## 请求参数
    - **character_id** (int): 人物ID，路径参数
    - **character_in** (CharacterUpdate): 人物更新数据，包含需要更新的字段
    - **tenant_id** (int): 租户ID，必填，用于权限验证

    ## 响应
    - **200**: 更新成功
        - 返回类型: CharacterResponse
        - 包含更新后的人物信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 人物不存在
    - **403**: 无权访问该人物（人物不属于指定租户）
    """
    character = character_service.get_character(db=db, character_id=character_id)
    if not character:
        raise HTTPException(status_code=404, detail="人物不存在")
    if character.tenant_id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问该人物")
    character = character_service.update_character(
        db=db, character_id=character_id, character_in=character_in
    )
    if character and character.avatar:
        character.avatar = get_oss_url(character.avatar)
    return character


@router.get("/avatar/upload-url", response_model=AvatarUploadURL)
def get_avatar_upload_url(
    *,
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    file_name: str = Query(..., description="文件名"),
):
    """
    获取人物头像上传的鉴权URL

    ## 功能描述
    获取人物头像上传的鉴权URL，前端可以直接使用该URL上传头像文件。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数
    - **file_name** (str): 文件名，必填查询参数

    ## 响应
    - **200**: 获取成功
        - 返回类型: AvatarUploadURL
        - 包含上传URL、文件路径、文件访问URL和过期时间

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 生成上传URL失败
    """
    try:
        # 获取文件扩展名
        file_extension = get_file_extension(file_name)
        
        # 生成上传URL
        upload_info = generate_oss_image_upload_url(
            upload_path=OSS_UPLOAD_PATH_CHARACTER_AVATAR,
            file_extension=file_extension
        )
        
        return AvatarUploadURL(
            upload_url=upload_info["upload_url"],
            file_path=upload_info["file_path"],
            file_url=upload_info["file_url"],
            expires=upload_info["expires"]
        )
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"生成头像上传URL失败: {str(e)}"
        )


@router.delete("/avatar")
def delete_avatar_file(
    *,
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: AvatarDeleteRequest,
):
    """
    删除人物头像文件

    ## 功能描述
    删除指定的人物头像OSS文件，仅删除OSS文件，不修改数据库中的人物记录。

    ## 请求参数
    - **request** (AvatarDeleteRequest): 删除请求数据，请求体
        - **file_path** (str): 要删除的文件OSS signed URL
            - 例如：https://bucket.oss-region.aliyuncs.com/character/avatar/uuid.jpg?Expires=xxx&...
            - 不支持相对路径

    ## 响应
    - **200**: 删除成功
        - 返回删除操作的结果信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 文件URL无效或删除失败
    - **404**: 文件不存在
    """
    try:
        # 从OSS signed URL中提取相对路径
        object_name = extract_object_name_from_url(request.file_path)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    
    # 调用OSS删除函数
    delete_result = delete_oss_file(object_name)
    
    if not delete_result["success"]:
        # 根据错误消息决定HTTP状态码
        if "文件不存在" in delete_result["message"]:
            raise HTTPException(status_code=404, detail=delete_result["message"])
        else:
            raise HTTPException(status_code=400, detail=delete_result["message"])
    
    return {
        "message": delete_result["message"],
        "file_path": delete_result.get("file_path")
    }
