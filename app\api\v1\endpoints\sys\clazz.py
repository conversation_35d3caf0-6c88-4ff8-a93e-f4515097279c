from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.constants import OSS_UPLOAD_PATH_CLASS_PIC
from app.db.session import get_db
from app.schemas.base import DeleteResponse
from app.schemas.clazz import (
    ClassCreate,
    ClassListResponse,
    ClassResponse,
    ClassUpdate,
    PicDeleteRequest,
    PicUploadURL,
    ClassExerciseCreate,
    ClassExerciseUpdate,
    ClassExerciseWithExerciseInfoResponse,
    ClassExerciseListResponse,
    ClassExerciseBatchOrderRequest,
    ClassExerciseBatchOrderResponse,
    ClassExerciseBatchSetTeacherRequest,
    ClassExerciseBatchSetTeacherResponse,
    ClassStudentCreate,
    ClassStudentUpdate,
    ClassStudentWithStudentInfoResponse,
    ClassStudentListResponse,
    ClassStudentBatchOrderRequest,
    ClassStudentBatchOrderResponse,
    ClassStudentBatchDeleteRequest,
    ClassStudentBatchDeleteResponse,
    ClassStudentBatchCreateRequest,
    ClassStudentBatchCreateResponse,
    ClassImportPlanRequest,
    ClassImportPlanResponse,
)
from app.services import clazz as class_service
from app.services.sys.auth import get_current_sys_admin
from app.utils.oss import (
    delete_oss_file,
    extract_object_name_from_url,
    generate_oss_image_upload_url,
    get_file_extension,
    get_oss_url,
)

router = APIRouter()


@router.get("", response_model=ClassListResponse)
def get_classes(
    *,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active: Optional[int] = Query(None, ge=0, le=1),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    name: Optional[str] = Query(None, description="按班级名称搜索"),
    notes: Optional[str] = Query(None, description="按备注搜索"),
    start_time: Optional[datetime] = Query(
        None, description="时间窗口开始时间，查找班级时间段被包含在此窗口中的班级"
    ),
    end_time: Optional[datetime] = Query(
        None, description="时间窗口结束时间，查找班级时间段被包含在此窗口中的班级"
    ),
):
    """
    获取班级列表

    ## 功能描述
    获取指定租户下的所有班级信息，支持分页、状态筛选、搜索和排序。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数，指定要查询的租户
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 每页返回的记录数，默认为100，范围为1-100
    - **active** (int): 班级状态筛选，可选值为0(禁用)或1(启用)，不传则返回所有状态
    - **sort_by** (str): 排序字段，可选值：id, ctime
    - **sort_order** (str): 排序方式，可选值：asc, desc
    - **name** (str): 按班级名称搜索，支持模糊匹配
    - **notes** (str): 按备注搜索，支持模糊匹配
    - **start_time** (datetime): 时间窗口开始时间，查找班级时间段被包含在此窗口中的班级
    - **end_time** (datetime): 时间窗口结束时间，查找班级时间段被包含在此窗口中的班级

    ## 响应
    - **200**: 获取成功
        - 返回类型: ClassListResponse
        - 包含班级信息列表和总数

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    classes, total = class_service.get_classes(
        db, tenant_id, skip, limit, active, sort_by, sort_order, name, notes, start_time, end_time
    )
    
    # 转换pic字段为签名URL
    class_responses = []
    for cls in classes:
        pic_url = get_oss_url(cls.pic) if cls.pic else None
        class_responses.append(ClassResponse(
            id=cls.id,
            tenant_id=cls.tenant_id,
            name=cls.name,
            pic=pic_url,
            description=cls.description,
            notes=cls.notes,
            btime=cls.btime,
            etime=cls.etime,
            ctime=cls.ctime,
            active=cls.active,
        ))
    
    return ClassListResponse(total=total, items=class_responses)


@router.post("", response_model=ClassResponse)
def create_class(
    *,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
    class_in: ClassCreate,
):
    """
    创建班级

    ## 功能描述
    在指定租户下创建一个新的班级。

    ## 请求参数
    - **class_in** (ClassCreate): 班级创建数据，包含班级名称、描述等基本信息

    ## 响应
    - **200**: 创建成功
        - 返回类型: ClassResponse
        - 包含创建成功的班级信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    db_class = class_service.create_class(db, class_in)
    
    # 转换pic字段为签名URL
    pic_url = get_oss_url(db_class.pic) if db_class.pic else None
    
    return ClassResponse(
        id=db_class.id,
        tenant_id=db_class.tenant_id,
        name=db_class.name,
        pic=pic_url,
        description=db_class.description,
        notes=db_class.notes,
        btime=db_class.btime,
        etime=db_class.etime,
        ctime=db_class.ctime,
        active=db_class.active,
    )


@router.get("/{class_id}", response_model=ClassResponse)
def get_class(
    *,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
    class_id: int,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    获取班级信息

    ## 功能描述
    根据班级ID获取单个班级的详细信息。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数，用于权限验证

    ## 响应
    - **200**: 获取成功
        - 返回类型: ClassResponse
        - 包含班级的详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 班级不存在或不属于指定租户
    """
    db_class = class_service.get_class(db, class_id)
    if not db_class or db_class.tenant_id != tenant_id:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    # 转换pic字段为签名URL
    pic_url = get_oss_url(db_class.pic) if db_class.pic else None
    
    return ClassResponse(
        id=db_class.id,
        tenant_id=db_class.tenant_id,
        name=db_class.name,
        pic=pic_url,
        description=db_class.description,
        notes=db_class.notes,
        btime=db_class.btime,
        etime=db_class.etime,
        ctime=db_class.ctime,
        active=db_class.active,
    )


@router.put("/{class_id}", response_model=ClassResponse)
def update_class(
    *,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
    class_id: int,
    class_in: ClassUpdate,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    更新班级信息

    ## 功能描述
    根据班级ID更新班级的信息。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **class_in** (ClassUpdate): 班级更新数据，包含需要更新的字段
    - **tenant_id** (int): 租户ID，必填查询参数，用于权限验证

    ## 响应
    - **200**: 更新成功
        - 返回类型: ClassResponse
        - 包含更新后的班级信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 班级不存在或不属于指定租户
    """
    db_class = class_service.get_class(db, class_id)
    if not db_class or db_class.tenant_id != tenant_id:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    updated_class = class_service.update_class(db, class_id, class_in)
    if not updated_class:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    # 转换pic字段为签名URL
    pic_url = get_oss_url(updated_class.pic) if updated_class.pic else None
    
    return ClassResponse(
        id=updated_class.id,
        tenant_id=updated_class.tenant_id,
        name=updated_class.name,
        pic=pic_url,
        description=updated_class.description,
        notes=updated_class.notes,
        btime=updated_class.btime,
        etime=updated_class.etime,
        ctime=updated_class.ctime,
        active=updated_class.active,
    )


@router.get("/pic/upload-url", response_model=PicUploadURL)
def get_pic_upload_url(
    *,
    current_admin: dict = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    file_name: str = Query(..., description="文件名"),
):
    """
    获取班级图片上传的鉴权URL

    ## 功能描述
    获取班级图片上传的鉴权URL，前端可以直接使用该URL上传图片文件。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数
    - **file_name** (str): 文件名，必填查询参数

    ## 响应
    - **200**: 获取成功
        - 返回类型: PicUploadURL
        - 包含上传URL、文件路径、文件访问URL和过期时间

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 生成上传URL失败
    """
    try:
        # 获取文件扩展名
        file_extension = get_file_extension(file_name)
        
        # 生成上传URL
        upload_info = generate_oss_image_upload_url(
            upload_path=OSS_UPLOAD_PATH_CLASS_PIC,
            file_extension=file_extension
        )
        
        return PicUploadURL(
            upload_url=upload_info["upload_url"],
            file_path=upload_info["file_path"],
            file_url=upload_info["file_url"],
            expires=upload_info["expires"]
        )
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"生成图片上传URL失败: {str(e)}"
        )


@router.delete("/pic")
def delete_pic_file(
    *,
    current_admin: dict = Depends(get_current_sys_admin),
    request: PicDeleteRequest,
):
    """
    删除班级图片文件

    ## 功能描述
    删除指定的班级图片OSS文件，仅删除OSS文件，不修改数据库中的班级记录。

    ## 请求参数
    - **request** (PicDeleteRequest): 删除请求数据，请求体
        - **file_path** (str): 要删除的文件OSS signed URL
            - 例如：https://bucket.oss-region.aliyuncs.com/class/pic/uuid.jpg?Expires=xxx&...
            - 不支持相对路径

    ## 响应
    - **200**: 删除成功
        - 返回删除操作的结果信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 文件URL无效或删除失败
    - **404**: 文件不存在
    """
    try:
        # 从OSS signed URL中提取相对路径
        object_name = extract_object_name_from_url(request.file_path)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    
    # 调用OSS删除函数
    delete_result = delete_oss_file(object_name)
    
    if not delete_result["success"]:
        # 根据错误消息决定HTTP状态码
        if "文件不存在" in delete_result["message"]:
            raise HTTPException(status_code=404, detail=delete_result["message"])
        else:
            raise HTTPException(status_code=400, detail=delete_result["message"])
    
    return {
        "message": delete_result["message"],
        "file_path": delete_result.get("file_path")
    }


@router.delete("/{class_id}", response_model=DeleteResponse)
def delete_class(
    *,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
    class_id: int,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    删除班级

    ## 功能描述
    根据班级ID删除班级。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数，用于权限验证

    ## 响应
    - **200**: 删除成功
        - 返回类型: DeleteResponse
        - 包含删除成功的状态信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 班级不存在或不属于指定租户

    ## 注意
    - 删除操作不可逆，请谨慎操作
    """
    db_class = class_service.get_class(db, class_id)
    if not db_class or db_class.tenant_id != tenant_id:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    success = class_service.delete_class(db, class_id)
    if not success:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    return {"message": "删除成功"}


# ===== ClassExercise 相关接口 =====

@router.get("/{class_id}/exercises", response_model=ClassExerciseListResponse)
def get_class_exercises(
    class_id: int,
    tenant_id: int = Query(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
):
    """
    获取班级练习关系列表

    ## 功能描述
    根据班级ID获取该班级下的练习关系列表，默认按priority从小到大排序。
    只返回已发布且有效的练习（published=1且active=1）。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数

    ## 响应
    - **200**: 成功返回班级练习关系列表
        - 返回类型: ClassExerciseListResponse
        - 包含练习关系的详细信息数组和总数，包含exercise的title, type, pic, intro

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    class_exercises, total = class_service.get_class_exercises(
        db=db,
        class_id=class_id,
        tenant_id=tenant_id,
    )
    
    class_exercise_resps = []
    for class_exercise_row in class_exercises:
        class_exercise, exercise, teacher = class_exercise_row
        
        # 处理老师信息
        tname = teacher.name if teacher else None
        tavatar = get_oss_url(teacher.avatar) if teacher and teacher.avatar else None
        
        class_exercise_resps.append(
            ClassExerciseWithExerciseInfoResponse(
                id=class_exercise.id,
                tenant_id=class_exercise.tenant_id,
                cid=class_exercise.cid,
                eid=class_exercise.eid,
                tid=class_exercise.tid,
                depend=class_exercise.depend,
                priority=class_exercise.priority,
                active=class_exercise.active,
                title=exercise.title,
                type=exercise.type,
                pic=get_oss_url(exercise.pic) if exercise.pic else None,
                intro=exercise.intro,
                tname=tname,
                tavatar=tavatar,
            )
        )
    
    return ClassExerciseListResponse(total=total, items=class_exercise_resps)


@router.post("/{class_id}/exercises", response_model=ClassExerciseWithExerciseInfoResponse)
def create_class_exercise(
    class_id: int,
    class_exercise_in: ClassExerciseCreate,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
):
    """
    创建班级练习关系

    ## 功能描述
    创建新的班级练习关系记录。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **class_exercise_in** (ClassExerciseCreate): 班级练习关系创建信息，请求体
        - 包含练习关系的基本信息

    ## 响应
    - **200**: 成功创建班级练习关系
        - 返回类型: ClassExerciseWithExerciseInfoResponse
        - 包含新创建练习关系的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    # 确保 cid 与路径参数一致
    class_exercise_in.cid = class_id
    
    db_class_exercise = class_service.create_class_exercise(db=db, class_exercise_in=class_exercise_in)
    
    # 获取关联的练习信息
    from app.services import exercise as exercise_service
    exercise = exercise_service.get_exercise(db=db, exercise_id=db_class_exercise.eid)
    
    # 获取老师信息
    tname = None
    tavatar = None
    if db_class_exercise.tid:
        from app.services import teacher as teacher_service
        teacher = teacher_service.get_teacher(db=db, teacher_id=db_class_exercise.tid)
        if teacher:
            tname = teacher.name
            tavatar = get_oss_url(teacher.avatar) if teacher.avatar else None
    
    return ClassExerciseWithExerciseInfoResponse(
        id=db_class_exercise.id,
        tenant_id=db_class_exercise.tenant_id,
        cid=db_class_exercise.cid,
        eid=db_class_exercise.eid,
        tid=db_class_exercise.tid,
        depend=db_class_exercise.depend,
        priority=db_class_exercise.priority,
        active=db_class_exercise.active,
        title=exercise.title if exercise else "",
        type=exercise.type if exercise else 1,
        pic=get_oss_url(exercise.pic) if exercise and exercise.pic else None,
        intro=exercise.intro if exercise else None,
        tname=tname,
        tavatar=tavatar,
    )


@router.put("/{class_id}/exercises/{class_exercise_id}", response_model=ClassExerciseWithExerciseInfoResponse)
def update_class_exercise(
    class_id: int,
    class_exercise_id: int,
    class_exercise_in: ClassExerciseUpdate,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
):
    """
    更新班级练习关系信息

    ## 功能描述
    更新指定班级练习关系的信息，主要用于更新depend字段。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **class_exercise_id** (int): 班级练习关系ID，路径参数
    - **class_exercise_in** (ClassExerciseUpdate): 班级练习关系更新信息，请求体
        - 包含需要更新的字段信息（depend、priority、active）

    ## 响应
    - **200**: 成功更新班级练习关系信息
        - 返回类型: ClassExerciseWithExerciseInfoResponse
        - 包含更新后的练习关系完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 班级练习关系不存在
    - 参数验证错误时返回422错误
    """
    updated_class_exercise = class_service.update_class_exercise(
        db=db, 
        class_exercise_id=class_exercise_id, 
        class_exercise_in=class_exercise_in
    )
    if not updated_class_exercise:
        raise HTTPException(status_code=404, detail="班级练习关系不存在")
    
    # 获取关联的练习信息
    from app.services import exercise as exercise_service
    exercise = exercise_service.get_exercise(db=db, exercise_id=updated_class_exercise.eid)
    
    # 获取老师信息
    tname = None
    tavatar = None
    if updated_class_exercise.tid:
        from app.services import teacher as teacher_service
        teacher = teacher_service.get_teacher(db=db, teacher_id=updated_class_exercise.tid)
        if teacher:
            tname = teacher.name
            tavatar = get_oss_url(teacher.avatar) if teacher.avatar else None
    
    return ClassExerciseWithExerciseInfoResponse(
        id=updated_class_exercise.id,
        tenant_id=updated_class_exercise.tenant_id,
        cid=updated_class_exercise.cid,
        eid=updated_class_exercise.eid,
        tid=updated_class_exercise.tid,
        depend=updated_class_exercise.depend,
        priority=updated_class_exercise.priority,
        active=updated_class_exercise.active,
        title=exercise.title if exercise else "",
        type=exercise.type if exercise else 1,
        pic=get_oss_url(exercise.pic) if exercise and exercise.pic else None,
        intro=exercise.intro if exercise else None,
        tname=tname,
        tavatar=tavatar,
    )


@router.delete("/exercises/{id}")
def delete_class_exercise(
    id: int,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
):
    """
    删除班级练习关系

    ## 功能描述
    删除指定的班级练习关系记录。

    ## 请求参数
    - **id** (int): 班级练习关系ID，路径参数

    ## 响应
    - **200**: 成功删除班级练习关系
        - 返回操作结果信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 班级练习关系不存在
    """
    success = class_service.delete_class_exercise(db=db, class_exercise_id=id)
    if not success:
        raise HTTPException(status_code=404, detail="班级练习关系不存在")
    
    return {"message": "班级练习关系删除成功"}


@router.put("/exercises/batch/order", response_model=ClassExerciseBatchOrderResponse)
def batch_update_class_exercise_order(
    request: ClassExerciseBatchOrderRequest,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
):
    """
    批量调整班级练习关系顺序

    ## 功能描述
    批量调整指定班级下的练习关系显示顺序，支持一次性调整多个关系的priority值。

    ## 请求参数
    - **request** (ClassExerciseBatchOrderRequest): 批量顺序调整请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **class_id** (int): 班级ID，必填
        - **class_exercises** (list[ClassExerciseOrderItem]): 练习关系顺序列表，必填
            - **id** (int): 练习关系ID
            - **priority** (int): 新的展示顺序（从小到大）

    ## 响应
    - **200**: 更新成功
        - 返回类型: ClassExerciseBatchOrderResponse
        - 包含成功更新的数量、总数量和更新后的练习关系列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败或更新失败
    - **404**: 部分练习关系不存在或不属于指定班级

    ## 注意事项
    - 只有属于指定租户和班级的练习关系才会被更新
    - 不存在或不属于该班级的关系ID会被忽略，不会报错
    - 返回的成功数量可能小于请求的总数量
    """
    updated_class_exercises, success_count, total_count = class_service.batch_update_class_exercise_order(
        db=db,
        tenant_id=request.tenant_id,
        class_id=request.class_id,
        class_exercise_orders=request.class_exercises
    )
    
    # 构建响应，需要获取关联的练习信息
    class_exercise_responses = []
    from app.services import exercise as exercise_service
    
    for class_exercise in updated_class_exercises:
        exercise = exercise_service.get_exercise(db=db, exercise_id=class_exercise.eid)
        
        # 获取老师信息
        tname = None
        tavatar = None
        if class_exercise.tid:
            from app.services import teacher as teacher_service
            teacher = teacher_service.get_teacher(db=db, teacher_id=class_exercise.tid)
            if teacher:
                tname = teacher.name
                tavatar = get_oss_url(teacher.avatar) if teacher.avatar else None
        
        class_exercise_responses.append(
            ClassExerciseWithExerciseInfoResponse(
                id=class_exercise.id,
                tenant_id=class_exercise.tenant_id,
                cid=class_exercise.cid,
                eid=class_exercise.eid,
                tid=class_exercise.tid,
                depend=class_exercise.depend,
                priority=class_exercise.priority,
                active=class_exercise.active,
                title=exercise.title if exercise else "",
                type=exercise.type if exercise else 1,
                pic=get_oss_url(exercise.pic) if exercise and exercise.pic else None,
                intro=exercise.intro if exercise else None,
                tname=tname,
                tavatar=tavatar,
            )
        )
    
    return ClassExerciseBatchOrderResponse(
        success_count=success_count,
        total_count=total_count,
        updated_class_exercises=class_exercise_responses
    )


@router.put("/exercises/batch/teacher", response_model=ClassExerciseBatchSetTeacherResponse)
def batch_set_class_exercise_teacher(
    request: ClassExerciseBatchSetTeacherRequest,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
):
    """
    批量设置班级练习关系的老师

    ## 功能描述
    批量设置指定班级下的所有练习关系的老师，支持设置或清空老师。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **request** (ClassExerciseBatchSetTeacherRequest): 批量设置老师请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **class_id** (int): 班级ID，必填
        - **teacher_id** (int, optional): 老师ID，传null则清空所有练习的老师

    ## 响应
    - **200**: 设置成功
        - 返回类型: ClassExerciseBatchSetTeacherResponse
        - 包含成功更新的数量、总数量和操作结果信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败或老师不存在
    - **422**: 参数验证错误时返回422错误

    ## 注意事项
    - 只会更新有效的班级练习关系（active=1）
    - 如果传入老师ID，会验证老师是否存在且属于指定租户
    - 传入null可以清空所有练习的老师设置
    - 整个批量操作在数据库事务中进行，保证数据一致性
    """
    try:
        success_count, total_count, message = class_service.batch_set_class_exercise_teacher(
            db=db,
            tenant_id=request.tenant_id,
            class_id=request.class_id,
            teacher_id=request.teacher_id,
        )
        
        return ClassExerciseBatchSetTeacherResponse(
            success_count=success_count,
            total_count=total_count,
            message=message
        )

    except Exception as e:
        if "不存在" in str(e) or "不属于" in str(e):
            raise HTTPException(status_code=400, detail=str(e))
        else:
            raise HTTPException(status_code=500, detail=f"批量设置老师失败: {str(e)}")


@router.post("/{class_id}/exercises/batch", response_model=ClassImportPlanResponse)
def batch_import_exercises_from_plan(
    class_id: int,
    request: ClassImportPlanRequest,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
):
    """
    批量导入计划练习到班级

    ## 功能描述
    批量导入指定计划中的所有练习到指定班级中，创建班级练习关系。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **request** (ClassImportPlanRequest): 批量导入请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **class_id** (int): 班级ID，必填
        - **plan_id** (int): 计划ID，必填
        - **teacher_id** (int, optional): 老师ID，可选，将分配给所有导入的练习

    ## 响应
    - **200**: 批量导入成功
        - 返回类型: ClassImportPlanResponse
        - 包含成功导入的练习数量、总数量、导入的练习关系列表和结果信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败、计划不存在、班级不存在
    - **422**: 参数验证错误时返回422错误

    ## 注意事项
    - 只会批量导入计划中已发布且有效的练习（published=1且active=1）
    - 如果练习已经在班级中存在，会跳过该练习，不会报错
    - 导入的练习会按照计划中的依赖关系设置进行创建
    - 导入的练习按照在班级中现有练习的最大priority值之后的顺序排列
    - 整个批量导入操作在数据库事务中进行，保证数据一致性
    """
    # 确保 class_id 与路径参数一致
    request.class_id = class_id
    
    try:
        imported_class_exercises, success_count, total_count, message = class_service.import_plan_to_class(
            db=db,
            import_request=request,
        )
        
        # 构建响应，需要获取关联的练习信息
        class_exercise_responses = []
        from app.services import exercise as exercise_service
        
        for class_exercise in imported_class_exercises:
            exercise = exercise_service.get_exercise(db=db, exercise_id=class_exercise.eid)
            
            # 获取老师信息
            tname = None
            tavatar = None
            if class_exercise.tid:
                from app.services import teacher as teacher_service
                teacher = teacher_service.get_teacher(db=db, teacher_id=class_exercise.tid)
                if teacher:
                    tname = teacher.name
                    tavatar = get_oss_url(teacher.avatar) if teacher.avatar else None
            
            class_exercise_responses.append(
                ClassExerciseWithExerciseInfoResponse(
                    id=class_exercise.id,
                    tenant_id=class_exercise.tenant_id,
                    cid=class_exercise.cid,
                    eid=class_exercise.eid,
                    tid=class_exercise.tid,
                    depend=class_exercise.depend,
                    priority=class_exercise.priority,
                    active=class_exercise.active,
                    title=exercise.title if exercise else "",
                    type=exercise.type if exercise else 1,
                    pic=get_oss_url(exercise.pic) if exercise and exercise.pic else None,
                    intro=exercise.intro if exercise else None,
                    tname=tname,
                    tavatar=tavatar,
                )
            )
        
        return ClassImportPlanResponse(
            success_count=success_count,
            total_count=total_count,
            imported_class_exercises=class_exercise_responses,
            message=message
        )

    except Exception as e:
        if "不存在" in str(e):
            raise HTTPException(status_code=400, detail=str(e))
        else:
            raise HTTPException(status_code=500, detail=f"批量导入练习失败: {str(e)}")


# ===== ClassStudent 相关接口 =====

@router.get("/{class_id}/students", response_model=ClassStudentListResponse)
def get_class_students(
    class_id: int,
    tenant_id: int = Query(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active: Optional[int] = Query(None, ge=0, le=1),
):
    """
    获取班级学员关系列表

    ## 功能描述
    根据班级ID获取该班级下的学员关系列表，默认按priority从小到大排序。
    包含学员的基本信息（姓名、性别、备注）。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数
    - **skip** (int, optional): 跳过的记录数，用于分页，默认为0
    - **limit** (int, optional): 返回的记录数限制，默认为100，范围1-100
    - **active** (int, optional): 关系状态筛选，0表示失效，1表示有效

    ## 响应
    - **200**: 成功返回班级学员关系列表
        - 返回类型: ClassStudentListResponse
        - 包含学员关系的详细信息数组和总数，包含学员的姓名、性别、备注

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    class_students, total = class_service.get_class_students_with_student_info(
        db=db,
        class_id=class_id,
        tenant_id=tenant_id,
        skip=skip,
        limit=limit,
        active=active,
    )
    
    class_student_resps = []
    for class_student_row in class_students:
        class_student, student = class_student_row
        class_student_resps.append(
            ClassStudentWithStudentInfoResponse(
                id=class_student.id,
                tenant_id=class_student.tenant_id,
                cid=class_student.cid,
                sid=class_student.sid,
                priority=class_student.priority,
                active=class_student.active,
                name=student.name,
                gender=student.gender,
                notes=student.notes,
            )
        )
    
    return ClassStudentListResponse(total=total, items=class_student_resps)


@router.post("/{class_id}/students", response_model=ClassStudentWithStudentInfoResponse)
def create_class_student(
    class_id: int,
    class_student_in: ClassStudentCreate,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
):
    """
    创建班级学员关系

    ## 功能描述
    创建新的班级学员关系记录。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **class_student_in** (ClassStudentCreate): 班级学员关系创建信息，请求体
        - **tenant_id** (int): 租户ID
        - **cid** (int): 班级ID
        - **sid** (int): 学员ID
        - **priority** (int): 展示顺序（从小到大）

    ## 响应
    - **200**: 成功创建班级学员关系
        - 返回类型: ClassStudentWithStudentInfoResponse
        - 包含新创建学员关系的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 学员不存在或班级不存在
    - 参数验证错误时返回422错误
    """
    # 确保 cid 与路径参数一致
    class_student_in.cid = class_id
    
    db_class_student = class_service.create_class_student(db=db, class_student_in=class_student_in)
    
    # 获取关联的学员信息
    from app.services import student as student_service
    student = student_service.get_student(db=db, student_id=db_class_student.sid)
    
    if not student:
        raise HTTPException(status_code=400, detail="学员不存在")
    
    return ClassStudentWithStudentInfoResponse(
        id=db_class_student.id,
        tenant_id=db_class_student.tenant_id,
        cid=db_class_student.cid,
        sid=db_class_student.sid,
        priority=db_class_student.priority,
        active=db_class_student.active,
        name=student.name,
        gender=student.gender,
        notes=student.notes,
    )


@router.put("/{class_id}/students/{class_student_id}", response_model=ClassStudentWithStudentInfoResponse)
def update_class_student(
    class_id: int,
    class_student_id: int,
    class_student_in: ClassStudentUpdate,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
):
    """
    更新班级学员关系信息

    ## 功能描述
    更新指定班级学员关系的信息，主要用于更新priority和active字段。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **class_student_id** (int): 班级学员关系ID，路径参数
    - **class_student_in** (ClassStudentUpdate): 班级学员关系更新信息，请求体
        - **priority** (int, optional): 展示顺序（从小到大）
        - **active** (int, optional): 是否有效（0：失效；1：有效）

    ## 响应
    - **200**: 成功更新班级学员关系信息
        - 返回类型: ClassStudentWithStudentInfoResponse
        - 包含更新后的学员关系完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 班级学员关系不存在
    - 参数验证错误时返回422错误
    """
    updated_class_student = class_service.update_class_student(
        db=db, 
        class_student_id=class_student_id, 
        class_student_in=class_student_in
    )
    if not updated_class_student:
        raise HTTPException(status_code=404, detail="班级学员关系不存在")
    
    # 获取关联的学员信息
    from app.services import student as student_service
    student = student_service.get_student(db=db, student_id=updated_class_student.sid)
    
    if not student:
        raise HTTPException(status_code=400, detail="学员不存在")
    
    return ClassStudentWithStudentInfoResponse(
        id=updated_class_student.id,
        tenant_id=updated_class_student.tenant_id,
        cid=updated_class_student.cid,
        sid=updated_class_student.sid,
        priority=updated_class_student.priority,
        active=updated_class_student.active,
        name=student.name,
        gender=student.gender,
        notes=student.notes,
    )


@router.delete("/students/batch", response_model=ClassStudentBatchDeleteResponse)
def batch_delete_class_students(
    *,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
    request: ClassStudentBatchDeleteRequest,
):
    """
    批量删除班级学员关系

    ## 功能描述
    批量删除指定班级下的学员关系，整个操作在事务中进行。

    ## 请求参数
    - **request** (ClassStudentBatchDeleteRequest): 批量删除请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **class_id** (int): 班级ID，必填
        - **class_student_ids** (list[int]): 班级学员关系ID列表，必填

    ## 响应
    - **200**: 删除成功
        - 返回类型: ClassStudentBatchDeleteResponse
        - 包含成功删除的数量和删除的关系ID列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **500**: 删除操作失败或数据库事务失败时会自动回滚

    ## 注意事项
    - 只有属于指定租户和班级的关系才会被删除
    - 不存在或不属于该租户和班级的关系ID会被忽略，不会报错
    - 整个操作在数据库事务中进行，保证数据一致性
    - 如果过程中出现错误，所有操作会回滚
    """
    try:
        deleted_ids, success_count = class_service.batch_delete_class_students(
            db=db,
            tenant_id=request.tenant_id,
            class_id=request.class_id,
            class_student_ids=request.class_student_ids,
        )

        return ClassStudentBatchDeleteResponse(
            success_count=success_count,
            total_count=len(request.class_student_ids),
            deleted_class_student_ids=deleted_ids,
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除操作失败: {str(e)}")


@router.put("/students/batch/order", response_model=ClassStudentBatchOrderResponse)
def batch_update_class_student_order(
    request: ClassStudentBatchOrderRequest,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
):
    """
    批量调整班级学员关系顺序

    ## 功能描述
    批量调整指定班级下的学员关系显示顺序，支持一次性调整多个关系的priority值。

    ## 请求参数
    - **request** (ClassStudentBatchOrderRequest): 批量顺序调整请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **class_id** (int): 班级ID，必填
        - **class_students** (list[ClassStudentOrderItem]): 学员关系顺序列表，必填
            - **id** (int): 学员关系ID
            - **priority** (int): 新的展示顺序（从小到大）

    ## 响应
    - **200**: 更新成功
        - 返回类型: ClassStudentBatchOrderResponse
        - 包含成功更新的数量、总数量和更新后的学员关系列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败或更新失败
    - **404**: 部分学员关系不存在或不属于指定班级

    ## 注意事项
    - 只有属于指定租户和班级的学员关系才会被更新
    - 不存在或不属于该班级的关系ID会被忽略，不会报错
    - 返回的成功数量可能小于请求的总数量
    """
    updated_class_students, success_count, total_count = class_service.batch_update_class_student_order(
        db=db,
        tenant_id=request.tenant_id,
        class_id=request.class_id,
        class_student_orders=request.class_students
    )
    
    # 构建响应，需要获取关联的学员信息
    class_student_responses = []
    from app.services import student as student_service
    
    for class_student in updated_class_students:
        student = student_service.get_student(db=db, student_id=class_student.sid)
        class_student_responses.append(
            ClassStudentWithStudentInfoResponse(
                id=class_student.id,
                tenant_id=class_student.tenant_id,
                cid=class_student.cid,
                sid=class_student.sid,
                priority=class_student.priority,
                active=class_student.active,
                name=student.name if student else "",
                gender=student.gender if student else 0,
                notes=student.notes if student else None,
            )
        )
    
    return ClassStudentBatchOrderResponse(
        success_count=success_count,
        total_count=total_count,
        updated_class_students=class_student_responses
    )


@router.post("/students/batch", response_model=ClassStudentBatchCreateResponse)
def batch_create_class_students(
    request: ClassStudentBatchCreateRequest,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
):
    """
    批量创建班级学员关系

    ## 功能描述
    批量创建指定班级的学员关系，支持同时添加多个学员到班级中。

    ## 请求参数
    - **request** (ClassStudentBatchCreateRequest): 批量创建请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **class_id** (int): 班级ID，必填
        - **student_ids** (list[int]): 学员ID列表，必填

    ## 响应
    - **200**: 批量创建成功
        - 返回类型: ClassStudentBatchCreateResponse
        - 包含成功创建的数量、总数量和结果信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败、班级不存在
    - **422**: 参数验证错误时返回422错误

    ## 注意事项
    - 只有属于指定租户的学员才会被创建关系
    - 不存在或不属于该租户的学员ID会被跳过，不会报错
    - 如果学员已经在班级中存在，会跳过该学员，不会重复创建
    - 新创建的学员关系会按照在班级中现有学员关系的最大priority值之后的顺序排列
    - 整个批量创建操作在数据库事务中进行，保证数据一致性
    - 如果过程中出现错误，所有操作会回滚
    """
    try:
        success_count, total_count, message = class_service.batch_create_class_students(
            db=db,
            batch_request=request,
        )
        
        return ClassStudentBatchCreateResponse(
            success_count=success_count,
            total_count=total_count,
            message=message
        )

    except Exception as e:
        if "不存在" in str(e) or "不属于" in str(e):
            raise HTTPException(status_code=400, detail=str(e))
        else:
            raise HTTPException(status_code=500, detail=f"批量创建学员关系失败: {str(e)}")

