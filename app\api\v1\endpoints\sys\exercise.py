from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.constants import OSS_UPLOAD_PATH_EXERCISE_PIC
from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.exercise import ExerciseCreate, ExerciseResponse, ExerciseUpdate, PicUploadURL, PicDeleteRequest
from app.services import exercise as exercise_service
from app.services.sys.auth import get_current_sys_admin
from app.utils.oss import get_oss_url, generate_oss_image_upload_url, get_file_extension, delete_oss_file, extract_object_name_from_url

router = APIRouter()


@router.get("", response_model=List[ExerciseResponse])
def get_exercises(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active: Optional[int] = Query(None),
    published: Optional[int] = Query(None),
    type: Optional[int] = Query(None),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    title: Optional[str] = Query(None, description="按标题搜索，支持模糊匹配"),
    notes: Optional[str] = Query(None, description="按备注搜索，支持模糊匹配"),
):
    """
    获取练习列表

    ## 功能描述
    获取指定租户下的所有练习信息，支持分页、多种筛选条件和排序。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数，用于指定查询哪个租户的练习
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 每页返回的记录数，默认为100，范围为1-100
    - **active** (int): 可选，练习状态筛选，可选值为0(禁用)或1(启用)，不传则返回所有状态
    - **published** (int): 可选，发布状态筛选，可选值为0(未发布)或1(已发布)，不传则返回所有状态
    - **type** (int): 可选，练习类型筛选，根据练习类型进行筛选
    - **sort_by** (str): 可选，排序字段，可选值：id, ctime
    - **sort_order** (str): 可选，排序方式，可选值：asc, desc
    - **title** (str): 可选，按标题搜索，支持模糊匹配
    - **notes** (str): 可选，按备注搜索，支持模糊匹配

    ## 响应
    - **200**: 获取成功
        - 返回类型: List[ExerciseResponse]
        - 包含练习信息列表，每个练习包含完整的数据库字段信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    exercises = exercise_service.get_exercises(
        db=db,
        tenant_id=tenant_id,
        skip=skip,
        limit=limit,
        active=active,
        published=published,
        type=type,
        sort_by=sort_by,
        sort_order=sort_order,
        title=title,
        notes=notes,
    )
    # 为exercises添加OSS URL处理
    exercise_responses = []
    for exercise in exercises:
        exercise_responses.append(ExerciseResponse(
            id=exercise.id,
            tenant_id=exercise.tenant_id,
            title=exercise.title,
            type=exercise.type,
            pic=get_oss_url(exercise.pic) if exercise.pic else None,
            intro=exercise.intro,
            duration=exercise.duration,
            version=exercise.version,
            bgtext=exercise.bgtext,
            bgvideo=exercise.bgvideo,
            notes=exercise.notes,
            published=exercise.published,
            ctime=exercise.ctime,
            active=exercise.active,
        ))
    return exercise_responses


@router.post("", response_model=ExerciseResponse)
def create_exercise(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    exercise_in: ExerciseCreate,
):
    """
    创建练习

    ## 功能描述
    创建一个新的练习项目。

    ## 请求参数
    - **exercise_in** (ExerciseCreate): 练习创建数据，请求体
        - 包含练习名称、内容、类型、所属租户等信息

    ## 响应
    - **200**: 创建成功
        - 返回类型: ExerciseResponse
        - 包含创建成功的练习信息，包含分配的ID和其他数据库字段

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    exercise = exercise_service.create_exercise(db=db, exercise_in=exercise_in)
    return ExerciseResponse(
        id=exercise.id,
        tenant_id=exercise.tenant_id,
        title=exercise.title,
        type=exercise.type,
        pic=get_oss_url(exercise.pic) if exercise.pic else None,
        intro=exercise.intro,
        duration=exercise.duration,
        version=exercise.version,
        bgtext=exercise.bgtext,
        bgvideo=exercise.bgvideo,
        notes=exercise.notes,
        published=exercise.published,
        ctime=exercise.ctime,
        active=exercise.active,
    )



@router.get("/{exercise_id}", response_model=ExerciseResponse)
def get_exercise(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    exercise_id: int,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    获取练习信息

    ## 功能描述
    根据练习ID获取单个练习的详细信息。

    ## 请求参数
    - **exercise_id** (int): 练习ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数，用于权限验证

    ## 响应
    - **200**: 获取成功
        - 返回类型: ExerciseResponse
        - 包含练习的详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 练习不存在
    - **403**: 无权访问该练习（练习不属于指定租户）
    """
    exercise = exercise_service.get_exercise(db=db, exercise_id=exercise_id)
    if not exercise:
        raise HTTPException(status_code=404, detail="练习不存在")
    if exercise.tenant_id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问该练习")
    return ExerciseResponse(
        id=exercise.id,
        tenant_id=exercise.tenant_id,
        title=exercise.title,
        type=exercise.type,
        pic=get_oss_url(exercise.pic) if exercise.pic else None,
        intro=exercise.intro,
        duration=exercise.duration,
        version=exercise.version,
        bgtext=exercise.bgtext,
        bgvideo=exercise.bgvideo,
        notes=exercise.notes,
        published=exercise.published,
        ctime=exercise.ctime,
        active=exercise.active,
    )


@router.put("/{exercise_id}", response_model=ExerciseResponse)
def update_exercise(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    exercise_id: int,
    exercise_in: ExerciseUpdate,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    更新练习信息

    ## 功能描述
    根据练习ID更新练习的信息。

    ## 请求参数
    - **exercise_id** (int): 练习ID，路径参数
    - **exercise_in** (ExerciseUpdate): 练习更新数据，请求体
        - 包含需要更新的字段
    - **tenant_id** (int): 租户ID，必填查询参数，用于权限验证

    ## 响应
    - **200**: 更新成功
        - 返回类型: ExerciseResponse
        - 包含更新后的练习信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 练习不存在
    - **403**: 无权访问该练习（练习不属于指定租户）
    """
    exercise = exercise_service.get_exercise(db=db, exercise_id=exercise_id)
    if not exercise:
        raise HTTPException(status_code=404, detail="练习不存在")
    if exercise.tenant_id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问该练习")
    exercise = exercise_service.update_exercise(
        db=db, exercise_id=exercise_id, exercise_in=exercise_in
    )
    if not exercise:
        raise HTTPException(status_code=404, detail="练习不存在")
    return ExerciseResponse(
        id=exercise.id,
        tenant_id=exercise.tenant_id,
        title=exercise.title,
        type=exercise.type,
        pic=get_oss_url(exercise.pic) if exercise.pic else None,
        intro=exercise.intro,
        duration=exercise.duration,
        version=exercise.version,
        bgtext=exercise.bgtext,
        bgvideo=exercise.bgvideo,
        notes=exercise.notes,
        published=exercise.published,
        ctime=exercise.ctime,
        active=exercise.active,
    )


@router.get("/pic/upload-url", response_model=PicUploadURL)
def get_pic_upload_url(
    *,
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    file_name: str = Query(..., description="文件名"),
):
    """
    获取练习图片上传的鉴权URL

    ## 功能描述
    获取练习图片上传的鉴权URL，前端可以直接使用该URL上传图片文件。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数
    - **file_name** (str): 文件名，必填查询参数

    ## 响应
    - **200**: 获取成功
        - 返回类型: PicUploadURL
        - 包含上传URL、文件路径、文件访问URL和过期时间

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 生成上传URL失败
    """
    try:
        # 获取文件扩展名
        file_extension = get_file_extension(file_name)
        
        # 生成上传URL
        upload_info = generate_oss_image_upload_url(
            upload_path=OSS_UPLOAD_PATH_EXERCISE_PIC,
            file_extension=file_extension
        )
        
        return PicUploadURL(
            upload_url=upload_info["upload_url"],
            file_path=upload_info["file_path"],
            file_url=upload_info["file_url"],
            expires=upload_info["expires"]
        )
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"生成图片上传URL失败: {str(e)}"
        )


@router.delete("/pic")
def delete_pic_file(
    *,
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: PicDeleteRequest,
):
    """
    删除练习图片文件

    ## 功能描述
    删除指定的练习图片OSS文件，仅删除OSS文件，不修改数据库中的练习记录。

    ## 请求参数
    - **request** (PicDeleteRequest): 删除请求数据，请求体
        - **file_path** (str): 要删除的文件OSS signed URL
            - 例如：https://bucket.oss-region.aliyuncs.com/exercise/pic/uuid.jpg?Expires=xxx&...
            - 不支持相对路径

    ## 响应
    - **200**: 删除成功
        - 返回删除操作的结果信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 文件URL无效或删除失败
    - **404**: 文件不存在
    """
    try:
        # 从OSS signed URL中提取相对路径
        object_name = extract_object_name_from_url(request.file_path)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    
    # 调用OSS删除函数
    delete_result = delete_oss_file(object_name)
    
    if not delete_result["success"]:
        # 根据错误消息决定HTTP状态码
        if "文件不存在" in delete_result["message"]:
            raise HTTPException(status_code=404, detail=delete_result["message"])
        else:
            raise HTTPException(status_code=400, detail=delete_result["message"])
    
    return {
        "message": delete_result["message"],
        "file_path": delete_result.get("file_path")
    }
