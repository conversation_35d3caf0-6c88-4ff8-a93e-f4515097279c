from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.constants import OSS_UPLOAD_PATH_FRAMEWORK_LOGO
from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.framework import FrameworkCreate, FrameworkResponse, FrameworkUpdate, FrameworkListResponse, LogoUploadURL, LogoDeleteRequest, FrameworkBatchOrderRequest, FrameworkBatchOrderResponse
from app.services import framework as framework_service
from app.services.sys.auth import get_current_sys_admin
from app.utils.oss import get_oss_url, generate_oss_image_upload_url, get_file_extension, delete_oss_file, extract_object_name_from_url

router = APIRouter()


@router.get("", response_model=FrameworkListResponse)
def get_frameworks(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active: Optional[int] = Query(None),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, priority"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取框架列表

    ## 功能描述
    获取指定租户下的所有框架信息，支持分页、筛选功能和排序。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数，用于指定查询哪个租户的框架
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 每页返回的记录数，默认为100，范围为1-100
    - **active** (int): 可选，框架状态筛选，可选值为0(禁用)或1(启用)，不传则返回所有状态
    - **sort_by** (str): 可选，排序字段，可选值：id, priority
    - **sort_order** (str): 可选，排序方式，可选值：asc, desc

    ## 响应
    - **200**: 获取成功
        - 返回类型: FrameworkListResponse
        - 包含总数和框架列表的分页响应数据

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    frameworks, total = framework_service.get_frameworks(
        db=db,
        tenant_id=tenant_id,
        skip=skip,
        limit=limit,
        active=active,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    
    # 为框架列表中的每个框架处理logo字段
    for framework in frameworks:
        if framework.logo:
            framework.logo = get_oss_url(framework.logo)
    
    return {"total": total, "items": frameworks}


@router.post("", response_model=FrameworkResponse)
def create_framework(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    framework_in: FrameworkCreate,
):
    """
    创建框架

    ## 功能描述
    创建一个新的框架。

    ## 请求参数
    - **framework_in** (FrameworkCreate): 框架创建数据，请求体
        - 包含框架名称、描述、所属租户等信息

    ## 响应
    - **200**: 创建成功
        - 返回类型: FrameworkResponse
        - 包含创建成功的框架信息，包含分配的ID和其他数据库字段

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    framework = framework_service.create_framework(db=db, framework_in=framework_in)
    if framework.logo:
        framework.logo = get_oss_url(framework.logo)
    return framework


@router.get("/{framework_id}", response_model=FrameworkResponse)
def get_framework(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    framework_id: int,
):
    """
    获取框架信息

    ## 功能描述
    根据框架ID获取单个框架的详细信息。

    ## 请求参数
    - **framework_id** (int): 框架ID，路径参数

    ## 响应
    - **200**: 获取成功
        - 返回类型: FrameworkResponse
        - 包含框架的详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 框架不存在
    """
    framework = framework_service.get_framework(db=db, framework_id=framework_id)
    if not framework:
        raise HTTPException(status_code=404, detail="框架不存在")
    if framework.logo:
        framework.logo = get_oss_url(framework.logo)
    return framework


@router.put("/{framework_id}", response_model=FrameworkResponse)
def update_framework(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    framework_id: int,
    framework_in: FrameworkUpdate,
):
    """
    更新框架信息

    ## 功能描述
    根据框架ID更新框架的信息。

    ## 请求参数
    - **framework_id** (int): 框架ID，路径参数
    - **framework_in** (FrameworkUpdate): 框架更新数据，请求体
        - 包含需要更新的字段

    ## 响应
    - **200**: 更新成功
        - 返回类型: FrameworkResponse
        - 包含更新后的框架信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 框架不存在
    """
    framework = framework_service.update_framework(
        db=db, framework_id=framework_id, framework_in=framework_in
    )
    if not framework:
        raise HTTPException(status_code=404, detail="框架不存在")
    if framework.logo:
        framework.logo = get_oss_url(framework.logo)
    return framework


@router.get("/logo/upload-url", response_model=LogoUploadURL)
def get_logo_upload_url(
    *,
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    file_name: str = Query(..., description="文件名"),
):
    """
    获取框架Logo上传的鉴权URL

    ## 功能描述
    获取框架Logo上传的鉴权URL，前端可以直接使用该URL上传Logo文件。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数
    - **file_name** (str): 文件名，必填查询参数

    ## 响应
    - **200**: 获取成功
        - 返回类型: LogoUploadURL
        - 包含上传URL、文件路径、文件访问URL和过期时间

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 生成上传URL失败
    """
    try:
        # 获取文件扩展名
        file_extension = get_file_extension(file_name)
        
        # 生成上传URL
        upload_info = generate_oss_image_upload_url(
            upload_path=OSS_UPLOAD_PATH_FRAMEWORK_LOGO,
            file_extension=file_extension
        )
        
        return LogoUploadURL(
            upload_url=upload_info["upload_url"],
            file_path=upload_info["file_path"],
            file_url=upload_info["file_url"],
            expires=upload_info["expires"]
        )
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"生成Logo上传URL失败: {str(e)}"
        )


@router.delete("/logo")
def delete_logo_file(
    *,
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: LogoDeleteRequest,
):
    """
    删除框架Logo文件

    ## 功能描述
    删除指定的框架Logo OSS文件，仅删除OSS文件，不修改数据库中的框架记录。

    ## 请求参数
    - **request** (LogoDeleteRequest): 删除请求数据，请求体
        - **file_path** (str): 要删除的文件OSS signed URL
            - 例如：https://bucket.oss-region.aliyuncs.com/framework/logo/uuid.jpg?Expires=xxx&...
            - 不支持相对路径

    ## 响应
    - **200**: 删除成功
        - 返回删除操作的结果信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 文件URL无效或删除失败
    - **404**: 文件不存在
    """
    try:
        # 从OSS signed URL中提取相对路径
        object_name = extract_object_name_from_url(request.file_path)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    
    # 调用OSS删除函数
    delete_result = delete_oss_file(object_name)
    
    if not delete_result["success"]:
        # 根据错误消息决定HTTP状态码
        if "文件不存在" in delete_result["message"]:
            raise HTTPException(status_code=404, detail=delete_result["message"])
        else:
            raise HTTPException(status_code=400, detail=delete_result["message"])
    
    return {
        "message": delete_result["message"],
        "file_path": delete_result.get("file_path")
    }

@router.put("/batch/order", response_model=FrameworkBatchOrderResponse)
def batch_update_framework_order(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: FrameworkBatchOrderRequest,
):
    """
    批量调整框架顺序

    ## 功能描述
    批量调整指定租户下的框架显示顺序，支持一次性调整多个框架的priority值。

    ## 请求参数
    - **request** (FrameworkBatchOrderRequest): 批量顺序调整请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **frameworks** (list[FrameworkOrderItem]): 框架顺序列表，必填
            - **id** (int): 框架ID
            - **priority** (int): 新的展示顺序（从小到大）

    ## 响应
    - **200**: 更新成功
        - 返回类型: FrameworkBatchOrderResponse
        - 包含成功更新的数量、总数量和更新后的框架列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败或更新失败
    - **404**: 部分框架不存在或不属于指定租户

    ## 注意事项
    - 只有属于指定租户的框架才会被更新
    - 不存在或不属于该租户的框架ID会被忽略，不会报错
    - 返回的成功数量可能小于请求的总数量
    """
    updated_frameworks, success_count, total_count = framework_service.batch_update_framework_order(
        db=db,
        tenant_id=request.tenant_id,
        framework_orders=request.frameworks
    )
    
    # 为框架列表中的每个框架处理logo字段并转换为响应模型
    framework_responses = []
    for framework in updated_frameworks:
        if framework.logo:
            framework.logo = get_oss_url(framework.logo)
        framework_responses.append(FrameworkResponse.model_validate(framework))
    
    return FrameworkBatchOrderResponse(
        success_count=success_count,
        total_count=total_count,
        updated_frameworks=framework_responses
    )

