from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.line import LineCreate, LineResponse, LineUpdate
from app.services import line as line_service
from app.services.sys.auth import get_current_sys_admin

router = APIRouter()


@router.get("/", response_model=List[LineResponse])
def get_lines(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active: Optional[int] = Query(None),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取台词列表

    ## 功能描述
    获取指定租户下的所有台词信息，支持分页、筛选功能和排序。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数，用于指定查询哪个租户的台词
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 每页返回的记录数，默认为100，范围为1-100
    - **active** (int): 可选，台词状态筛选，可选值为0(禁用)或1(启用)，不传则返回所有状态
    - **sort_by** (str): 可选，排序字段，可选值：id, ctime
    - **sort_order** (str): 可选，排序方式，可选值：asc, desc

    ## 响应
    - **200**: 获取成功
        - 返回类型: List[LineResponse]
        - 包含台词信息列表，每个台词包含完整的数据库字段信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    lines = line_service.get_lines(
        db=db,
        tenant_id=tenant_id,
        skip=skip,
        limit=limit,
        active=active,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    return lines


@router.post("/", response_model=LineResponse)
def create_line(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    line_in: LineCreate,
):
    """
    创建台词

    ## 功能描述
    创建一个新的台词。

    ## 请求参数
    - **line_in** (LineCreate): 台词创建数据，请求体
        - 包含台词名称、描述、所属租户等信息

    ## 响应
    - **200**: 创建成功
        - 返回类型: LineResponse
        - 包含创建成功的台词信息，包含分配的ID和其他数据库字段

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    line = line_service.create_line(db=db, line_in=line_in)
    return line


@router.get("/{line_id}", response_model=LineResponse)
def get_line(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    line_id: int,
):
    """
    获取台词信息

    ## 功能描述
    根据台词ID获取单个台词的详细信息。

    ## 请求参数
    - **line_id** (int): 台词ID，路径参数

    ## 响应
    - **200**: 获取成功
        - 返回类型: LineResponse
        - 包含台词的详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 台词不存在
    """
    line = line_service.get_line(db=db, line_id=line_id)
    if not line:
        raise HTTPException(status_code=404, detail="台词不存在")
    return line


@router.put("/{line_id}", response_model=LineResponse)
def update_line(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    line_id: int,
    line_in: LineUpdate,
):
    """
    更新台词信息

    ## 功能描述
    根据台词ID更新台词的信息。

    ## 请求参数
    - **line_id** (int): 台词ID，路径参数
    - **line_in** (LineUpdate): 台词更新数据，请求体
        - 包含需要更新的字段

    ## 响应
    - **200**: 更新成功
        - 返回类型: LineResponse
        - 包含更新后的台词信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 台词不存在
    """
    line = line_service.update_line(db=db, line_id=line_id, line_in=line_in)
    if not line:
        raise HTTPException(status_code=404, detail="台词不存在")
    return line
