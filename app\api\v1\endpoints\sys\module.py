from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.module import ModuleCreate, ModuleListResponse, ModuleUpdate, ModuleResponse, ModuleBatchOrderRequest, ModuleBatchOrderResponse
from app.services import module as module_service
from app.services.sys.auth import get_current_sys_admin

router = APIRouter()


@router.get("", response_model=ModuleListResponse)
def get_modules(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active: Optional[int] = Query(None),
    framework_id: Optional[int] = Query(None, description="框架ID"),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, priority"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取模块列表

    ## 功能描述
    获取指定租户下的所有模块信息，支持分页、筛选功能和排序。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数，用于指定查询哪个租户的模块
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 每页返回的记录数，默认为100，范围为1-100
    - **active** (int): 可选，模块状态筛选，可选值为0(禁用)或1(启用)，不传则返回所有状态
    - **framework_id** (int): 可选，框架ID筛选，用于查询指定框架下的模块
    - **sort_by** (str): 可选，排序字段，可选值：id, priority
    - **sort_order** (str): 可选，排序方式，可选值：asc, desc

    ## 响应
    - **200**: 获取成功
        - 返回类型: ModuleListResponse
        - 包含总数和模块列表的分页响应数据

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    modules, total = module_service.get_modules(
        db=db,
        tenant_id=tenant_id,
        skip=skip,
        limit=limit,
        active=active,
        fid=framework_id,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    return {"total": total, "items": modules}


@router.post("", response_model=ModuleResponse)
def create_module(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    module_in: ModuleCreate,
):
    """
    创建模块

    ## 功能描述
    创建一个新的模块。

    ## 请求参数
    - **module_in** (ModuleCreate): 模块创建数据，请求体
        - 包含模块名称、描述、所属租户等信息

    ## 响应
    - **200**: 创建成功
        - 返回类型: ModuleResponse
        - 包含创建成功的模块信息，包含分配的ID和其他数据库字段

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    module = module_service.create_module(db=db, module_in=module_in)
    return module


@router.get("/{module_id}", response_model=ModuleResponse)
def get_module(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    module_id: int,
):
    """
    获取模块信息

    ## 功能描述
    根据模块ID获取单个模块的详细信息。

    ## 请求参数
    - **module_id** (int): 模块ID，路径参数

    ## 响应
    - **200**: 获取成功
        - 返回类型: ModuleResponse
        - 包含模块的详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 模块不存在
    """
    module = module_service.get_module(db=db, module_id=module_id)
    if not module:
        raise HTTPException(status_code=404, detail="模块不存在")
    return module


@router.put("/{module_id}", response_model=ModuleResponse)
def update_module(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    module_id: int,
    module_in: ModuleUpdate,
):
    """
    更新模块信息

    ## 功能描述
    根据模块ID更新模块的信息。

    ## 请求参数
    - **module_id** (int): 模块ID，路径参数
    - **module_in** (ModuleUpdate): 模块更新数据，请求体
        - 包含需要更新的字段

    ## 响应
    - **200**: 更新成功
        - 返回类型: ModuleResponse
        - 包含更新后的模块信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 模块不存在
    """
    module = module_service.update_module(
        db=db, module_id=module_id, module_in=module_in
    )
    if not module:
        raise HTTPException(status_code=404, detail="模块不存在")
    return module


@router.put("/batch/order", response_model=ModuleBatchOrderResponse)
def batch_update_module_order(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: ModuleBatchOrderRequest,
):
    """
    批量调整模块顺序

    ## 功能描述
    批量调整指定租户下的模块显示顺序，支持一次性调整多个模块的priority值。

    ## 请求参数
    - **request** (ModuleBatchOrderRequest): 批量顺序调整请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **modules** (list[ModuleOrderItem]): 模块顺序列表，必填
            - **id** (int): 模块ID
            - **priority** (int): 新的展示顺序（从小到大）

    ## 响应
    - **200**: 更新成功
        - 返回类型: ModuleBatchOrderResponse
        - 包含成功更新的数量、总数量和更新后的模块列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败或更新失败
    - **404**: 部分模块不存在或不属于指定租户

    ## 注意事项
    - 只有属于指定租户的模块才会被更新
    - 不存在或不属于该租户的模块ID会被忽略，不会报错
    - 返回的成功数量可能小于请求的总数量
    """
    updated_modules, success_count, total_count = module_service.batch_update_module_order(
        db=db,
        tenant_id=request.tenant_id,
        module_orders=request.modules
    )
    
    # 转换为响应模型
    module_responses = []
    for module in updated_modules:
        module_responses.append(ModuleResponse.model_validate(module))
    
    return ModuleBatchOrderResponse(
        success_count=success_count,
        total_count=total_count,
        updated_modules=module_responses
    )
