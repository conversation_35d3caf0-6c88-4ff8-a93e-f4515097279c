from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.constants import OSS_UPLOAD_PATH_PLAN_PIC
from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.plan import (
    PlanCreate,
    PlanListResponse,
    PlanResponse,
    PlanUpdate,
    PicUploadURL,
    PicDeleteRequest,
    PlanExerciseCreate,
    PlanExerciseUpdate,
    PlanExerciseResponse,
    PlanExerciseListResponse,
    PlanExerciseBatchOrderRequest,
    PlanExerciseBatchOrderResponse,
)
from app.services import plan as plan_service
from app.services.sys.auth import get_current_sys_admin
from app.utils.oss import get_oss_url, generate_oss_image_upload_url, get_file_extension, delete_oss_file, extract_object_name_from_url

router = APIRouter()


@router.get("", response_model=PlanListResponse)
def get_plans(
    tenant_id: int,
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active: Optional[int] = Query(None, description="按状态筛选，可选值为0(禁用)或1(启用)"),
    name: Optional[str] = Query(None, description="按计划名称搜索"),
    description: Optional[str] = Query(None, description="按描述搜索"),
    notes: Optional[str] = Query(None, description="按备注搜索"),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    获取租户的计划列表

    ## 功能描述
    根据租户ID获取该租户下的计划列表，支持分页查询、按状态筛选、搜索和排序。

    ## 请求参数
    - **tenant_id** (int): 租户ID，路径参数
    - **skip** (int, optional): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int, optional): 返回的记录数限制，默认为100，范围1-100
    - **active** (int, optional): 按状态筛选，可选值为0(禁用)或1(启用)，不传则返回所有状态
    - **name** (str, optional): 按计划名称搜索，支持模糊匹配
    - **description** (str, optional): 按描述搜索，支持模糊匹配
    - **notes** (str, optional): 按备注搜索，支持模糊匹配
    - **sort_by** (str, optional): 排序字段，可选值：id
    - **sort_order** (str, optional): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回计划列表
        - 返回类型: PlanListResponse
        - 包含计划的详细信息数组和总数

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    plans, total = plan_service.get_plans(
        db=db,
        tenant_id=tenant_id,
        skip=skip,
        limit=limit,
        active=active,
        name=name,
        description=description,
        notes=notes,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    plan_resps = [
        PlanResponse(
            id=plan.id,
            tenant_id=plan.tenant_id,
            name=plan.name,
            pic=get_oss_url(plan.pic) if plan.pic else None,
            description=plan.description,
            notes=plan.notes,
            active=plan.active,
        )
        for plan in plans
    ]
    return PlanListResponse(total=total, items=plan_resps)


@router.post("", response_model=PlanResponse)
def create_plan(
    plan_in: PlanCreate,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    创建计划

    ## 功能描述
    创建新的计划记录。

    ## 请求参数
    - **plan_in** (PlanCreate): 计划创建信息，请求体
        - 包含计划的基本信息

    ## 响应
    - **200**: 成功创建计划
        - 返回类型: PlanResponse
        - 包含新创建计划的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    db_plan = plan_service.create_plan(db=db, plan_in=plan_in)
    return PlanResponse(
        id=db_plan.id,
        tenant_id=db_plan.tenant_id,
        name=db_plan.name,
        pic=get_oss_url(db_plan.pic) if db_plan.pic else None,
        description=db_plan.description,
        notes=db_plan.notes,
        active=db_plan.active,
    )


@router.get("/{plan_id}", response_model=PlanResponse)
def get_plan(
    plan_id: int,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    获取计划信息

    ## 功能描述
    获取指定计划的详细信息。

    ## 请求参数
    - **plan_id** (int): 计划ID，路径参数

    ## 响应
    - **200**: 成功返回计划信息
        - 返回类型: PlanResponse
        - 包含计划的完整详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 计划不存在
    """
    db_plan = plan_service.get_plan(db=db, plan_id=plan_id)
    if not db_plan:
        raise HTTPException(status_code=404, detail="计划不存在")
    return PlanResponse(
        id=db_plan.id,
        tenant_id=db_plan.tenant_id,
        name=db_plan.name,
        pic=get_oss_url(db_plan.pic) if db_plan.pic else None,
        description=db_plan.description,
        notes=db_plan.notes,
        active=db_plan.active,
    )


@router.put("/{plan_id}", response_model=PlanResponse)
def update_plan(
    plan_id: int,
    plan_in: PlanUpdate,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    更新计划信息

    ## 功能描述
    更新指定计划的信息。

    ## 请求参数
    - **plan_id** (int): 计划ID，路径参数
    - **plan_in** (PlanUpdate): 计划更新信息，请求体
        - 包含需要更新的计划字段信息

    ## 响应
    - **200**: 成功更新计划信息
        - 返回类型: PlanResponse
        - 包含更新后的计划完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 计划不存在
    - 参数验证错误时返回422错误
    """
    updated_plan = plan_service.update_plan(db=db, plan_id=plan_id, plan_in=plan_in)
    if not updated_plan:
        raise HTTPException(status_code=404, detail="计划不存在")
    return PlanResponse(
        id=updated_plan.id,
        tenant_id=updated_plan.tenant_id,
        name=updated_plan.name,
        pic=get_oss_url(updated_plan.pic) if updated_plan.pic else None,
        description=updated_plan.description,
        notes=updated_plan.notes,
        active=updated_plan.active,
    )


@router.get("/pic/upload-url", response_model=PicUploadURL)
def get_pic_upload_url(
    *,
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    file_name: str = Query(..., description="文件名"),
):
    """
    获取计划图片上传的鉴权URL

    ## 功能描述
    获取计划图片上传的鉴权URL，前端可以直接使用该URL上传图片文件。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数
    - **file_name** (str): 文件名，必填查询参数

    ## 响应
    - **200**: 获取成功
        - 返回类型: PicUploadURL
        - 包含上传URL、文件路径、文件访问URL和过期时间

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 生成上传URL失败
    """
    try:
        # 获取文件扩展名
        file_extension = get_file_extension(file_name)
        
        # 生成上传URL
        upload_info = generate_oss_image_upload_url(
            upload_path=OSS_UPLOAD_PATH_PLAN_PIC,
            file_extension=file_extension
        )
        
        return PicUploadURL(
            upload_url=upload_info["upload_url"],
            file_path=upload_info["file_path"],
            file_url=upload_info["file_url"],
            expires=upload_info["expires"]
        )
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"生成图片上传URL失败: {str(e)}"
        )


@router.delete("/pic")
def delete_pic_file(
    *,
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: PicDeleteRequest,
):
    """
    删除计划图片文件

    ## 功能描述
    删除指定的计划图片OSS文件，仅删除OSS文件，不修改数据库中的计划记录。

    ## 请求参数
    - **request** (PicDeleteRequest): 删除请求数据，请求体
        - **file_path** (str): 要删除的文件OSS signed URL
            - 例如：https://bucket.oss-region.aliyuncs.com/plan/pic/uuid.jpg?Expires=xxx&...
            - 不支持相对路径

    ## 响应
    - **200**: 删除成功
        - 返回删除操作的结果信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 文件URL无效或删除失败
    - **404**: 文件不存在
    """
    try:
        # 从OSS signed URL中提取相对路径
        object_name = extract_object_name_from_url(request.file_path)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    
    # 调用OSS删除函数
    delete_result = delete_oss_file(object_name)
    
    if not delete_result["success"]:
        # 根据错误消息决定HTTP状态码
        if "文件不存在" in delete_result["message"]:
            raise HTTPException(status_code=404, detail=delete_result["message"])
        else:
            raise HTTPException(status_code=400, detail=delete_result["message"])
    
    return {
        "message": delete_result["message"],
        "file_path": delete_result.get("file_path")
    }


# ===== PlanExercise 相关接口 =====

@router.get("/{plan_id}/exercises", response_model=PlanExerciseListResponse)
def get_plan_exercises(
    plan_id: int,
    tenant_id: int = Query(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    获取计划练习关系列表

    ## 功能描述
    根据计划ID获取该计划下的练习关系列表，默认按priority从小到大排序。
    只返回已发布且有效的练习（published=1且active=1）。

    ## 请求参数
    - **plan_id** (int): 计划ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数

    ## 响应
    - **200**: 成功返回计划练习关系列表
        - 返回类型: PlanExerciseListResponse
        - 包含练习关系的详细信息数组和总数，包含exercise的title, type, pic, intro, depend

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    plan_exercises, total = plan_service.get_plan_exercises(
        db=db,
        plan_id=plan_id,
        tenant_id=tenant_id,
    )
    
    plan_exercise_resps = []
    for plan_exercise_row in plan_exercises:
        plan_exercise, exercise = plan_exercise_row
        plan_exercise_resps.append(
            PlanExerciseResponse(
                id=plan_exercise.id,
                tenant_id=plan_exercise.tenant_id,
                pid=plan_exercise.pid,
                eid=plan_exercise.eid,
                depend=plan_exercise.depend,
                priority=plan_exercise.priority,
                title=exercise.title,
                type=exercise.type,
                pic=get_oss_url(exercise.pic) if exercise.pic else None,
                intro=exercise.intro,
            )
        )
    
    return PlanExerciseListResponse(total=total, items=plan_exercise_resps)


@router.post("/{plan_id}/exercises", response_model=PlanExerciseResponse)
def create_plan_exercise(
    plan_id: int,
    plan_exercise_in: PlanExerciseCreate,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    创建计划练习关系

    ## 功能描述
    创建新的计划练习关系记录。

    ## 请求参数
    - **plan_id** (int): 计划ID，路径参数
    - **plan_exercise_in** (PlanExerciseCreate): 计划练习关系创建信息，请求体
        - 包含练习关系的基本信息

    ## 响应
    - **200**: 成功创建计划练习关系
        - 返回类型: PlanExerciseResponse
        - 包含新创建练习关系的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    # 确保 pid 与路径参数一致
    plan_exercise_in.pid = plan_id
    
    db_plan_exercise = plan_service.create_plan_exercise(db=db, plan_exercise_in=plan_exercise_in)
    
    # 获取关联的练习信息
    from app.services import exercise as exercise_service
    exercise = exercise_service.get_exercise(db=db, exercise_id=db_plan_exercise.eid)
    
    return PlanExerciseResponse(
        id=db_plan_exercise.id,
        tenant_id=db_plan_exercise.tenant_id,
        pid=db_plan_exercise.pid,
        eid=db_plan_exercise.eid,
        depend=db_plan_exercise.depend,
        priority=db_plan_exercise.priority,
        title=exercise.title if exercise else "",
        type=exercise.type if exercise else 1,
        pic=get_oss_url(exercise.pic) if exercise and exercise.pic else None,
        intro=exercise.intro if exercise else None,
    )


@router.put("/{plan_id}/exercises/{plan_exercise_id}", response_model=PlanExerciseResponse)
def update_plan_exercise(
    plan_id: int,
    plan_exercise_id: int,
    plan_exercise_in: PlanExerciseUpdate,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    更新计划练习关系信息

    ## 功能描述
    更新指定计划练习关系的信息，主要用于更新depend字段。

    ## 请求参数
    - **plan_id** (int): 计划ID，路径参数
    - **plan_exercise_id** (int): 计划练习关系ID，路径参数
    - **plan_exercise_in** (PlanExerciseUpdate): 计划练习关系更新信息，请求体
        - 包含需要更新的字段信息（主要是depend字段）

    ## 响应
    - **200**: 成功更新计划练习关系信息
        - 返回类型: PlanExerciseResponse
        - 包含更新后的练习关系完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 计划练习关系不存在
    - 参数验证错误时返回422错误
    """
    updated_plan_exercise = plan_service.update_plan_exercise(
        db=db, 
        plan_exercise_id=plan_exercise_id, 
        plan_exercise_in=plan_exercise_in
    )
    if not updated_plan_exercise:
        raise HTTPException(status_code=404, detail="计划练习关系不存在")
    
    # 获取关联的练习信息
    from app.services import exercise as exercise_service
    exercise = exercise_service.get_exercise(db=db, exercise_id=updated_plan_exercise.eid)
    
    return PlanExerciseResponse(
        id=updated_plan_exercise.id,
        tenant_id=updated_plan_exercise.tenant_id,
        pid=updated_plan_exercise.pid,
        eid=updated_plan_exercise.eid,
        depend=updated_plan_exercise.depend,
        priority=updated_plan_exercise.priority,
        title=exercise.title if exercise else "",
        type=exercise.type if exercise else 1,
        pic=get_oss_url(exercise.pic) if exercise and exercise.pic else None,
        intro=exercise.intro if exercise else None,
    )


@router.delete("/exercises/{id}")
def delete_plan_exercise(
    id: int,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    删除计划练习关系

    ## 功能描述
    删除指定的计划练习关系记录。

    ## 请求参数
    - **id** (int): 计划练习关系ID，路径参数

    ## 响应
    - **200**: 成功删除计划练习关系
        - 返回操作结果信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 计划练习关系不存在
    """
    success = plan_service.delete_plan_exercise(db=db, plan_exercise_id=id)
    if not success:
        raise HTTPException(status_code=404, detail="计划练习关系不存在")
    
    return {"message": "计划练习关系删除成功"}


@router.put("/exercises/batch/order", response_model=PlanExerciseBatchOrderResponse)
def batch_update_plan_exercise_order(
    request: PlanExerciseBatchOrderRequest,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    批量调整计划练习关系顺序

    ## 功能描述
    批量调整指定计划下的练习关系显示顺序，支持一次性调整多个关系的priority值。

    ## 请求参数
    - **request** (PlanExerciseBatchOrderRequest): 批量顺序调整请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **plan_id** (int): 计划ID，必填
        - **plan_exercises** (list[PlanExerciseOrderItem]): 练习关系顺序列表，必填
            - **id** (int): 练习关系ID
            - **priority** (int): 新的展示顺序（从小到大）

    ## 响应
    - **200**: 更新成功
        - 返回类型: PlanExerciseBatchOrderResponse
        - 包含成功更新的数量、总数量和更新后的练习关系列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败或更新失败
    - **404**: 部分练习关系不存在或不属于指定计划

    ## 注意事项
    - 只有属于指定租户和计划的练习关系才会被更新
    - 不存在或不属于该计划的关系ID会被忽略，不会报错
    - 返回的成功数量可能小于请求的总数量
    """
    updated_plan_exercises, success_count, total_count = plan_service.batch_update_plan_exercise_order(
        db=db,
        tenant_id=request.tenant_id,
        plan_id=request.plan_id,
        plan_exercise_orders=request.plan_exercises
    )
    
    # 构建响应，需要获取关联的练习信息
    plan_exercise_responses = []
    from app.services import exercise as exercise_service
    
    for plan_exercise in updated_plan_exercises:
        exercise = exercise_service.get_exercise(db=db, exercise_id=plan_exercise.eid)
        plan_exercise_responses.append(
            PlanExerciseResponse(
                id=plan_exercise.id,
                tenant_id=plan_exercise.tenant_id,
                pid=plan_exercise.pid,
                eid=plan_exercise.eid,
                depend=plan_exercise.depend,
                priority=plan_exercise.priority,
                title=exercise.title if exercise else "",
                type=exercise.type if exercise else 1,
                pic=get_oss_url(exercise.pic) if exercise and exercise.pic else None,
                intro=exercise.intro if exercise else None,
            )
        )
    
    return PlanExerciseBatchOrderResponse(
        success_count=success_count,
        total_count=total_count,
        updated_plan_exercises=plan_exercise_responses
    )
