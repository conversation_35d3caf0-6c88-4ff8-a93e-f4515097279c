from typing import Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.question import (
    Question<PERSON>reate,
    QuestionGuideBatchOrderRequest,
    QuestionGuideBatchOrderResponse,
    QuestionGuideCreate,
    QuestionGuideListResponse,
    QuestionGuideResponse,
    QuestionGuideUpdate,
    QuestionListItemResponse,
    QuestionListResponse,
    QuestionModuleBatchUpdate,
    QuestionModuleResponse,
    QuestionModuleUpdateResponse,
    QuestionRelationResponse,
    QuestionResponse,
    QuestionSubjectBatchUpdate,
    QuestionSubjectUpdateResponse,
    QuestionSubjectResponse,
    QuestionUpdate,
)
from app.services import question as question_service
from app.services.sys.auth import get_current_sys_admin

router = APIRouter()


@router.get("", response_model=QuestionListResponse)
def get_questions(
    tenant_id: int,
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active: Optional[int] = Query(
        None, description="按问题状态筛选，可选值为0(禁用)或1(启用)"
    ),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    subject_id: Optional[int] = Query(None, description="按关联的主题ID筛选"),
    start_time: Optional[datetime] = Query(
        None, description="创建时间的开始时间，格式为ISO 8601"
    ),
    end_time: Optional[datetime] = Query(
        None, description="创建时间的结束时间，格式为ISO 8601"
    ),
    title: Optional[str] = Query(None, description="按标题关键字模糊搜索"),
    notes: Optional[str] = Query(None, description="按备注关键字模糊搜索"),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    获取租户的问题列表

    ## 功能描述
    根据租户ID获取该租户下的问题列表，支持分页查询、按问题状态筛选、排序和按创建时间区间筛选。

    ## 请求参数
    - **tenant_id** (int): 租户ID，路径参数
    - **skip** (int, optional): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int, optional): 返回的记录数限制，默认为100，范围1-100
    - **active** (int, optional): 按问题状态筛选，可选值为0(禁用)或1(启用)，不传则返回所有状态
    - **sort_by** (str, optional): 排序字段，可选值：id, ctime
    - **sort_order** (str, optional): 排序方式，可选值：asc, desc
    - **subject_id** (int, optional): 按关联的主题ID筛选，只返回关联了指定主题的问题
    - **start_time** (datetime, optional): 创建时间的开始时间，格式为ISO 8601
    - **end_time** (datetime, optional): 创建时间的结束时间，格式为ISO 8601
    - **title** (str, optional): 按标题关键字模糊搜索，支持部分匹配
    - **notes** (str, optional): 按备注关键字模糊搜索，支持部分匹配

    ## 响应
    - **200**: 成功返回问题列表
        - 返回类型: QuestionListResponse
        - 包含问题的详细信息数组和总数

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    questions, total = question_service.get_questions(
        db=db,
        tenant_id=tenant_id,
        skip=skip,
        limit=limit,
        active=active,
        sort_by=sort_by,
        sort_order=sort_order,
        start_time=start_time,
        end_time=end_time,
        subject_id=subject_id,
        title=title,
        notes=notes,
    )
    question_resps = [
        QuestionListItemResponse(
            id=question.id,
            tenant_id=question.tenant_id,
            title=question.title,
            notes=question.notes,
            ctime=question.ctime,
            active=question.active,
        )
        for question in questions
    ]
    return QuestionListResponse(total=total, items=question_resps)


@router.post("", response_model=QuestionResponse)
def create_question(
    question_in: QuestionCreate,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    创建问题

    ## 功能描述
    在指定租户下创建新的问题记录。

    ## 请求参数
    - **question_in** (QuestionCreate): 问题创建信息，请求体
        - 包含问题的基本信息

    ## 响应
    - **200**: 成功创建问题
        - 返回类型: QuestionResponse
        - 包含新创建问题的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    question = question_service.create_question(db=db, question_in=question_in)
    return question


# 问题作答指南相关接口
@router.get("/guides", response_model=QuestionGuideListResponse)
def get_question_guides(
    tenant_id: int,
    question_id: int = Query(..., description="问题ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, priority"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    获取问题作答指南列表

    ## 功能描述
    获取指定租户下的问题作答指南列表，支持分页和排序。

    ## 请求参数
    - **tenant_id** (int): 租户ID，查询参数
    - **question_id** (int): 问题ID，必填
    - **skip** (int, optional): 跳过的记录数，用于分页，默认为0
    - **limit** (int, optional): 返回的记录数限制，默认为100，范围1-100
    - **sort_by** (str, optional): 排序字段，可选值：id, priority
    - **sort_order** (str, optional): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回作答指南列表

    ## 权限要求
    - 需要系统管理员权限
    """
    guides, total = question_service.get_question_guides(
        db=db,
        tenant_id=tenant_id,
        question_id=question_id,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    guide_resps = [
        QuestionGuideResponse(
            id=guide.id,
            tenant_id=guide.tenant_id,
            qid=guide.qid,
            title=guide.title,
            details=guide.details,
            priority=guide.priority,
        )
        for guide in guides
    ]
    return QuestionGuideListResponse(total=total, items=guide_resps)


@router.post("/guides", response_model=QuestionGuideResponse)
def create_question_guide(
    guide_in: QuestionGuideCreate,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    创建问题作答指南

    ## 功能描述
    为指定问题创建作答指南。

    ## 请求参数
    - **guide_in** (QuestionGuideCreate): 作答指南创建信息

    ## 响应
    - **200**: 成功创建作答指南

    ## 权限要求
    - 需要系统管理员权限
    """
    guide = question_service.create_question_guide(db=db, guide_in=guide_in)
    return guide


@router.delete("/guides/{guide_id}")
def delete_question_guide(
    guide_id: int,
    tenant_id: int,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    删除问题作答指南

    ## 功能描述
    删除指定的问题作答指南。

    ## 请求参数
    - **guide_id** (int): 作答指南ID，路径参数
    - **tenant_id** (int): 租户ID，查询参数

    ## 响应
    - **200**: 成功删除
    - **404**: 作答指南不存在

    ## 权限要求
    - 需要系统管理员权限
    """
    success = question_service.delete_question_guide(
        db=db, guide_id=guide_id, tenant_id=tenant_id
    )
    if not success:
        raise HTTPException(status_code=404, detail="作答指南不存在")
    return {"message": "作答指南删除成功"}


@router.put("/guides/{guide_id}", response_model=QuestionGuideResponse)
def update_question_guide(
    guide_id: int,
    guide_in: QuestionGuideUpdate,
    tenant_id: int,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    更新问题作答指南

    ## 功能描述
    更新指定的问题作答指南信息。

    ## 请求参数
    - **guide_id** (int): 作答指南ID，路径参数
    - **guide_in** (QuestionGuideUpdate): 作答指南更新信息，请求体
        - **title** (str, optional): 指南标题
        - **details** (str, optional): 指南详情
        - **priority** (int, optional): 展示顺序（从小到大）
    - **tenant_id** (int): 租户ID，查询参数

    ## 响应
    - **200**: 成功更新作答指南
        - 返回类型: QuestionGuideResponse
        - 包含更新后的作答指南完整信息
    - **404**: 作答指南不存在

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    guide = question_service.update_question_guide(
        db=db, guide_id=guide_id, guide_in=guide_in, tenant_id=tenant_id
    )
    if not guide:
        raise HTTPException(status_code=404, detail="作答指南不存在")
    return guide


@router.put("/guides/batch/order", response_model=QuestionGuideBatchOrderResponse)
def batch_update_question_guide_order(
    request: QuestionGuideBatchOrderRequest,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    批量调整问题作答指南顺序

    ## 功能描述
    批量调整指定租户下的问题作答指南显示顺序，支持一次性调整多个作答指南的priority值。

    ## 请求参数
    - **request** (QuestionGuideBatchOrderRequest): 批量顺序调整请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **guides** (list[QuestionGuideOrderItem]): 作答指南顺序列表，必填
            - **id** (int): 作答指南ID
            - **priority** (int): 新的展示顺序（从小到大）

    ## 响应
    - **200**: 更新成功
        - 返回类型: QuestionGuideBatchOrderResponse
        - 包含成功更新的数量、总数量和更新后的作答指南列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败或更新失败
    - **404**: 部分作答指南不存在或不属于指定租户

    ## 注意事项
    - 只有属于指定租户的作答指南才会被更新
    - 不存在或不属于该租户的作答指南ID会被忽略，不会报错
    - 返回的成功数量可能小于请求的总数量
    """
    updated_guides, success_count, total_count = (
        question_service.batch_update_question_guide_order(
            db=db, tenant_id=request.tenant_id, guide_orders=request.guides
        )
    )

    # 转换为响应模型
    guide_responses = []
    for guide in updated_guides:
        guide_responses.append(QuestionGuideResponse.model_validate(guide))

    return QuestionGuideBatchOrderResponse(
        success_count=success_count,
        total_count=total_count,
        updated_guides=guide_responses,
    )


# 问题关联的理论模块和主题接口
@router.get("/tags", response_model=QuestionRelationResponse)
def get_question_relations(
    tenant_id: int,
    question_id: int = Query(..., description="问题ID"),
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    获取问题关联的理论模块和主题

    ## 功能描述
    获取指定租户下某个问题关联的所有理论模块和主题。

    ## 请求参数
    - **tenant_id** (int): 租户ID，查询参数
    - **question_id** (int): 问题ID，必填

    ## 响应
    - **200**: 成功返回关联的理论模块和主题列表
        - **modules**: 关联的理论模块列表
        - **subjects**: 关联的主题列表
        - **module_total**: 理论模块总数
        - **subject_total**: 主题总数

    ## 权限要求
    - 需要系统管理员权限
    """
    modules, module_total, subjects, subject_total = (
        question_service.get_question_relations(
            db=db, tenant_id=tenant_id, question_id=question_id
        )
    )

    # 转换理论模块数据
    module_resps = [
        QuestionModuleResponse(
            id=module.id,
            tenant_id=module.tenant_id,
            qid=module.qid,
            mid=module.mid,
            module_name=module.module.name,
        )
        for module in modules
    ]

    # 转换主题数据
    subject_resps = [
        QuestionSubjectResponse(
            id=subject.id,
            tenant_id=subject.tenant_id,
            qid=subject.qid,
            sid=subject.sid,
            subject_name=subject.subject.name,
        )
        for subject in subjects
    ]

    return QuestionRelationResponse(
        modules=module_resps,
        subjects=subject_resps,
        module_total=module_total,
        subject_total=subject_total,
    )


# 问题-理论模块关系获取接口已合并到 /tags 接口，以下为批量操作接口


@router.put("/modules/batch", response_model=QuestionModuleUpdateResponse)
def batch_update_question_modules(
    batch_in: QuestionModuleBatchUpdate,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    批量更新问题-理论模块关系

    ## 功能描述
    批量更新指定问题的理论模块关系，使用增量更新方式：只删除不需要的关系，只添加新的关系，保持已存在的关系不变。

    ## 请求参数
    - **batch_in** (QuestionModuleBatchUpdate): 批量更新信息，请求体
        - **tenant_id** (int): 租户ID，必填
        - **question_id** (int): 问题ID，必填
        - **module_ids** (list[int]): 理论模块ID列表，必填

    ## 响应
    - **200**: 返回操作结果
        - **success** (bool): 操作是否成功
        - **message** (str): 操作结果信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误

    ## 注意事项
    - 该操作使用增量更新：仅处理变化的部分
    - 已存在的关系保持不变，不会重复创建
    - 不在新列表中的关系会被删除
    - 新列表中不存在的关系会被添加
    - 如果传入空的module_ids列表，则会删除所有现有关系
    - 操作是原子性的，要么全部成功，要么全部回滚
    """
    success = question_service.batch_update_question_modules(
        db=db, batch_in=batch_in
    )

    if success:
        message = "更新成功"
    else:
        message = "更新失败"

    return QuestionModuleUpdateResponse(
        success=success,
        message=message,
    )


# 问题-主题关系获取接口已合并到 /tags 接口，以下为批量操作接口

@router.put("/subjects/batch", response_model=QuestionSubjectUpdateResponse)
def batch_update_question_subjects(
    batch_in: QuestionSubjectBatchUpdate,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    批量更新问题-主题关系

    ## 功能描述
    批量更新指定问题的主题关系，使用增量更新方式：只删除不需要的关系，只添加新的关系，保持已存在的关系不变。

    ## 请求参数
    - **batch_in** (QuestionSubjectBatchUpdate): 批量更新信息，请求体
        - **tenant_id** (int): 租户ID，必填
        - **question_id** (int): 问题ID，必填
        - **subject_ids** (list[int]): 主题ID列表，必填

    ## 响应
    - **200**: 返回操作结果
        - **success** (bool): 操作是否成功
        - **message** (str): 操作结果信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误

    ## 注意事项
    - 该操作使用增量更新：仅处理变化的部分
    - 已存在的关系保持不变，不会重复创建
    - 不在新列表中的关系会被删除
    - 新列表中不存在的关系会被添加
    - 如果传入空的subject_ids列表，则会删除所有现有关系
    - 操作是原子性的，要么全部成功，要么全部回滚
    """
    success = question_service.batch_update_question_subjects(
        db=db, batch_in=batch_in
    )

    if success:
        message = "更新成功"
    else:
        message = "更新失败"

    return QuestionSubjectUpdateResponse(
        success=success,
        message=message,
    )


# 注意：带有路径参数的路由必须放在最后，避免匹配冲突
@router.get("/{question_id}", response_model=QuestionResponse)
def get_question(
    question_id: int,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    获取问题信息

    ## 功能描述
    获取指定问题的详细信息。

    ## 请求参数
    - **question_id** (int): 问题ID，路径参数

    ## 响应
    - **200**: 成功返回问题信息
        - 返回类型: QuestionResponse
        - 包含问题的完整详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 问题不存在
    """
    question = question_service.get_question(db=db, question_id=question_id)
    if not question:
        raise HTTPException(status_code=404, detail="问题不存在")
    return question


@router.put("/{question_id}", response_model=QuestionResponse)
def update_question(
    question_id: int,
    question_in: QuestionUpdate,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    更新问题信息

    ## 功能描述
    更新指定问题的信息。

    ## 请求参数
    - **question_id** (int): 问题ID，路径参数
    - **question_in** (QuestionUpdate): 问题更新信息，请求体
        - 包含需要更新的问题字段信息

    ## 响应
    - **200**: 成功更新问题信息
        - 返回类型: QuestionResponse
        - 包含更新后的问题完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 问题不存在
    - 参数验证错误时返回422错误
    """
    question = question_service.update_question(
        db=db, question_id=question_id, question_in=question_in
    )
    if not question:
        raise HTTPException(status_code=404, detail="问题不存在")
    return question
