from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.base import DeleteResponse
from app.utils.oss import get_oss_url
from app.schemas.scene import (
    SceneCreate, SceneListResponse, SceneListItemResponse, SceneResponse, SceneUpdate,
    SceneCharacterResponse, SceneCharacterCreate, SceneCharacterUpdate, SceneCharacterBatchOrderRequest, 
    SceneCharacterBatchOrderResponse, SceneCharacterBatchDeleteRequest, 
    SceneCharacterBatchDeleteResponse,
    SceneGuideResponse, SceneGuideListResponse, SceneGuideCreate, SceneGuideUpdate,
    SceneGuideBatchOrderRequest, SceneGuideBatchOrderResponse
)
from app.schemas.cue import (
    CueResponse, CueCreate, CueUpdate, CueBatchOrderRequest, CueBatchOrderResponse
)
from app.schemas.line import (
    LineResponse, LineCreate, LineUpdate, LineBatchOrderRequest, LineBatchOrderResponse,
    LineBatchMoveRequest, LineBatchMoveResponse
)
from app.services import scene as scene_service
from app.services import cue as cue_service
from app.services import line as line_service
from app.services.sys.auth import get_current_sys_admin

router = APIRouter()


@router.get("", response_model=SceneListResponse)
def get_scenes(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    title: Optional[str] = Query(None, description="按场景标题搜索"),
    published: Optional[bool] = Query(None, description="按发布状态搜索"),
    active: Optional[bool] = Query(None, description="按激活状态搜索"),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    start_time: Optional[datetime] = Query(
        None, description="创建时间的开始时间，格式为ISO 8601"
    ),
    end_time: Optional[datetime] = Query(
        None, description="创建时间的结束时间，格式为ISO 8601"
    ),
):
    """
    获取场景列表

    ## 功能描述
    获取指定租户下的场景列表，支持分页查询、按标题搜索、按发布状态搜索、按激活状态搜索、排序和按创建时间区间筛选。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数
    - **skip** (int, optional): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int, optional): 返回的记录数限制，默认为100，范围1-100
    - **title** (str, optional): 按场景标题搜索，支持模糊匹配
    - **published** (bool, optional): 按发布状态搜索，true表示已发布，false表示未发布
    - **active** (bool, optional): 按激活状态搜索，true表示已激活，false表示未激活
    - **sort_by** (str, optional): 排序字段，可选值：id, ctime
    - **sort_order** (str, optional): 排序方式，可选值：asc, desc
    - **start_time** (datetime, optional): 创建时间的开始时间，格式为ISO 8601
    - **end_time** (datetime, optional): 创建时间的结束时间，格式为ISO 8601

    ## 响应
    - **200**: 成功返回场景列表
        - 返回类型: SceneListResponse
        - 包含场景及关联练习的详细信息数组和总数

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    scenes, total = scene_service.get_scenes(
        db=db,
        tenant_id=tenant_id,
        skip=skip,
        limit=limit,
        title=title,
        published=published,
        active=active,
        sort_by=sort_by,
        sort_order=sort_order,
        start_time=start_time,
        end_time=end_time,
    )
    
    # 构造响应数据
    items = []
    for scene in scenes:
        exercise = scene.exercise
        items.append(
            SceneListItemResponse(
                id=scene.id,
                tenant_id=scene.tenant_id,
                eid=scene.eid,
                title=exercise.title,
                type=exercise.type,
                intro=exercise.intro,
                duration=exercise.duration,
                version=exercise.version,
                pic=get_oss_url(exercise.pic) if exercise.pic else None,
                notes=exercise.notes,
                published=exercise.published,
                ctime=exercise.ctime,
                active=exercise.active,
            )
        )

    return SceneListResponse(total=total, items=items)


@router.post("", response_model=SceneResponse)
def create_scene(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    scene_in: SceneCreate,
):
    """
    创建场景

    ## 功能描述
    创建一个新的场景。

    ## 请求参数
    - **scene_in** (SceneCreate): 场景创建数据，请求体
        - 包含场景名称、描述、配置、所属租户等信息

    ## 响应
    - **200**: 创建成功
        - 返回类型: SceneResponse
        - 包含创建成功的场景信息，包含分配的ID和其他数据库字段

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **400**: 数据库约束违反时返回400错误
    """
    scene = scene_service.create_scene(db=db, scene_in=scene_in)
    
    # 构造响应数据
    exercise = scene.exercise
    return SceneResponse(
        id=scene.id,
        tenant_id=scene.tenant_id,
        eid=scene.eid,
        title=exercise.title,
        type=exercise.type,
        intro=exercise.intro,
        duration=exercise.duration,
        version=exercise.version,
        pic=get_oss_url(exercise.pic) if exercise.pic else None,
        bgtext=exercise.bgtext,
        notes=exercise.notes,
        published=exercise.published,
        ctime=exercise.ctime,
        active=exercise.active,
        pv_scripts=scene.pv_scripts,
    )


@router.get("/{scene_id}", response_model=SceneResponse)
def get_scene(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    scene_id: int,
):
    """
    获取场景信息

    ## 功能描述
    根据场景ID获取单个场景的详细信息。

    ## 请求参数
    - **scene_id** (int): 场景ID，路径参数

    ## 响应
    - **200**: 获取成功
        - 返回类型: SceneResponse
        - 包含场景的详细信息，包含场景配置和相关设置

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 场景不存在
    """
    scene = scene_service.get_scene(db=db, scene_id=scene_id)
    if not scene:
        raise HTTPException(status_code=404, detail="场景不存在")
    
    # 构造响应数据
    exercise = scene.exercise
    return SceneResponse(
        id=scene.id,
        tenant_id=scene.tenant_id,
        eid=scene.eid,
        title=exercise.title,
        type=exercise.type,
        intro=exercise.intro,
        duration=exercise.duration,
        version=exercise.version,
        pic=get_oss_url(exercise.pic) if exercise.pic else None,
        bgtext=exercise.bgtext,
        notes=exercise.notes,
        published=exercise.published,
        ctime=exercise.ctime,
        active=exercise.active,
        pv_scripts=scene.pv_scripts,
    )


@router.put("/{scene_id}", response_model=SceneResponse)
def update_scene(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    scene_id: int,
    scene_in: SceneUpdate,
):
    """
    更新场景信息

    ## 功能描述
    根据场景ID更新场景的信息。

    ## 请求参数
    - **scene_id** (int): 场景ID，路径参数
    - **scene_in** (SceneUpdate): 场景更新数据，请求体
        - 包含需要更新的字段

    ## 响应
    - **200**: 更新成功
        - 返回类型: SceneResponse
        - 包含更新后的场景信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 场景不存在
    """
    scene = scene_service.update_scene(db=db, scene_id=scene_id, scene_in=scene_in)
    if not scene:
        raise HTTPException(status_code=404, detail="场景不存在")
    
    # 构造响应数据
    exercise = scene.exercise
    return SceneResponse(
        id=scene.id,
        tenant_id=scene.tenant_id,
        eid=scene.eid,
        title=exercise.title,
        type=exercise.type,
        intro=exercise.intro,
        duration=exercise.duration,
        version=exercise.version,
        pic=get_oss_url(exercise.pic) if exercise.pic else None,
        bgtext=exercise.bgtext,
        notes=exercise.notes,
        published=exercise.published,
        ctime=exercise.ctime,
        active=exercise.active,
        pv_scripts=scene.pv_scripts,
    )


@router.delete("/{scene_id}", response_model=DeleteResponse)
def delete_scene(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    scene_id: int,
):
    """
    删除场景

    ## 功能描述
    删除指定的场景记录。

    ## 请求参数
    - **scene_id** (int): 场景ID，路径参数

    ## 响应
    - **200**: 成功删除场景，返回删除成功消息
    - **404**: 场景不存在

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 当场景不存在时，返回404错误
    """
    if not scene_service.delete_scene(db=db, scene_id=scene_id):
        raise HTTPException(status_code=404, detail="场景不存在")
    return {"message": "删除成功"}


# 场景-人物关系相关接口

@router.get("/{scene_id}/characters", response_model=list[SceneCharacterResponse])
def get_scene_characters(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    scene_id: int,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    获取场景关联的人物列表

    ## 功能描述
    获取指定场景下的所有人物，按priority从小到大排序。只返回已发布且激活的人物。

    ## 请求参数
    - **scene_id** (int): 场景ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数

    ## 响应
    - **200**: 成功返回人物列表
        - 返回字段: id（关系ID）, cid（人物ID）, name, gender, avatar, profile, played
        - 按priority字段升序排列
        - 只包含已发布且激活的人物

    ## 权限要求
    - 需要系统管理员权限

    ## 注意
    - 如果场景不存在或不属于指定租户，返回空列表
    """
    scene_characters = scene_service.get_scene_characters(
        db=db, tenant_id=tenant_id, scene_id=scene_id
    )

    return [
        SceneCharacterResponse(
            id=sc.id,
            cid=sc.cid,
            name=sc.character.name,
            gender=sc.character.gender,
            avatar=get_oss_url(sc.character.avatar) if sc.character.avatar else None,
            profile=sc.character.profile,
            played=sc.played,
        )
        for sc in scene_characters
    ]


@router.post("/character", response_model=SceneCharacterResponse)
def create_scene_character(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    scene_character_in: SceneCharacterCreate,
):
    """
    创建场景-人物关系

    ## 功能描述
    为指定场景添加人物关系。

    ## 请求参数
    - **scene_character_in** (SceneCharacterCreate): 场景人物关系创建信息，请求体
        - **tenant_id** (int): 租户ID
        - **sid** (int): 场景ID
        - **cid** (int): 人物ID
        - **played** (int): 是否为学员扮演（0：否；1：是）
        - **priority** (int): 展示顺序（从小到大）

    ## 响应
    - **200**: 成功创建关系
        - 返回类型: SceneCharacterResponse
        - 包含新创建关系的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **400**: 场景不存在、人物不存在或未发布、关系已存在
    """
    try:
        scene_character = scene_service.create_scene_character(
            db=db, scene_character_in=scene_character_in
        )
        
        return SceneCharacterResponse(
            id=scene_character.id,
            cid=scene_character.cid,
            name=scene_character.character.name,
            gender=scene_character.character.gender,
            avatar=get_oss_url(scene_character.character.avatar) if scene_character.character.avatar else None,
            profile=scene_character.character.profile,
            played=scene_character.played,
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/character/{character_id}", response_model=SceneCharacterResponse)
def update_scene_character(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    character_id: int,
    request: SceneCharacterUpdate,
):
    """
    更新场景人物关系

    ## 功能描述
    更新指定场景人物关系的信息，包括扮演状态（played字段）等。

    ## 请求参数
    - **character_id** (int): 场景人物关系ID，路径参数
    - **request** (SceneCharacterUpdate): 更新请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **played** (int): 是否为学员扮演（0：否；1：是），必填

    ## 响应
    - **200**: 更新成功
        - 返回类型: SceneCharacterResponse
        - 包含更新后的场景人物关系信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **404**: 场景人物关系不存在
    - **500**: 数据库操作失败

    ## 注意事项
    - 只能更新属于指定租户的场景人物关系
    - played 字段只能为 0（学员不扮演）或 1（学员扮演）
    """
    try:
        updated_character = scene_service.update_scene_character(
            db=db,
            scene_character_id=character_id,
            tenant_id=request.tenant_id,
            played=request.played,
        )
        
        if not updated_character:
            raise HTTPException(status_code=404, detail="场景人物关系不存在")
        
        return SceneCharacterResponse(
            id=updated_character.id,
            cid=updated_character.cid,
            name=updated_character.character.name,
            gender=updated_character.character.gender,
            avatar=get_oss_url(updated_character.character.avatar) if updated_character.character.avatar else None,
            profile=updated_character.character.profile,
            played=updated_character.played,
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新操作失败: {str(e)}")


@router.put("/characters/batch/order", response_model=SceneCharacterBatchOrderResponse)
def batch_update_scene_character_order(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: SceneCharacterBatchOrderRequest,
):
    """
    批量调整场景人物关系顺序

    ## 功能描述
    批量调整指定场景下的人物显示顺序，支持一次性调整多个人物的priority值。

    ## 请求参数
    - **request** (SceneCharacterBatchOrderRequest): 批量顺序调整请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **sid** (int): 场景ID，必填
        - **characters** (list[SceneCharacterOrderItem]): 人物顺序列表，必填
            - **id** (int): 关系ID
            - **priority** (int): 新的展示顺序（从小到大）

    ## 响应
    - **200**: 更新成功
        - 返回类型: SceneCharacterBatchOrderResponse
        - 包含成功更新的数量、总数量和更新后的人物列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **500**: 更新操作失败时会自动回滚

    ## 注意事项
    - 只有属于指定租户和场景的关系才会被更新
    - 不存在或不属于该租户和场景的关系ID会被忽略，不会报错
    - 返回的成功数量可能小于请求的总数量
    """
    try:
        character_orders = [
            {"id": item.id, "priority": item.priority} for item in request.characters
        ]
        
        updated_characters, success_count, total_count = (
            scene_service.batch_update_scene_character_order(
                db=db,
                tenant_id=request.tenant_id,
                scene_id=request.sid,
                character_orders=character_orders,
            )
        )

        # 转换为响应模型
        character_responses = []
        for sc in updated_characters:
            character_responses.append(
                SceneCharacterResponse(
                    id=sc.id,
                    cid=sc.cid,
                    name=sc.character.name,
                    gender=sc.character.gender,
                    avatar=get_oss_url(sc.character.avatar) if sc.character.avatar else None,
                    profile=sc.character.profile,
                    played=sc.played,
                )
            )

        return SceneCharacterBatchOrderResponse(
            success_count=success_count,
            total_count=total_count,
            updated_characters=character_responses,
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新操作失败: {str(e)}")


@router.delete("/characters/batch", response_model=SceneCharacterBatchDeleteResponse)
def batch_delete_scene_characters(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: SceneCharacterBatchDeleteRequest,
):
    """
    批量删除场景人物关系

    ## 功能描述
    批量删除指定场景下的人物关系，整个操作在事务中进行。

    ## 请求参数
    - **request** (SceneCharacterBatchDeleteRequest): 批量删除请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **sid** (int): 场景ID，必填
        - **character_ids** (list[int]): 关系ID列表，必填

    ## 响应
    - **200**: 删除成功
        - 返回类型: SceneCharacterBatchDeleteResponse
        - 包含成功删除的数量和删除的关系ID列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **500**: 删除操作失败或数据库事务失败时会自动回滚

    ## 注意事项
    - 只有属于指定租户和场景的关系才会被删除
    - 不存在或不属于该租户和场景的关系ID会被忽略，不会报错
    - 整个操作在数据库事务中进行，保证数据一致性
    - 如果过程中出现错误，所有操作会回滚
    """
    try:
        deleted_ids, success_count = scene_service.batch_delete_scene_characters(
            db=db,
            tenant_id=request.tenant_id,
            scene_id=request.sid,
            character_ids=request.character_ids,
        )

        return SceneCharacterBatchDeleteResponse(
            success_count=success_count,
            total_count=len(request.character_ids),
            deleted_character_ids=deleted_ids,
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除操作失败: {str(e)}")


# 场景指南相关接口

@router.get("/{scene_id}/guides", response_model=SceneGuideListResponse)
def get_scene_guides(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    scene_id: int,
    tenant_id: int = Query(..., description="租户ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, priority"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取场景指南列表

    ## 功能描述
    获取指定场景下的指南列表，支持分页和排序。

    ## 请求参数
    - **scene_id** (int): 场景ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数
    - **skip** (int, optional): 跳过的记录数，用于分页，默认为0
    - **limit** (int, optional): 返回的记录数限制，默认为100，范围1-100
    - **sort_by** (str, optional): 排序字段，可选值：id, priority
    - **sort_order** (str, optional): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回指南列表
        - 返回类型: SceneGuideListResponse
        - 包含指南的详细信息数组和总数

    ## 权限要求
    - 需要系统管理员权限
    """
    guides, total = scene_service.get_scene_guides(
        db=db,
        tenant_id=tenant_id,
        scene_id=scene_id,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    
    guide_responses = [
        SceneGuideResponse(
            id=guide.id,
            tenant_id=guide.tenant_id,
            sid=guide.sid,
            title=guide.title,
            details=guide.details,
            priority=guide.priority,
        )
        for guide in guides
    ]
    
    return SceneGuideListResponse(total=total, items=guide_responses)


@router.post("/guides", response_model=SceneGuideResponse)
def create_scene_guide(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    guide_in: SceneGuideCreate,
):
    """
    创建场景指南

    ## 功能描述
    为指定场景创建指南。

    ## 请求参数
    - **guide_in** (SceneGuideCreate): 指南创建信息，请求体
        - **tenant_id** (int): 租户ID
        - **sid** (int): 场景ID
        - **title** (str): 指南标题
        - **details** (str): 指南详情
        - **priority** (int): 展示顺序（从小到大）

    ## 响应
    - **200**: 成功创建指南
        - 返回类型: SceneGuideResponse
        - 包含新创建指南的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **400**: 场景不存在
    """
    try:
        guide = scene_service.create_scene_guide(db=db, guide_in=guide_in)
        return SceneGuideResponse(
            id=guide.id,
            tenant_id=guide.tenant_id,
            sid=guide.sid,
            title=guide.title,
            details=guide.details,
            priority=guide.priority,
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/guides/{guide_id}", response_model=SceneGuideResponse)
def get_scene_guide(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    guide_id: int,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    获取场景指南信息

    ## 功能描述
    根据指南ID获取场景指南的详细信息。

    ## 请求参数
    - **guide_id** (int): 指南ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数

    ## 响应
    - **200**: 成功返回指南信息
        - 返回类型: SceneGuideResponse
        - 包含指南的完整详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 指南不存在
    """
    guide = scene_service.get_scene_guide(db=db, guide_id=guide_id, tenant_id=tenant_id)
    if not guide:
        raise HTTPException(status_code=404, detail="指南不存在")
    
    return SceneGuideResponse(
        id=guide.id,
        tenant_id=guide.tenant_id,
        sid=guide.sid,
        title=guide.title,
        details=guide.details,
        priority=guide.priority,
    )


@router.put("/guides/{guide_id}", response_model=SceneGuideResponse)
def update_scene_guide(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    guide_id: int,
    guide_in: SceneGuideUpdate,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    更新场景指南

    ## 功能描述
    更新指定的场景指南信息。

    ## 请求参数
    - **guide_id** (int): 指南ID，路径参数
    - **guide_in** (SceneGuideUpdate): 指南更新信息，请求体
        - **title** (str, optional): 指南标题
        - **details** (str, optional): 指南详情
        - **priority** (int, optional): 展示顺序（从小到大）
    - **tenant_id** (int): 租户ID，必填查询参数

    ## 响应
    - **200**: 成功更新指南
        - 返回类型: SceneGuideResponse
        - 包含更新后的指南完整信息
    - **404**: 指南不存在

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    guide = scene_service.update_scene_guide(
        db=db, guide_id=guide_id, guide_in=guide_in, tenant_id=tenant_id
    )
    if not guide:
        raise HTTPException(status_code=404, detail="指南不存在")
    
    return SceneGuideResponse(
        id=guide.id,
        tenant_id=guide.tenant_id,
        sid=guide.sid,
        title=guide.title,
        details=guide.details,
        priority=guide.priority,
    )


@router.delete("/guides/{guide_id}", response_model=DeleteResponse)
def delete_scene_guide(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    guide_id: int,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    删除场景指南

    ## 功能描述
    删除指定的场景指南。

    ## 请求参数
    - **guide_id** (int): 指南ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数

    ## 响应
    - **200**: 成功删除指南，返回删除成功消息
    - **404**: 指南不存在

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 当指南不存在时，返回404错误
    """
    if not scene_service.delete_scene_guide(
        db=db, guide_id=guide_id, tenant_id=tenant_id
    ):
        raise HTTPException(status_code=404, detail="指南不存在")
    return {"message": "指南删除成功"}


@router.put("/guides/batch/order", response_model=SceneGuideBatchOrderResponse)
def batch_update_scene_guide_order(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: SceneGuideBatchOrderRequest,
):
    """
    批量调整场景指南顺序

    ## 功能描述
    批量调整指南显示顺序，支持一次性调整多个指南的priority值。每个指南的优先级将被独立更新。

    ## 请求参数
    - **request** (SceneGuideBatchOrderRequest): 批量顺序调整请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **guides** (list[SceneGuideOrderItem]): 指南顺序列表，必填
            - **id** (int): 指南ID
            - **priority** (int): 新的展示顺序（从小到大）

    ## 响应
    - **200**: 更新成功
        - 返回类型: SceneGuideBatchOrderResponse
        - **success_count** (int): 成功更新的指南数量
        - **total_count** (int): 请求更新的指南总数
        - **updated_guides** (list): 实际更新的指南列表（包含最新的priority值）

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **500**: 数据库操作失败

    ## 注意事项
    - 只有属于指定租户的指南才会被更新
    - 不存在或不属于该租户的指南ID会被忽略，不会报错
    - 指南可以属于不同场景，系统会按指南ID独立更新每个指南的优先级
    - 返回的 `success_count` 可能小于 `total_count`，表示部分指南未找到或无权限更新
    - 响应中的 `updated_guides` 列表包含实际被更新的指南信息
    """
    guide_orders = [
        {"id": item.id, "priority": item.priority} for item in request.guides
    ]
    
    updated_guides, success_count, total_count = (
        scene_service.batch_update_scene_guide_order(
            db=db,
            tenant_id=request.tenant_id,
            guide_orders=guide_orders,
        )
    )

    # 转换为响应模型
    guide_responses = []
    for guide in updated_guides:
        guide_responses.append(
            SceneGuideResponse(
                id=guide.id,
                tenant_id=guide.tenant_id,
                sid=guide.sid,
                title=guide.title,
                details=guide.details,
                priority=guide.priority,
            )
        )

    return SceneGuideBatchOrderResponse(
        success_count=success_count,
        total_count=total_count,
        updated_guides=guide_responses,
    )


# 场景剧本提示相关接口

@router.get("/{scene_id}/cues", response_model=list[CueResponse])
def get_scene_cues(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    scene_id: int,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    获取场景剧本提示列表

    ## 功能描述
    获取指定场景下的剧本提示列表，按priority从小到大排序。

    ## 请求参数
    - **scene_id** (int): 场景ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数

    ## 响应
    - **200**: 成功返回剧本提示列表
        - 按priority字段升序排列

    ## 权限要求
    - 需要系统管理员权限
    """
    cues = cue_service.get_cues(
        db=db, tenant_id=tenant_id, sid=scene_id, active=1
    )

    return [
        CueResponse(
            id=cue.id,
            tenant_id=cue.tenant_id,
            sid=cue.sid,
            cid=cue.cid,
            content=cue.content,
            serial=cue.serial,
            priority=cue.priority,
            active=cue.active,
        )
        for cue in cues
    ]


@router.post("/cues", response_model=CueResponse)
def create_scene_cue(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    cue_in: CueCreate,
):
    """
    创建场景剧本提示

    ## 功能描述
    为指定场景创建剧本提示。

    ## 请求参数
    - **cue_in** (CueCreate): 剧本提示创建信息，请求体
        - **tenant_id** (int): 租户ID
        - **sid** (int): 场景ID
        - **cid** (int): 人物ID（触发人物）
        - **content** (str): 触发内容
        - **serial** (int): 是否顺序发言（0：并行发言；1：顺序发言）
        - **priority** (int): 展示顺序（从小到大）

    ## 响应
    - **200**: 成功创建剧本提示
        - 返回类型: CueResponse

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **400**: 场景不存在或人物不存在
    """
    try:
        cue = cue_service.create_cue(db=db, cue_in=cue_in)
        
        return CueResponse(
            id=cue.id,
            tenant_id=cue.tenant_id,
            sid=cue.sid,
            cid=cue.cid,
            content=cue.content,
            serial=cue.serial,
            priority=cue.priority,
            active=cue.active,
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/cues/{cue_id}", response_model=CueResponse)
def update_scene_cue(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    cue_id: int,
    cue_in: CueUpdate,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    更新场景剧本提示

    ## 功能描述
    更新指定的剧本提示信息。

    ## 请求参数
    - **cue_id** (int): 剧本提示ID，路径参数
    - **cue_in** (CueUpdate): 剧本提示更新信息，请求体
    - **tenant_id** (int): 租户ID，必填查询参数

    ## 响应
    - **200**: 成功更新剧本提示
        - 返回类型: CueResponse
    - **404**: 剧本提示不存在

    ## 权限要求
    - 需要系统管理员权限
    """
    cue = cue_service.update_cue(
        db=db, cue_id=cue_id, cue_in=cue_in, tenant_id=tenant_id
    )
    if not cue:
        raise HTTPException(status_code=404, detail="剧本提示不存在")
    
    return CueResponse(
        id=cue.id,
        tenant_id=cue.tenant_id,
        sid=cue.sid,
        cid=cue.cid,
        content=cue.content,
        serial=cue.serial,
        priority=cue.priority,
        active=cue.active,
    )


@router.delete("/cues/{cue_id}", response_model=DeleteResponse)
def delete_scene_cue(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    cue_id: int,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    删除场景剧本提示

    ## 功能描述
    删除指定的剧本提示，会先删除关联的台词，再删除剧本提示。

    ## 请求参数
    - **cue_id** (int): 剧本提示ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数

    ## 响应
    - **200**: 成功删除剧本提示，返回删除成功消息
    - **404**: 剧本提示不存在

    ## 权限要求
    - 需要系统管理员权限

    ## 注意事项
    - 删除操作会先删除关联的台词，再删除剧本提示
    - 操作在同一个事务中进行，保证数据一致性
    """
    if not cue_service.delete_cue(
        db=db, cue_id=cue_id, tenant_id=tenant_id
    ):
        raise HTTPException(status_code=404, detail="剧本提示不存在")
    return {"message": "剧本提示删除成功"}


@router.put("/cues/batch/order", response_model=CueBatchOrderResponse)
def batch_update_scene_cue_order(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: CueBatchOrderRequest,
):
    """
    批量调整场景剧本提示顺序

    ## 功能描述
    批量调整剧本提示显示顺序，支持一次性调整多个剧本提示的priority值。

    ## 请求参数
    - **request** (CueBatchOrderRequest): 批量顺序调整请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **sid** (int): 场景ID，必填
        - **cues** (list[CueOrderItem]): 剧本提示顺序列表，必填
            - **id** (int): 剧本提示ID
            - **priority** (int): 新的展示顺序（从小到大）

    ## 响应
    - **200**: 更新成功
        - 返回类型: CueBatchOrderResponse

    ## 权限要求
    - 需要系统管理员权限

    ## 注意事项
    - 只有属于指定租户和场景的剧本提示才会被更新
    - 不存在或不属于该租户和场景的剧本提示ID会被忽略
    """
    try:
        cue_orders = [
            {"id": item.id, "priority": item.priority} for item in request.cues
        ]
        
        updated_cues, success_count, total_count = (
            cue_service.batch_update_cue_order(
                db=db,
                tenant_id=request.tenant_id,
                scene_id=request.sid,
                cue_orders=cue_orders,
            )
        )

        # 转换为响应模型
        cue_responses = []
        for cue in updated_cues:
            cue_responses.append(
                CueResponse(
                    id=cue.id,
                    tenant_id=cue.tenant_id,
                    sid=cue.sid,
                    cid=cue.cid,
                    content=cue.content,
                    serial=cue.serial,
                    priority=cue.priority,
                    active=cue.active,
                )
            )

        return CueBatchOrderResponse(
            success_count=success_count,
            total_count=total_count,
            updated_cues=cue_responses,
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新操作失败: {str(e)}")


# 场景台词相关接口

@router.get("/cues/{cue_id}/lines", response_model=list[LineResponse])
def get_cue_lines(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    cue_id: int,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    获取剧本提示的台词列表

    ## 功能描述
    获取指定剧本提示下的台词列表，按priority从小到大排序。

    ## 请求参数
    - **cue_id** (int): 剧本提示ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数

    ## 响应
    - **200**: 成功返回台词列表
        - 按priority字段升序排列

    ## 权限要求
    - 需要系统管理员权限
    """
    lines = line_service.get_lines(
        db=db, tenant_id=tenant_id, cueid=cue_id
    )

    return [
        LineResponse(
            id=line.id,
            tenant_id=line.tenant_id,
            cueid=line.cueid,
            cid=line.cid,
            pv_topic=line.pv_topic,
            pv_ability=line.pv_ability,
            pv_restriction=line.pv_restriction,
            priority=line.priority,
            active=line.active,
        )
        for line in lines
    ]


@router.post("/lines", response_model=LineResponse)
def create_cue_line(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    line_in: LineCreate,
):
    """
    创建台词

    ## 功能描述
    为指定剧本提示创建台词。

    ## 请求参数
    - **line_in** (LineCreate): 台词创建信息，请求体
        - **tenant_id** (int): 租户ID
        - **cueid** (int): 剧本提示ID
        - **cid** (int): 人物ID（发言人物）
        - **pv_topic** (str): 发言主题（提示词变量）
        - **pv_ability** (str): 人物能力（提示词变量）
        - **pv_restriction** (str): 人物限制（提示词变量）
        - **priority** (int): 展示顺序（从小到大）

    ## 响应
    - **200**: 成功创建台词
        - 返回类型: LineResponse

    ## 权限要求
    - 需要系统管理员权限
    """
    try:
        line = line_service.create_line(db=db, line_in=line_in)
        
        return LineResponse(
            id=line.id,
            tenant_id=line.tenant_id,
            cueid=line.cueid,
            cid=line.cid,
            pv_topic=line.pv_topic,
            pv_ability=line.pv_ability,
            pv_restriction=line.pv_restriction,
            priority=line.priority,
            active=line.active,
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/lines/{line_id}", response_model=LineResponse)
def update_cue_line(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    line_id: int,
    line_in: LineUpdate,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    更新台词

    ## 功能描述
    更新指定的台词信息。

    ## 请求参数
    - **line_id** (int): 台词ID，路径参数
    - **line_in** (LineUpdate): 台词更新信息，请求体
    - **tenant_id** (int): 租户ID，必填查询参数

    ## 响应
    - **200**: 成功更新台词
        - 返回类型: LineResponse
    - **404**: 台词不存在

    ## 权限要求
    - 需要系统管理员权限
    """
    line = line_service.update_line(
        db=db, line_id=line_id, line_in=line_in, tenant_id=tenant_id
    )
    if not line:
        raise HTTPException(status_code=404, detail="台词不存在")
    
    return LineResponse(
        id=line.id,
        tenant_id=line.tenant_id,
        cueid=line.cueid,
        cid=line.cid,
        pv_topic=line.pv_topic,
        pv_ability=line.pv_ability,
        pv_restriction=line.pv_restriction,
        priority=line.priority,
        active=line.active,
    )


@router.delete("/lines/{line_id}", response_model=DeleteResponse)
def delete_cue_line(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    line_id: int,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    删除台词

    ## 功能描述
    删除指定的台词。

    ## 请求参数
    - **line_id** (int): 台词ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数

    ## 响应
    - **200**: 成功删除台词，返回删除成功消息
    - **404**: 台词不存在

    ## 权限要求
    - 需要系统管理员权限
    """
    if not line_service.delete_line(
        db=db, line_id=line_id, tenant_id=tenant_id
    ):
        raise HTTPException(status_code=404, detail="台词不存在")
    return {"message": "台词删除成功"}


@router.put("/lines/batch/order", response_model=LineBatchOrderResponse)
def batch_update_line_order(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: LineBatchOrderRequest,
):
    """
    批量调整台词顺序

    ## 功能描述
    批量调整台词显示顺序，支持一次性调整多个台词的priority值。

    ## 请求参数
    - **request** (LineBatchOrderRequest): 批量顺序调整请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **lines** (list[LineOrderItem]): 台词顺序列表，必填
            - **id** (int): 台词ID
            - **priority** (int): 新的展示顺序（从小到大）

    ## 响应
    - **200**: 更新成功
        - 返回类型: LineBatchOrderResponse

    ## 权限要求
    - 需要系统管理员权限

    ## 注意事项
    - 只有属于指定租户的台词才会被更新
    - 不存在或不属于该租户的台词ID会被忽略
    """
    try:
        line_orders = [
            {"id": item.id, "priority": item.priority} for item in request.lines
        ]
        
        updated_lines, success_count, total_count = (
            line_service.batch_update_line_order(
                db=db,
                tenant_id=request.tenant_id,
                line_orders=line_orders,
            )
        )

        # 转换为响应模型
        line_responses = []
        for line in updated_lines:
            line_responses.append(
                LineResponse(
                    id=line.id,
                    tenant_id=line.tenant_id,
                    cueid=line.cueid,
                    cid=line.cid,
                    pv_topic=line.pv_topic,
                    pv_ability=line.pv_ability,
                    pv_restriction=line.pv_restriction,
                    priority=line.priority,
                    active=line.active,
                )
            )

        return LineBatchOrderResponse(
            success_count=success_count,
            total_count=total_count,
            updated_lines=line_responses,
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新操作失败: {str(e)}")


@router.put("/lines/batch/move", response_model=LineBatchMoveResponse)
def batch_move_lines(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: LineBatchMoveRequest,
):
    """
    批量移动台词到指定剧本提示

    ## 功能描述
    将多个台词批量移动到指定的目标剧本提示中，整个操作在事务中进行。

    ## 请求参数
    - **request** (LineBatchMoveRequest): 批量移动请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **target_cueid** (int): 目标剧本提示ID，必填
        - **line_ids** (list[int]): 要移动的台词ID列表，必填

    ## 响应
    - **200**: 移动成功
        - 返回类型: LineBatchMoveResponse
        - 包含成功移动的数量和移动的台词ID列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **404**: 目标剧本提示不存在或不属于指定租户
    - **500**: 移动操作失败或数据库事务失败时会自动回滚

    ## 注意事项
    - 只有属于指定租户的台词才会被移动
    - 目标剧本提示必须存在且属于同一租户
    - 不存在或不属于指定租户的台词ID会被忽略，不会报错
    - 整个操作在数据库事务中进行，保证数据一致性
    - 如果过程中出现错误，所有操作会回滚
    """
    try:
        moved_line_ids, success_count = line_service.batch_move_lines(
            db=db,
            tenant_id=request.tenant_id,
            target_cueid=request.target_cueid,
            line_ids=request.line_ids,
        )

        return LineBatchMoveResponse(
            success_count=success_count,
            total_count=len(request.line_ids),
            moved_line_ids=moved_line_ids,
            target_cueid=request.target_cueid,
        )
    
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"移动操作失败: {str(e)}")


@router.put("/{scene_id}/script", response_model=SceneResponse)
def save_scene_script(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    scene_id: int,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    生成并保存场景剧情脚本

    ## 功能描述
    遍历指定场景下的所有剧本提示(cue)，为每个cue生成一行脚本内容并更新到场景的pv_scripts字段。

    ## 脚本格式规范
    
    ### 基本格式
    ```
    {序号}.如发言内容是："{cue触发内容}"时，返回[{人物映射列表}]
    ```
    
    ### 格式说明
    - **序号**: 从1开始的连续数字，表示cue的处理顺序
    - **cue触发内容**: 来自TntCue表的content字段，即触发该cue的发言内容
    - **人物映射列表**: JSON数组格式，包含该cue下所有激活line的人物名称和line ID映射
    
    ### 人物映射格式
    - **单个line**: `[{"人物名称":"line_id"}]`
    - **多个line**: `[{"人物1":"line_id1"},{"人物2":"line_id2"},{"人物3":"line_id3"}]`
    
    ### 示例
    ```
    1.如发言内容是："希望落实外部技术合作"时，返回[{"周小兰":"14"}]
    2.如发言内容是："要求给出工作任务时间安排"时，返回[{"钱伟":"15"},{"李静":"16"},{"孙涛":"17"},{"周小兰":"18"}]
    ```

    ## 请求参数
    - **scene_id** (int): 场景ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数

    ## 响应
    - **200**: 成功生成并保存脚本
        - 返回类型: SceneResponse
        - 包含更新后的场景信息，其中pv_scripts字段包含生成的脚本内容
    - **404**: 场景不存在

    ## 权限要求
    - 需要系统管理员权限

    ## 处理规则
    - 只处理激活状态(active=1)的剧本提示(cue)和台词(line)
    - 按cue的priority字段升序排序处理
    - 按line的priority字段升序排序生成人物映射
    - 生成的脚本会完全覆盖原有的pv_scripts内容
    - 如果cue下没有激活的line，则跳过该cue不生成脚本行
    - 如果场景下没有符合条件的cue，会生成空脚本内容
    """
    try:
        updated_scene = scene_service.generate_and_save_scene_script(
            db=db, scene_id=scene_id, tenant_id=tenant_id
        )
        
        if not updated_scene:
            raise HTTPException(status_code=404, detail="场景不存在")
        
        # 构造响应数据
        exercise = updated_scene.exercise
        return SceneResponse(
            id=updated_scene.id,
            tenant_id=updated_scene.tenant_id,
            eid=updated_scene.eid,
            title=exercise.title,
            type=exercise.type,
            intro=exercise.intro,
            duration=exercise.duration,
            version=exercise.version,
            pic=get_oss_url(exercise.pic) if exercise.pic else None,
            bgtext=exercise.bgtext,
            notes=exercise.notes,
            published=exercise.published,
            ctime=exercise.ctime,
            active=exercise.active,
            pv_scripts=updated_scene.pv_scripts,
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成脚本失败: {str(e)}")
