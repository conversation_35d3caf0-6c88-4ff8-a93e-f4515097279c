from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.scene_speech import (
    SceneSpeechCreate,
    SceneSpeechResponse,
    SceneSpeechUpdate,
)
from app.services import scene_speech as scene_speech_service
from app.services.sys.auth import get_current_sys_admin

router = APIRouter()


@router.get("/", response_model=List[SceneSpeechResponse])
def get_scene_speeches(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
):
    """
    获取场景语音列表

    ## 功能描述
    获取指定租户下的所有场景语音信息，支持分页查询。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数
    - **skip** (int, optional): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int, optional): 返回的记录数限制，默认为100，范围1-100

    ## 响应
    - **200**: 成功返回场景语音列表
        - 返回类型: List[SceneSpeechResponse]
        - 包含场景语音的详细信息数组

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    scene_speeches = scene_speech_service.get_scene_speeches(
        db=db,
        tenant_id=tenant_id,
        skip=skip,
        limit=limit,
    )
    return scene_speeches


@router.post("/", response_model=SceneSpeechResponse)
def create_scene_speech(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    scene_speech_in: SceneSpeechCreate,
):
    """
    创建场景语音

    ## 功能描述
    创建一个新的场景语音记录。

    ## 请求参数
    - **scene_speech_in** (SceneSpeechCreate): 场景语音创建信息，请求体
        - 包含语音内容、场景信息、所属租户等信息

    ## 响应
    - **200**: 成功创建场景语音
        - 返回类型: SceneSpeechResponse
        - 包含新创建场景语音的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    - 数据库约束违反时返回400错误
    """
    scene_speech = scene_speech_service.create_scene_speech(
        db=db, scene_speech_in=scene_speech_in
    )
    return scene_speech


@router.get("/{scene_speech_id}", response_model=SceneSpeechResponse)
def get_scene_speech(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    scene_speech_id: int,
):
    """
    获取场景语音信息

    ## 功能描述
    根据场景语音ID获取单个场景语音的详细信息。

    ## 请求参数
    - **scene_speech_id** (int): 场景语音ID，路径参数

    ## 响应
    - **200**: 成功返回场景语音信息
        - 返回类型: SceneSpeechResponse
        - 包含场景语音的详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 场景语音不存在
    """
    scene_speech = scene_speech_service.get_scene_speech(
        db=db, scene_speech_id=scene_speech_id
    )
    if not scene_speech:
        raise HTTPException(status_code=404, detail="场景语音不存在")
    return scene_speech


@router.put("/{scene_speech_id}", response_model=SceneSpeechResponse)
def update_scene_speech(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    scene_speech_id: int,
    scene_speech_in: SceneSpeechUpdate,
):
    """
    更新场景语音信息

    ## 功能描述
    根据场景语音ID更新场景语音的信息。

    ## 请求参数
    - **scene_speech_id** (int): 场景语音ID，路径参数
    - **scene_speech_in** (SceneSpeechUpdate): 场景语音更新信息，请求体
        - 包含需要更新的字段

    ## 响应
    - **200**: 成功更新场景语音信息
        - 返回类型: SceneSpeechResponse
        - 包含更新后的场景语音完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 场景语音不存在
    - 参数验证错误时返回422错误
    """
    scene_speech = scene_speech_service.update_scene_speech(
        db=db, scene_speech_id=scene_speech_id, scene_speech_in=scene_speech_in
    )
    if not scene_speech:
        raise HTTPException(status_code=404, detail="场景语音不存在")
    return scene_speech
