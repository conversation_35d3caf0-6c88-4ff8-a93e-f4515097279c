from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.student import (
    StudentCreate,
    StudentListResponse,
    StudentResponse,
    StudentUpdate,
)
from app.services import student as student_service
from app.services.sys.auth import get_current_sys_admin

router = APIRouter()


@router.get("", response_model=StudentListResponse)
def get_students(
    tenant_id: int,
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active: Optional[int] = Query(None, description="按用户状态筛选，可选值为0(禁用)或1(启用)"),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    start_time: Optional[datetime] = Query(
        None, description="创建时间的开始时间，格式为ISO 8601"
    ),
    end_time: Optional[datetime] = Query(
        None, description="创建时间的结束时间，格式为ISO 8601"
    ),
    name: Optional[str] = Query(None, description="按姓名搜索，支持模糊匹配"),
    gender: Optional[int] = Query(None, description="按性别筛选，可选值：0(未知)、1(男)、2(女)"),
    notes: Optional[str] = Query(None, description="按备注搜索，支持模糊匹配"),
    current_admin: dict = Depends(get_current_sys_admin),
):
    """
    获取学员列表

    ## 功能描述
    根据租户ID获取该租户下的学员列表，支持分页查询、按用户状态筛选、排序、按创建时间区间筛选和按学员信息搜索。

    ## 请求参数
    - **tenant_id** (int): 租户ID，路径参数
    - **skip** (int, optional): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int, optional): 返回的记录数限制，默认为100，范围1-100
    - **active** (int, optional): 按用户状态筛选，可选值为0(禁用)或1(启用)，不传则返回所有状态
    - **sort_by** (str, optional): 排序字段，可选值：id, ctime
    - **sort_order** (str, optional): 排序方式，可选值：asc, desc
    - **start_time** (datetime, optional): 创建时间的开始时间，格式为ISO 8601
    - **end_time** (datetime, optional): 创建时间的结束时间，格式为ISO 8601
    - **name** (str, optional): 按姓名搜索，支持模糊匹配
    - **gender** (int, optional): 按性别筛选，可选值：0(未知)、1(男)、2(女)
    - **notes** (str, optional): 按备注搜索，支持模糊匹配

    ## 响应
    - **200**: 成功返回学员列表
        - 返回类型: StudentListResponse
        - 包含学员的详细信息数组和总数

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    students, total = student_service.get_students(
        db, tenant_id, skip, limit, active, sort_by, sort_order, start_time, end_time,
        name, gender, notes
    )
    student_resps = [
        StudentResponse(
            id=student.id,
            tenant_id=student.tenant_id,
            uid=student.uid,
            username=student.user.username,
            name=student.name,
            gender=student.gender,
            notes=student.notes,
            ctime=student.user.ctime,
            active=student.user.active,
        )
        for student in students
    ]
    return StudentListResponse(total=total, items=student_resps)


@router.post("", response_model=StudentResponse)
def create_student(
    student_in: StudentCreate,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
):
    """
    创建学员

    ## 功能描述
    在指定租户下创建新的学员记录。

    ## 请求参数
    - **tenant_id** (int): 租户ID，路径参数
    - **student_in** (StudentCreate): 学员创建信息，请求体
        - 包含学员的基本信息和用户认证信息

    ## 响应
    - **200**: 成功创建学员
        - 返回类型: StudentResponse
        - 包含新创建学员的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 租户ID不匹配
    - 参数验证错误时返回422错误
    """
    db_student = student_service.create_student(db, student_in)
    return StudentResponse(
        id=db_student.id,
        tenant_id=db_student.tenant_id,
        uid=db_student.uid,
        username=db_student.user.username,
        name=db_student.name,
        gender=db_student.gender,
        notes=db_student.notes,
        ctime=db_student.user.ctime,
        active=db_student.user.active,
    )


@router.get("/{student_id}", response_model=StudentResponse)
def get_student(
    student_id: int,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
):
    """
    获取学员信息

    ## 功能描述
    获取指定租户下的特定学员的详细信息。

    ## 请求参数
    - **student_id** (int): 学员ID，路径参数

    ## 响应
    - **200**: 成功返回学员信息
        - 返回类型: StudentResponse
        - 包含学员的完整详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 学员不存在
    """
    db_student = student_service.get_student(db, student_id)
    if not db_student:
        raise HTTPException(status_code=404, detail="学员不存在")
    return StudentResponse(
        id=db_student.id,
        tenant_id=db_student.tenant_id,
        uid=db_student.uid,
        username=db_student.user.username,
        name=db_student.name,
        gender=db_student.gender,
        notes=db_student.notes,
        ctime=db_student.user.ctime,
        active=db_student.user.active,
    )


@router.put("/{student_id}", response_model=StudentResponse)
def update_student(
    student_id: int,
    student_in: StudentUpdate,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(get_current_sys_admin),
):
    """
    更新学员信息

    ## 功能描述
    更新指定租户下的特定学员的信息。

    ## 请求参数
    - **student_id** (int): 学员ID，路径参数
    - **student_in** (StudentUpdate): 学员更新信息，请求体
        - 包含需要更新的学员字段信息

    ## 响应
    - **200**: 成功更新学员信息
        - 返回类型: StudentResponse
        - 包含更新后的学员完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 学员不存在
    - 参数验证错误时返回422错误
    """
    updated_student = student_service.update_student(db, student_id, student_in)
    if not updated_student:
        raise HTTPException(status_code=404, detail="学员不存在")
    return StudentResponse(
        id=updated_student.id,
        tenant_id=updated_student.tenant_id,
        uid=updated_student.uid,
        username=updated_student.user.username,
        name=updated_student.name,
        gender=updated_student.gender,
        notes=updated_student.notes,
        ctime=updated_student.user.ctime,
        active=updated_student.user.active,
    )
