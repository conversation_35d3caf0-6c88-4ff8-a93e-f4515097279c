from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.base import DeleteResponse
from app.schemas.subject import SubjectCreate, SubjectResponse, SubjectUpdate, SubjectListResponse
from app.services import subject as subject_service
from app.services.sys.auth import get_current_sys_admin

router = APIRouter()


@router.get("", response_model=SubjectListResponse)
def get_subjects(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    name: Optional[str] = Query(None, description="按名称搜索"),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取主题列表

    ## 功能描述
    获取指定租户下的主题列表，支持分页查询、按名称搜索和排序。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数
    - **skip** (int, optional): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int, optional): 返回的记录数限制，默认为100，范围1-100
    - **name** (str, optional): 按名称搜索，支持模糊匹配
    - **sort_by** (str, optional): 排序字段，可选值：id, ctime
    - **sort_order** (str, optional): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回主题列表
        - 返回类型: SubjectListResponse
        - 包含总数和主题列表的分页响应数据

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    subjects, total = subject_service.get_subjects(
        db=db,
        tenant_id=tenant_id,
        skip=skip,
        limit=limit,
        name=name,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    return {"total": total, "items": subjects}


@router.post("", response_model=SubjectResponse)
def create_subject(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    subject_in: SubjectCreate,
):
    """
    创建主题

    ## 功能描述
    创建新的主题记录。

    ## 请求参数
    - **subject_in** (SubjectCreate): 主题创建信息，请求体
        - 包含主题的名称、描述、租户ID等基本信息

    ## 响应
    - **200**: 成功创建主题
        - 返回类型: SubjectResponse
        - 包含新创建主题的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    - 数据库约束违反时返回400错误
    """
    subject = subject_service.create_subject(db=db, subject_in=subject_in)
    return subject


@router.get("/{subject_id}", response_model=SubjectResponse)
def get_subject(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    subject_id: int,
):
    """
    获取主题信息

    ## 功能描述
    根据主题ID获取主题的详细信息。

    ## 请求参数
    - **subject_id** (int): 主题ID，路径参数

    ## 响应
    - **200**: 成功返回主题信息
        - 返回类型: SubjectResponse
        - 包含主题的完整详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 主题不存在
    """
    subject = subject_service.get_subject(db=db, subject_id=subject_id)
    if not subject:
        raise HTTPException(status_code=404, detail="主题不存在")
    return subject


@router.put("/{subject_id}", response_model=SubjectResponse)
def update_subject(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    subject_id: int,
    subject_in: SubjectUpdate,
):
    """
    更新主题信息

    ## 功能描述
    更新指定主题的信息。

    ## 请求参数
    - **subject_id** (int): 主题ID，路径参数
    - **subject_in** (SubjectUpdate): 主题更新信息，请求体
        - 包含需要更新的主题字段信息

    ## 响应
    - **200**: 成功更新主题信息
        - 返回类型: SubjectResponse
        - 包含更新后的主题完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 主题不存在
    - 参数验证错误时返回422错误
    """
    subject = subject_service.update_subject(
        db=db, subject_id=subject_id, subject_in=subject_in
    )
    if not subject:
        raise HTTPException(status_code=404, detail="主题不存在")
    return subject


@router.delete("/{subject_id}", response_model=DeleteResponse)
def delete_subject(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    subject_id: int,
):
    """
    删除主题

    ## 功能描述
    根据主题ID删除指定的主题。

    ## 请求参数
    - **subject_id** (int): 主题ID，路径参数

    ## 响应
    - **200**: 删除成功
        - 返回类型: DeleteResponse
        - 包含删除成功的状态信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 主题不存在
    """
    if not subject_service.delete_subject(db=db, subject_id=subject_id):
        raise HTTPException(status_code=404, detail="主题不存在")
    return {"message": "删除成功"}
