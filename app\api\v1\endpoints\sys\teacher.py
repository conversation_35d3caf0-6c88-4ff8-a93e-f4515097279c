from typing import Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.constants import OSS_UPLOAD_PATH_TEACHER_AVATAR
from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.teacher import TeacherCreate, TeacherResponse, TeacherUpdate, TeacherListResponse, AvatarUploadURL, AvatarDeleteRequest
from app.services import teacher as teacher_service
from app.services.sys.auth import get_current_sys_admin
from app.utils.oss import get_oss_url, generate_oss_image_upload_url, get_file_extension, delete_oss_file, extract_object_name_from_url

router = APIRouter()


@router.get("", response_model=TeacherListResponse)
def get_teachers(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    start_time: Optional[datetime] = Query(
        None, description="创建时间的开始时间，格式为ISO 8601"
    ),
    end_time: Optional[datetime] = Query(
        None, description="创建时间的结束时间，格式为ISO 8601"
    ),
    name: Optional[str] = Query(None, description="按姓名搜索"),
    gender: Optional[int] = Query(None, description="按性别筛选"),
    intro: Optional[str] = Query(None, description="按简介搜索"),
    notes: Optional[str] = Query(None, description="按备注搜索"),
):
    """
    获取老师列表

    ## 功能描述
    获取指定租户下的老师列表，支持分页查询、排序和按创建时间区间筛选。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数
    - **skip** (int, optional): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int, optional): 返回的记录数限制，默认为100，范围1-100
    - **sort_by** (str, optional): 排序字段，可选值：id, ctime
    - **sort_order** (str, optional): 排序方式，可选值：asc, desc
    - **start_time** (datetime, optional): 创建时间的开始时间，格式为ISO 8601
    - **end_time** (datetime, optional): 创建时间的结束时间，格式为ISO 8601
    - **name** (str, optional): 按姓名搜索，支持模糊匹配
    - **gender** (int, optional): 按性别筛选，可选值为0(未知)、1(男)或2(女)
    - **intro** (str, optional): 按简介搜索，支持模糊匹配
    - **notes** (str, optional): 按备注搜索，支持模糊匹配

    ## 响应
    - **200**: 成功返回老师列表
        - 返回类型: TeacherListResponse
        - 包含老师信息列表和总数

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    teachers, total = teacher_service.get_teachers(
        db=db,
        tenant_id=tenant_id,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        sort_order=sort_order,
        start_time=start_time,
        end_time=end_time,
        name=name,
        gender=gender,
        intro=intro,
        notes=notes,
    )
    
    # 处理头像字段，返回signed URL
    teacher_items = []
    for teacher in teachers:
        avatar_url = get_oss_url(teacher.avatar) if teacher.avatar else ""
        teacher_response = TeacherResponse(
            id=teacher.id,
            tenant_id=teacher.tenant_id,
            name=teacher.name,
            gender=teacher.gender,
            avatar=avatar_url,
            intro=teacher.intro,
            notes=teacher.notes,
            ctime=teacher.ctime,
        )
        teacher_items.append(teacher_response)
    
    return TeacherListResponse(total=total, items=teacher_items)


@router.post("", response_model=TeacherResponse)
def create_teacher(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    teacher_in: TeacherCreate,
):
    """
    创建老师

    ## 功能描述
    创建新的老师记录。

    ## 请求参数
    - **teacher_in** (TeacherCreate): 老师创建信息，请求体
        - 包含老师的姓名、专业、联系方式、租户ID等基本信息

    ## 响应
    - **200**: 成功创建老师
        - 返回类型: TeacherResponse
        - 包含新创建老师的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    - 数据库约束违反时返回400错误
    """
    teacher = teacher_service.create_teacher(db=db, teacher_in=teacher_in)
    if teacher.avatar:
        teacher.avatar = get_oss_url(teacher.avatar)
    return teacher


@router.get("/{teacher_id}", response_model=TeacherResponse)
def get_teacher(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    teacher_id: int,
):
    """
    获取老师信息

    ## 功能描述
    根据老师ID获取老师的详细信息。

    ## 请求参数
    - **teacher_id** (int): 老师ID，路径参数

    ## 响应
    - **200**: 成功返回老师信息
        - 返回类型: TeacherResponse
        - 包含老师的完整详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 老师不存在
    """
    teacher = teacher_service.get_teacher(db=db, teacher_id=teacher_id)
    if not teacher:
        raise HTTPException(status_code=404, detail="老师不存在")
    if teacher.avatar:
        teacher.avatar = get_oss_url(teacher.avatar)
    return teacher


@router.put("/{teacher_id}", response_model=TeacherResponse)
def update_teacher(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    teacher_id: int,
    teacher_in: TeacherUpdate,
):
    """
    更新老师信息

    ## 功能描述
    更新指定老师的信息。

    ## 请求参数
    - **teacher_id** (int): 老师ID，路径参数
    - **teacher_in** (TeacherUpdate): 老师更新信息，请求体
        - 包含需要更新的老师字段信息

    ## 响应
    - **200**: 成功更新老师信息
        - 返回类型: TeacherResponse
        - 包含更新后的老师完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 老师不存在
    - 参数验证错误时返回422错误
    """
    teacher = teacher_service.update_teacher(
        db=db, teacher_id=teacher_id, teacher_in=teacher_in
    )
    if not teacher:
        raise HTTPException(status_code=404, detail="老师不存在")
    if teacher and teacher.avatar:
        teacher.avatar = get_oss_url(teacher.avatar)
    return teacher


@router.delete("/{teacher_id}")
def delete_teacher(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    teacher_id: int,
):
    """
    删除老师

    ## 功能描述
    删除指定的老师记录。

    ## 请求参数
    - **teacher_id** (int): 老师ID，路径参数

    ## 响应
    - **200**: 成功删除老师
        - 返回删除成功的确认信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 老师不存在
    """
    success = teacher_service.delete_teacher(db=db, teacher_id=teacher_id)
    if not success:
        raise HTTPException(status_code=404, detail="老师不存在")
    
    return {"message": "老师删除成功", "teacher_id": teacher_id}


@router.get("/avatar/upload-url", response_model=AvatarUploadURL)
def get_avatar_upload_url(
    *,
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    file_name: str = Query(..., description="文件名"),
):
    """
    获取老师头像上传的鉴权URL

    ## 功能描述
    获取老师头像上传的鉴权URL，前端可以直接使用该URL上传头像文件。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数
    - **file_name** (str): 文件名，必填查询参数

    ## 响应
    - **200**: 获取成功
        - 返回类型: AvatarUploadURL
        - 包含上传URL、文件路径、文件访问URL和过期时间

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 生成上传URL失败
    """
    try:
        # 获取文件扩展名
        file_extension = get_file_extension(file_name)
        
        # 生成上传URL
        upload_info = generate_oss_image_upload_url(
            upload_path=OSS_UPLOAD_PATH_TEACHER_AVATAR,
            file_extension=file_extension
        )
        
        return AvatarUploadURL(
            upload_url=upload_info["upload_url"],
            file_path=upload_info["file_path"],
            file_url=upload_info["file_url"],
            expires=upload_info["expires"]
        )
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"生成头像上传URL失败: {str(e)}"
        )


@router.delete("/avatar")
def delete_avatar_file(
    *,
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: AvatarDeleteRequest,
):
    """
    删除老师头像文件

    ## 功能描述
    删除指定的老师头像OSS文件，仅删除OSS文件，不修改数据库中的老师记录。

    ## 请求参数
    - **request** (AvatarDeleteRequest): 删除请求数据，请求体
        - **file_path** (str): 要删除的文件OSS signed URL
            - 例如：https://bucket.oss-region.aliyuncs.com/teacher/avatar/uuid.jpg?Expires=xxx&...
            - 不支持相对路径

    ## 响应
    - **200**: 删除成功
        - 返回删除操作的结果信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 文件URL无效或删除失败
    - **404**: 文件不存在
    """
    try:
        # 从OSS signed URL中提取相对路径
        object_name = extract_object_name_from_url(request.file_path)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    
    # 调用OSS删除函数
    delete_result = delete_oss_file(object_name)
    
    if not delete_result["success"]:
        # 根据错误消息决定HTTP状态码
        if "文件不存在" in delete_result["message"]:
            raise HTTPException(status_code=404, detail=delete_result["message"])
        else:
            raise HTTPException(status_code=400, detail=delete_result["message"])
    
    return {
        "message": delete_result["message"],
        "file_path": delete_result.get("file_path")
    }
