from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.base import DeleteResponse
from app.schemas.sys.tenant import (
    TenantCreate,
    TenantListResponse,
    TenantResponse,
    TenantUpdate,
)
from app.services.sys.auth import get_current_sys_admin
from app.services.sys.tenant import (
    create_tenant,
    delete_tenant,
    get_tenant,
    get_tenants,
    update_tenant,
)

router = APIRouter()


@router.post("", response_model=TenantResponse)
def create_tenant_api(
    tenant: TenantCreate,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    创建租户

    ## 功能描述
    创建新的租户账户。

    ## 请求参数
    - **tenant** (TenantCreate): 租户创建信息，请求体
        - 包含租户名称、描述、联系信息等基本信息

    ## 响应
    - **200**: 成功创建租户
        - 返回类型: TenantResponse
        - 包含新创建租户的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    db_tenant = create_tenant(db, tenant)
    return db_tenant


@router.get("/{tenant_id}", response_model=TenantResponse)
def get_tenant_api(
    tenant_id: int,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    获取租户

    ## 功能描述
    根据租户ID获取租户的详细信息。

    ## 请求参数
    - **tenant_id** (int): 租户ID，路径参数

    ## 响应
    - **200**: 成功返回租户信息
        - 返回类型: TenantResponse
        - 包含租户的详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 租户不存在
    """
    db_tenant = get_tenant(db, tenant_id)
    if not db_tenant:
        raise HTTPException(status_code=404, detail="租户不存在")
    return db_tenant


@router.get("", response_model=TenantListResponse)
def get_tenants_api(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    keyword: Optional[str] = Query(None),
    active: Optional[int] = Query(None, description="按租户状态筛选，可选值为0(禁用)或1(启用)"),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    start_time: Optional[datetime] = Query(
        None, description="创建时间的开始时间，格式为ISO 8601"
    ),
    end_time: Optional[datetime] = Query(
        None, description="创建时间的结束时间，格式为ISO 8601"
    ),
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    获取租户列表

    ## 功能描述
    获取租户列表，支持分页查询、关键字搜索、按状态筛选、排序和按创建时间区间筛选。

    ## 请求参数
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 返回的记录数限制，默认为100，范围1-100
    - **keyword** (str): 可选，搜索关键字，可用于按租户名称搜索
    - **active** (int): 可选，按租户状态筛选，可选值为0(禁用)或1(启用)，不传则返回所有状态
    - **sort_by** (str): 可选，排序字段，可选值：id, ctime
    - **sort_order** (str): 可选，排序方式，可选值：asc, desc
    - **start_time** (datetime): 可选，创建时间的开始时间，格式为ISO 8601
    - **end_time** (datetime): 可选，创建时间的结束时间，格式为ISO 8601

    ## 响应
    - **200**: 成功返回租户列表
        - 返回类型: TenantListResponse
        - 包含总数和租户信息数组

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    tenants, total = get_tenants(
        db, skip, limit, keyword, active, sort_by, sort_order, start_time, end_time
    )
    return {"total": total, "items": tenants}


@router.put("/{tenant_id}", response_model=TenantResponse)
def update_tenant_api(
    tenant_id: int,
    tenant: TenantUpdate,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    更新租户

    ## 功能描述
    更新指定租户的信息。

    ## 请求参数
    - **tenant_id** (int): 租户ID，路径参数
    - **tenant** (TenantUpdate): 租户更新信息，请求体
        - 包含需要更新的租户字段信息

    ## 响应
    - **200**: 成功更新租户信息
        - 返回类型: TenantResponse
        - 包含更新后的租户完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 租户不存在
    """
    db_tenant = update_tenant(db, tenant_id, tenant)
    if not db_tenant:
        raise HTTPException(status_code=404, detail="租户不存在")
    return db_tenant


@router.delete("/{tenant_id}", response_model=DeleteResponse)
def delete_tenant_api(
    tenant_id: int,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    删除租户

    ## 功能描述
    删除指定的租户账户。

    ## 请求参数
    - **tenant_id** (int): 租户ID，路径参数

    ## 响应
    - **200**: 成功删除租户
        - 返回删除成功的消息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 租户不存在
    """
    if not delete_tenant(db, tenant_id):
        raise HTTPException(status_code=404, detail="租户不存在")
    return {"message": "删除成功"}
