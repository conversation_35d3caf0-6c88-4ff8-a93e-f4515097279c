from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.base import DeleteResponse
from app.schemas.sys.tnt_admin import (
    TntAdminCreate,
    TntAdminListResponse,
    TntAdminResponse,
    TntAdminUpdate,
)
from app.services.sys.auth import get_current_sys_admin
from app.services.sys.tnt_admin import (
    create_tnt_admin,
    delete_tnt_admin,
    get_tnt_admin,
    get_tnt_admins,
    update_tnt_admin,
)

router = APIRouter()


@router.post("", response_model=TntAdminResponse)
def create_tnt_admin_api(
    admin: TntAdminCreate,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    创建租户管理员

    ## 功能描述
    为租户创建新的管理员账户。

    ## 请求参数
    - **admin** (TntAdminCreate): 租户管理员创建信息，请求体
        - 包含用户名、密码、姓名、租户ID等基本信息

    ## 响应
    - **200**: 成功创建租户管理员
        - 返回类型: TntAdminResponse
        - 包含新创建租户管理员的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    - 数据库约束违反时返回400错误
    """
    db_admin = create_tnt_admin(db=db, admin=admin)
    return TntAdminResponse.model_validate(db_admin)


@router.get("/{admin_id}", response_model=TntAdminResponse)
def get_tnt_admin_api(
    admin_id: int,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    获取租户管理员

    ## 功能描述
    根据管理员ID获取租户管理员的详细信息。

    ## 请求参数
    - **admin_id** (int): 租户管理员ID，路径参数

    ## 响应
    - **200**: 成功返回租户管理员信息
        - 返回类型: TntAdminResponse
        - 包含租户管理员的详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 租户管理员不存在
    """
    db_admin = get_tnt_admin(db=db, admin_id=admin_id)
    if not db_admin:
        raise HTTPException(status_code=404, detail="租户管理员不存在")
    return TntAdminResponse.model_validate(db_admin)


@router.get("", response_model=TntAdminListResponse)
def get_tnt_admins_api(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    keyword: Optional[str] = None,
    tenant_id: Optional[int] = None,
    active: Optional[int] = Query(None, description="按用户状态筛选，可选值为0(禁用)或1(启用)"),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    start_time: Optional[datetime] = Query(
        None, description="创建时间的开始时间，格式为ISO 8601"
    ),
    end_time: Optional[datetime] = Query(
        None, description="创建时间的结束时间，格式为ISO 8601"
    ),
):
    """
    获取租户管理员列表

    ## 功能描述
    获取租户管理员列表，支持分页查询、多种筛选条件（包括按用户状态筛选）和排序。

    ## 请求参数
    - **skip** (int, optional): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int, optional): 返回的记录数限制，默认为100，范围1-100
    - **keyword** (str, optional): 搜索关键字，可用于按用户名或姓名搜索
    - **tenant_id** (int, optional): 租户ID筛选，用于查询特定租户的管理员
    - **active** (int, optional): 按用户状态筛选，可选值为0(禁用)或1(启用)，不传则返回所有状态
    - **sort_by** (str, optional): 排序字段，可选值：id, ctime
    - **sort_order** (str, optional): 排序方式，可选值：asc, desc
    - **start_time** (datetime, optional): 创建时间的开始时间，格式为ISO 8601
    - **end_time** (datetime, optional): 创建时间的结束时间，格式为ISO 8601

    ## 响应
    - **200**: 成功返回租户管理员列表
        - 返回类型: TntAdminListResponse
        - 包含分页信息和管理员列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    admins, total = get_tnt_admins(
        db=db,
        skip=skip,
        limit=limit,
        keyword=keyword,
        tenant_id=tenant_id,
        active=active,
        sort_by=sort_by,
        sort_order=sort_order,
        start_time=start_time,
        end_time=end_time,
    )
    tnt_admin_resps = [
        TntAdminResponse.model_validate(admin)
        for admin in admins
    ]
    return TntAdminListResponse(total=total, items=tnt_admin_resps)


@router.put("/{admin_id}", response_model=TntAdminResponse)
def update_tnt_admin_api(
    admin_id: int,
    admin: TntAdminUpdate,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    更新租户管理员

    ## 功能描述
    更新指定租户管理员的信息。

    ## 请求参数
    - **admin_id** (int): 租户管理员ID，路径参数
    - **admin** (TntAdminUpdate): 租户管理员更新信息，请求体
        - 包含需要更新的管理员字段信息

    ## 响应
    - **200**: 成功更新租户管理员信息
        - 返回类型: TntAdminResponse
        - 包含更新后的管理员完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 租户管理员不存在
    - 参数验证错误时返回422错误
    """
    db_admin = update_tnt_admin(db=db, admin_id=admin_id, admin=admin)
    if not db_admin:
        raise HTTPException(status_code=404, detail="租户管理员不存在")
    return TntAdminResponse.model_validate(db_admin)


@router.delete("/{admin_id}", response_model=DeleteResponse)
def delete_tnt_admin_api(
    admin_id: int,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
):
    """
    删除租户管理员

    ## 功能描述
    删除指定的租户管理员账户。

    ## 请求参数
    - **admin_id** (int): 租户管理员ID，路径参数

    ## 响应
    - **200**: 成功删除租户管理员
        - 返回删除成功的消息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 租户管理员不存在
    """
    if not delete_tnt_admin(db=db, admin_id=admin_id):
        raise HTTPException(status_code=404, detail="租户管理员不存在")
    return {"message": "删除成功"}
