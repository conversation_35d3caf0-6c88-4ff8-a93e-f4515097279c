from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.base import DeleteResponse
from app.schemas.sys.tnt_bconf import (
    TntBconfCreate,
    TntBconfListResponse,
    TntBconfResponse,
    TntBconfUpdate,
)
from app.services.sys.auth import get_current_sys_admin
from app.services.sys.tnt_bconf import (
    create_tnt_bconf,
    delete_tnt_bconf,
    get_tnt_bconf,
    get_tnt_bconfs,
    update_tnt_bconf,
)

router = APIRouter()


@router.post("", response_model=TntBconfResponse)
def create_tnt_bconf_api(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    bconf: TntBconfCreate,
):
    """
    创建租户机器人设置

    ## 功能描述
    为租户创建新的机器人配置设置。

    ## 请求参数
    - **bconf** (TntBconfCreate): 租户机器人设置创建信息，请求体
        - 包含租户ID、机器人ID、配置键值对等信息

    ## 响应
    - **200**: 成功创建租户机器人设置
        - 返回类型: TntBconfResponse
        - 包含新创建的租户机器人设置完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    - 数据库约束违反时返回400错误
    """
    return create_tnt_bconf(db=db, bconf=bconf)


@router.get("/{tenant_id}/{key}", response_model=TntBconfResponse)
def get_tnt_bconf_api(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int,
    key: str,
):
    """
    获取租户机器人设置

    ## 功能描述
    根据租户ID和配置键获取特定的租户机器人设置。

    ## 请求参数
    - **tenant_id** (int): 租户ID，路径参数
    - **key** (str): 配置键名，路径参数

    ## 响应
    - **200**: 成功返回租户机器人设置
        - 返回类型: TntBconfResponse
        - 包含租户机器人设置的详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 租户机器人设置不存在
    """
    db_bconf = get_tnt_bconf(db=db, tenant_id=tenant_id, key=key)
    if not db_bconf:
        raise HTTPException(status_code=404, detail="租户机器人设置不存在")
    return db_bconf


@router.get("", response_model=TntBconfListResponse)
def get_tnt_bconfs_api(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    keyword: Optional[str] = None,
    tenant_id: Optional[int] = None,
):
    """
    获取租户机器人设置列表

    ## 功能描述
    获取租户机器人设置列表，支持分页查询和多种筛选条件。

    ## 请求参数
    - **skip** (int, optional): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int, optional): 返回的记录数限制，默认为100，范围1-100
    - **keyword** (str, optional): 搜索关键字，可用于按配置键或值搜索
    - **tenant_id** (int, optional): 租户ID筛选，用于查询特定租户的设置

    ## 响应
    - **200**: 成功返回租户机器人设置列表
        - 返回类型: TntBconfListResponse
        - 包含分页信息和设置列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    bconfs, total = get_tnt_bconfs(
        db=db, skip=skip, limit=limit, keyword=keyword, tenant_id=tenant_id
    )
    return {
        "items": bconfs,
        "total": total,
        "page": skip // limit + 1,
        "size": limit,
        "pages": (total + limit - 1) // limit,
    }


@router.put("/{tenant_id}/{key}", response_model=TntBconfResponse)
def update_tnt_bconf_api(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int,
    key: str,
    bconf: TntBconfUpdate,
):
    """
    更新租户机器人设置

    ## 功能描述
    更新指定租户和配置键的机器人设置信息。

    ## 请求参数
    - **tenant_id** (int): 租户ID，路径参数
    - **key** (str): 原配置键名，路径参数
    - **bconf** (TntBconfUpdate): 租户机器人设置更新信息，请求体
        - **key** (str): 新的配置键名，可选
        - **bid** (int): 机器人ID，可选
        - **notes** (str): 备注，可选

    ## 响应
    - **200**: 成功更新租户机器人设置
        - 返回类型: TntBconfResponse
        - 包含更新后的设置完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 新配置键名已存在或参数验证失败
    - **404**: 租户机器人设置不存在

    ## 注意事项
    - 如果更新配置键名，系统会删除旧记录并创建新记录
    - 确保新的机器人ID (bid) 对应的机器人存在
    """
    try:
        db_bconf = update_tnt_bconf(db=db, tenant_id=tenant_id, key=key, bconf=bconf)
        if not db_bconf:
            raise HTTPException(status_code=404, detail="租户机器人设置不存在")
        return db_bconf
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/{tenant_id}/{key}", response_model=DeleteResponse)
def delete_tnt_bconf_api(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int,
    key: str,
):
    """
    删除租户机器人设置

    ## 功能描述
    删除指定租户和配置键的机器人设置。

    ## 请求参数
    - **tenant_id** (int): 租户ID，路径参数
    - **key** (str): 配置键名，路径参数

    ## 响应
    - **200**: 成功删除租户机器人设置
        - 返回删除成功的消息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 租户机器人设置不存在
    """
    if not delete_tnt_bconf(db=db, tenant_id=tenant_id, key=key):
        raise HTTPException(status_code=404, detail="租户机器人设置不存在")
    return {"message": "删除成功"}
