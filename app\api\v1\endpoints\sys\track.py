from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.track import TrackExerciseListResponse
from app.services.track import get_class_exercises, get_class_name

router = APIRouter()


@router.get("/{class_id}/{student_id}/exercises", response_model=TrackExerciseListResponse)
def get_exercises_api(
    class_id: int,
    student_id: int,
    tenant_id: int,
    db: Session = Depends(get_db)
):
    """
    获取指定班级的所有练习信息

    ## 功能描述
    获取指定班级的所有练习信息，包括练习详情和对应的老师信息，按优先级从小到大排序，如果优先级相同按创建时间倒序排列。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **student_id** (int): 学生ID，路径参数
    - **tenant_id** (int): 租户ID，查询参数

    ## 响应
    - **200**: 成功返回练习列表
        - 返回类型: TrackExerciseListResponse
        - 包含练习列表信息
            - class_name: 班级名称
            - exercises: 练习列表
                - e_id: 练习ID
                - e_title: 练习标题
                - e_type: 练习类型（1：作业单；2：角色扮演）
                - e_pic: 练习图片URL（OSS签名URL）
                - e_intro: 练习简介
                - e_duration: 练习时长（分钟）
                - t_id: 老师ID
                - t_name: 老师姓名
                - t_avatar: 老师头像URL（OSS签名URL）
                - t_intro: 老师简介
                - depend: 练习依赖（0：不依赖；1：依赖）
                - w_id: 作业单ID（如果存在）
                - s_id: 场景ID（如果存在）
                - el_id: 练习情况ID（如果存在）
                - el_status: 练习状态（0：待练习；1：练习中；2：已提交；如果为null表示无练习记录"未开始"）
                - el_btime: 开始练习时间
                - el_stime: 提交练习时间
                - el_utime: 上次更新时间

    ## 错误处理
    - **404**: 班级不存在或用户无权访问该班级
    """
    # 获取班级名称
    class_name = get_class_name(db, class_id, tenant_id)
    
    # 获取指定班级的练习列表
    exercises = get_class_exercises(db, class_id, tenant_id, student_id)
    
    # 构造响应对象
    return TrackExerciseListResponse(class_name=class_name, exercises=exercises)

