from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.base import DeleteResponse
from app.schemas.sys.user import (
    SysUserCreate,
    SysUserListResponse,
    SysUserResponse,
    SysUserUpdate,
)
from app.services.sys.auth import get_current_sys_admin
from app.services.sys.user import (
    create_user,
    delete_user,
    get_user,
    get_user_by_username,
    get_users,
    update_user,
)

router = APIRouter()


@router.post("", response_model=SysUserResponse)
def create_user_api(
    user: SysUserCreate,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    创建用户

    ## 功能描述
    创建新的系统用户账户。

    ## 请求参数
    - **user** (SysUserCreate): 用户创建信息，请求体
        - 包含用户的用户名、密码等基本信息

    ## 响应
    - **200**: 成功创建用户
        - 返回类型: SysUserResponse
        - 包含新创建用户的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 用户名已存在
    """
    # 检查用户名是否已存在
    existing_user = get_user_by_username(db, user.username)
    if existing_user:
        raise HTTPException(status_code=400, detail="用户名已存在")

    db_user = create_user(db, user)
    return db_user


@router.get("/{user_id}", response_model=SysUserResponse)
def get_user_api(
    user_id: int,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    获取用户

    ## 功能描述
    根据用户ID获取用户的详细信息。

    ## 请求参数
    - **user_id** (int): 用户ID，路径参数

    ## 响应
    - **200**: 成功返回用户信息
        - 返回类型: SysUserResponse
        - 包含用户的详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 用户不存在
    """
    db_user = get_user(db, user_id)
    if not db_user:
        raise HTTPException(status_code=404, detail="用户不存在")
    return db_user


@router.get("", response_model=SysUserListResponse)
def get_users_api(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    keyword: Optional[str] = Query(None),
    active: Optional[int] = Query(None, description="按用户状态筛选，可选值为0(禁用)或1(启用)"),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    start_time: Optional[datetime] = Query(
        None, description="创建时间的开始时间，格式为ISO 8601"
    ),
    end_time: Optional[datetime] = Query(
        None, description="创建时间的结束时间，格式为ISO 8601"
    ),
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    获取用户列表

    ## 功能描述
    获取系统用户列表，支持分页查询、关键字搜索、按状态筛选、排序和按创建时间区间筛选。

    ## 请求参数
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 返回的记录数限制，默认为100，范围1-100
    - **keyword** (str): 可选，搜索关键字，可用于按用户名搜索
    - **active** (int): 可选，按用户状态筛选，可选值为0(禁用)或1(启用)，不传则返回所有状态
    - **sort_by** (str): 可选，排序字段，可选值：id, ctime
    - **sort_order** (str): 可选，排序方式，可选值：asc, desc
    - **start_time** (datetime): 可选，创建时间的开始时间，格式为ISO 8601
    - **end_time** (datetime): 可选，创建时间的结束时间，格式为ISO 8601

    ## 响应
    - **200**: 成功返回用户列表
        - 返回类型: SysUserListResponse
        - 包含总数和用户信息数组

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **400**: 参数验证失败
    """
    users, total = get_users(
        db, skip, limit, keyword, active, sort_by, sort_order, start_time, end_time
    )
    return {"total": total, "items": users}


@router.put("/{user_id}", response_model=SysUserResponse)
def update_user_api(
    user_id: int,
    user: SysUserUpdate,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    更新用户

    ## 功能描述
    更新指定用户的信息，包括用户名、密码、激活状态等。

    ## 请求参数
    - **user_id** (int): 用户ID，路径参数
    - **user** (SysUserUpdate): 用户更新信息，请求体
        - username: 用户名（可选）
        - password: 密码（可选）
        - active: 激活状态，0为停用，1为启用（可选）

    ## 响应
    - **200**: 成功更新用户信息
        - 返回类型: SysUserResponse
        - 包含更新后的用户完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 用户不存在
    - **400**: 用户名已存在
    """
    # 如果要更新用户名，检查是否已存在
    if user.username:
        existing_user = get_user_by_username(db, user.username)
        if existing_user and existing_user.id != user_id:
            raise HTTPException(status_code=400, detail="用户名已存在")

    db_user = update_user(db, user_id, user)
    if not db_user:
        raise HTTPException(status_code=404, detail="用户不存在")
    return db_user


@router.delete("/{user_id}", response_model=DeleteResponse)
def delete_user_api(
    user_id: int,
    db: Session = Depends(get_db),
    current_admin=Depends(get_current_sys_admin),
):
    """
    删除用户

    ## 功能描述
    删除指定的系统用户账户。

    ## 请求参数
    - **user_id** (int): 用户ID，路径参数

    ## 响应
    - **200**: 成功删除用户
        - 返回删除成功的消息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 用户不存在
    """
    success = delete_user(db, user_id)
    if not success:
        raise HTTPException(status_code=404, detail="用户不存在")
    return {"message": "用户删除成功"}
