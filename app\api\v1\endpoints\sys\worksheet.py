from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.base import DeleteResponse
from app.utils.oss import get_oss_url
from app.schemas.worksheet import (
    WorksheetCreate,
    WorksheetListResponse,
    WorksheetResponse,
    WorksheetUpdate,
    WorksheetUnitResponse,
    WorksheetUnitQuestionResponse,
    UnitBatchOrderRequest,
    UnitBatchOrderResponse,
    WorksheetAsmBatchOrderRequest,
    WorksheetAsmBatchOrderResponse,
    UnitBatchDeleteRequest,
    UnitBatchDeleteResponse,
    WorksheetAsmBatchDeleteRequest,
    WorksheetAsmBatchDeleteResponse,
    WorksheetAsmBatchMoveRequest,
    WorksheetAsmBatchMoveResponse,
    UnitQuestionsUpdateRequest,
    UnitQuestionsUpdateResponse,
)
from app.schemas.unit import UnitCreate, UnitResponse, UnitUpdate
from app.services import worksheet as worksheet_service
from app.services import unit as unit_service
from app.services import worksheet_asm as worksheet_asm_service
from app.services.sys.auth import get_current_sys_admin

router = APIRouter()


@router.get("", response_model=WorksheetListResponse)
def get_worksheets(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    title: Optional[str] = Query(None, description="按工作表标题搜索"),
    published: Optional[bool] = Query(None, description="按发布状态搜索"),
    active: Optional[bool] = Query(None, description="按激活状态搜索"),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    start_time: Optional[datetime] = Query(
        None, description="创建时间的开始时间，格式为ISO 8601"
    ),
    end_time: Optional[datetime] = Query(
        None, description="创建时间的结束时间，格式为ISO 8601"
    ),
):
    """
    获取工作表列表

    ## 功能描述
    获取指定租户下的工作表列表，支持分页查询、按标题搜索、按发布状态搜索、按激活状态搜索、排序和按创建时间区间筛选。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数
    - **skip** (int, optional): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int, optional): 返回的记录数限制，默认为100，范围1-100
    - **title** (str, optional): 按工作表标题搜索，支持模糊匹配
    - **published** (bool, optional): 按发布状态搜索，true表示已发布，false表示未发布
    - **active** (bool, optional): 按激活状态搜索，true表示已激活，false表示未激活
    - **sort_by** (str, optional): 排序字段，可选值：id, ctime
    - **sort_order** (str, optional): 排序方式，可选值：asc, desc
    - **start_time** (datetime, optional): 创建时间的开始时间，格式为ISO 8601
    - **end_time** (datetime, optional): 创建时间的结束时间，格式为ISO 8601

    ## 响应
    - **200**: 成功返回工作表列表
        - 返回类型: WorksheetListResponse
        - 包含工作表及关联练习的详细信息数组和总数

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    worksheets, total = worksheet_service.get_worksheets(
        db=db,
        tenant_id=tenant_id,
        skip=skip,
        limit=limit,
        title=title,
        published=published,
        active=active,
        sort_by=sort_by,
        sort_order=sort_order,
        start_time=start_time,
        end_time=end_time,
    )

    # 构造响应数据
    items = []
    for worksheet in worksheets:
        exercise = worksheet.exercise
        items.append(
            WorksheetResponse(
                id=worksheet.id,
                tenant_id=worksheet.tenant_id,
                eid=worksheet.eid,
                title=exercise.title,
                type=exercise.type,
                intro=exercise.intro,
                duration=exercise.duration,
                version=exercise.version,
                pic=get_oss_url(exercise.pic) if exercise.pic else None,
                bgtext=exercise.bgtext,
                notes=exercise.notes,
                published=exercise.published,
                ctime=exercise.ctime,
                active=exercise.active,
            )
        )

    return WorksheetListResponse(total=total, items=items)


@router.post("", response_model=WorksheetResponse)
def create_worksheet(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    worksheet_in: WorksheetCreate,
):
    """
    创建工作表

    ## 功能描述
    创建新的工作表记录，会自动先创建对应的练习记录。

    ## 请求参数
    - **worksheet_in** (WorksheetCreate): 工作表创建信息，请求体
        - 包含工作表的标题、内容、租户ID等基本信息

    ## 响应
    - **200**: 成功创建工作表
        - 返回类型: WorksheetResponse
        - 包含新创建工作表的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    - 数据库约束违反时返回400错误
    """
    worksheet = worksheet_service.create_worksheet(db=db, worksheet_in=worksheet_in)

    # 构造响应数据
    exercise = worksheet.exercise
    return WorksheetResponse(
        id=worksheet.id,
        tenant_id=worksheet.tenant_id,
        eid=worksheet.eid,
        title=exercise.title,
        type=exercise.type,
        intro=exercise.intro,
        duration=exercise.duration,
        version=exercise.version,
        pic=get_oss_url(exercise.pic) if exercise.pic else None,
        bgtext=exercise.bgtext,
        notes=exercise.notes,
        published=exercise.published,
        ctime=exercise.ctime,
        active=exercise.active,
    )


@router.get("/{worksheet_id}", response_model=WorksheetResponse)
def get_worksheet(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    worksheet_id: int,
):
    """
    获取工作表信息

    ## 功能描述
    根据工作表ID获取工作表的详细信息。

    ## 请求参数
    - **worksheet_id** (int): 工作表ID，路径参数

    ## 响应
    - **200**: 成功返回工作表信息
        - 返回类型: WorksheetResponse
        - 包含工作表的完整详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 工作表不存在
    """
    worksheet = worksheet_service.get_worksheet(db=db, worksheet_id=worksheet_id)
    if not worksheet:
        raise HTTPException(status_code=404, detail="工作表不存在")

    # 构造响应数据
    exercise = worksheet.exercise
    return WorksheetResponse(
        id=worksheet.id,
        tenant_id=worksheet.tenant_id,
        eid=worksheet.eid,
        title=exercise.title,
        type=exercise.type,
        intro=exercise.intro,
        duration=exercise.duration,
        version=exercise.version,
        pic=get_oss_url(exercise.pic) if exercise.pic else None,
        bgtext=exercise.bgtext,
        notes=exercise.notes,
        published=exercise.published,
        ctime=exercise.ctime,
        active=exercise.active,
    )


@router.put("/{worksheet_id}", response_model=WorksheetResponse)
def update_worksheet(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    worksheet_id: int,
    worksheet_in: WorksheetUpdate,
):
    """
    更新工作表信息

    ## 功能描述
    更新指定工作表的信息。

    ## 请求参数
    - **worksheet_id** (int): 工作表ID，路径参数
    - **worksheet_in** (WorksheetUpdate): 工作表更新信息，请求体
        - 包含需要更新的工作表字段信息

    ## 响应
    - **200**: 成功更新工作表信息
        - 返回类型: WorksheetResponse
        - 包含更新后的工作表完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 工作表不存在
    - 参数验证错误时返回422错误
    """
    worksheet = worksheet_service.update_worksheet(
        db=db, worksheet_id=worksheet_id, worksheet_in=worksheet_in
    )
    if not worksheet:
        raise HTTPException(status_code=404, detail="工作表不存在")

    # 构造响应数据
    exercise = worksheet.exercise
    return WorksheetResponse(
        id=worksheet.id,
        tenant_id=worksheet.tenant_id,
        eid=worksheet.eid,
        title=exercise.title,
        type=exercise.type,
        intro=exercise.intro,
        duration=exercise.duration,
        version=exercise.version,
        pic=get_oss_url(exercise.pic) if exercise.pic else None,
        bgtext=exercise.bgtext,
        notes=exercise.notes,
        published=exercise.published,
        ctime=exercise.ctime,
        active=exercise.active,
    )


@router.delete("/{worksheet_id}", response_model=DeleteResponse)
def delete_worksheet(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    worksheet_id: int,
):
    """
    删除工作表

    ## 功能描述
    删除指定的工作表记录，同时会删除对应的练习记录。

    ## 请求参数
    - **worksheet_id** (int): 工作表ID，路径参数

    ## 响应
    - **200**: 成功删除工作表，返回删除成功消息
    - **404**: 工作表不存在

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 当工作表不存在时，返回404错误
    """
    if not worksheet_service.delete_worksheet(db=db, worksheet_id=worksheet_id):
        raise HTTPException(status_code=404, detail="工作表不存在")
    return {"message": "删除成功"}


@router.get("/{worksheet_id}/units", response_model=list[WorksheetUnitResponse])
def get_worksheet_units(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    worksheet_id: int,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    获取工作表的单元列表

    ## 功能描述
    获取指定工作表下的所有单元，按priority从小到大排序。

    ## 请求参数
    - **worksheet_id** (int): 工作表ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数

    ## 响应
    - **200**: 成功返回单元列表
        - 返回字段: id, name
        - 按priority字段升序排列

    ## 权限要求
    - 需要系统管理员权限

    ## 注意
    - 如果工作表不存在或不属于指定租户，返回空列表
    """
    # 直接获取单元列表，如果工作表不存在会返回空列表
    units = unit_service.get_units_by_worksheet(
        db=db, tenant_id=tenant_id, worksheet_id=worksheet_id
    )

    return [
        WorksheetUnitResponse(
            id=unit.id,
            name=unit.name,
        )
        for unit in units
    ]


@router.post("/unit", response_model=UnitResponse)
def create_worksheet_unit(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    unit_in: UnitCreate,
):
    """
    创建工作表单元

    ## 功能描述
    为指定工作表创建新的单元记录。

    ## 请求参数
    - **unit_in** (UnitCreate): 单元创建信息，请求体
        - 包含单元的名称、描述、租户ID等基本信息

    ## 响应
    - **200**: 成功创建单元
        - 返回类型: UnitResponse
        - 包含新创建单元的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    - 数据库约束违反时返回400错误
    """

    unit = unit_service.create_unit(db=db, unit_in=unit_in)
    return unit


@router.get("/{worksheet_id}/units/{unit_id}", response_model=UnitResponse)
def get_worksheet_unit(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    worksheet_id: int,
    unit_id: int,
):
    """
    获取工作表单元信息

    ## 功能描述
    根据工作表ID和单元ID获取单元的详细信息。

    ## 请求参数
    - **worksheet_id** (int): 工作表ID，路径参数
    - **unit_id** (int): 单元ID，路径参数

    ## 响应
    - **200**: 成功返回单元信息
        - 返回类型: UnitResponse
        - 包含单元的完整详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 单元不存在
    """
    unit = unit_service.get_unit(db=db, unit_id=unit_id, worksheet_id=worksheet_id)
    if not unit:
        raise HTTPException(status_code=404, detail="单元不存在")
    return unit


@router.put("/{worksheet_id}/units/{unit_id}", response_model=UnitResponse)
def update_worksheet_unit(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    worksheet_id: int,
    unit_id: int,
    unit_in: UnitUpdate,
):
    """
    更新工作表单元信息

    ## 功能描述
    更新指定工作表下指定单元的信息。

    ## 请求参数
    - **worksheet_id** (int): 工作表ID，路径参数
    - **unit_id** (int): 单元ID，路径参数
    - **unit_in** (UnitUpdate): 单元更新信息，请求体
        - 包含需要更新的单元字段信息

    ## 响应
    - **200**: 成功更新单元信息
        - 返回类型: UnitResponse
        - 包含更新后的单元完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 单元不存在
    - 参数验证错误时返回422错误
    """
    unit = unit_service.update_unit(
        db=db, unit_id=unit_id, unit_in=unit_in, worksheet_id=worksheet_id
    )
    if not unit:
        raise HTTPException(status_code=404, detail="单元不存在")
    return unit


@router.delete("/units/batch", response_model=UnitBatchDeleteResponse)
def batch_delete_units(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: UnitBatchDeleteRequest,
):
    """
    批量删除单元

    ## 功能描述
    批量删除指定工作表下的单元，会自动先删除所有关联的作业单构成关系，整个操作在事务中进行。

    ## 请求参数
    - **request** (UnitBatchDeleteRequest): 批量删除请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **worksheet_id** (int): 工作表ID，必填
        - **unit_ids** (list[int]): 单元ID列表，必填

    ## 响应
    - **200**: 删除成功
        - 返回类型: UnitBatchDeleteResponse
        - 包含成功删除的数量和删除的单元ID列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **500**: 删除操作失败或数据库事务失败时会自动回滚

    ## 注意事项
    - 只有属于指定租户和工作表的单元才会被删除
    - 不存在或不属于该租户和工作表的单元ID会被忽略，不会报错
    - 删除单元前会自动删除所有关联的作业单构成关系
    - 整个操作在数据库事务中进行，保证数据一致性
    - 如果过程中出现错误，所有操作会回滚
    """
    try:
        deleted_unit_ids, success_count, deleted_asm_count = (
            unit_service.batch_delete_units(
                db=db,
                tenant_id=request.tenant_id,
                worksheet_id=request.worksheet_id,
                unit_ids=request.unit_ids,
            )
        )

        return UnitBatchDeleteResponse(
            success_count=success_count,
            total_count=len(request.unit_ids),
            deleted_unit_ids=deleted_unit_ids,
            deleted_asm_count=deleted_asm_count,
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除操作失败: {str(e)}")


@router.put("/units/batch/order", response_model=UnitBatchOrderResponse)
def batch_update_unit_order(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: UnitBatchOrderRequest,
):
    """
    批量调整单元顺序

    ## 功能描述
    批量调整指定工作表下的单元显示顺序，支持一次性调整多个单元的priority值。

    ## 请求参数
    - **request** (UnitBatchOrderRequest): 批量顺序调整请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **worksheet_id** (int): 工作表ID，必填
        - **units** (list[UnitOrderItem]): 单元顺序列表，必填
            - **id** (int): 单元ID
            - **priority** (int): 新的展示顺序（从小到大）

    ## 响应
    - **200**: 更新成功
        - 返回类型: UnitBatchOrderResponse
        - 包含成功更新的数量、总数量和更新后的单元列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **404**: 部分单元不存在或不属于指定租户和工作表

    ## 注意事项
    - 只有属于指定租户和工作表的单元才会被更新
    - 不存在或不属于该租户和工作表的单元ID会被忽略，不会报错
    - 返回的成功数量可能小于请求的总数量
    """
    updated_units, success_count, total_count = unit_service.batch_update_unit_order(
        db=db,
        tenant_id=request.tenant_id,
        worksheet_id=request.worksheet_id,
        unit_orders=request.units,
    )

    # 转换为响应模型
    unit_responses = []
    for unit in updated_units:
        unit_responses.append(
            WorksheetUnitResponse(
                id=unit.id,
                name=unit.name,
            )
        )

    return UnitBatchOrderResponse(
        success_count=success_count,
        total_count=total_count,
        updated_units=unit_responses,
    )


@router.get("/{worksheet_id}/unit/{unit_id}/questions", response_model=list[WorksheetUnitQuestionResponse])
def get_worksheet_unit_questions(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    worksheet_id: int,
    unit_id: int,
    tenant_id: int = Query(..., description="租户ID"),
):
    """
    获取单元的问题列表

    ## 功能描述
    获取指定工作表下指定单元的所有问题，按priority从小到大排序。

    ## 请求参数
    - **worksheet_id** (int): 工作表ID，路径参数
    - **unit_id** (int): 单元ID，路径参数
    - **tenant_id** (int): 租户ID，必填查询参数

    ## 响应
    - **200**: 成功返回问题列表
        - 返回字段: id（asm关系ID）, qid（问题ID）, title, notes
        - 按priority字段升序排列

    ## 权限要求
    - 需要系统管理员权限

    ## 注意
    - 如果工作表或单元不存在或不属于指定租户，返回空列表
    """
    # 获取包含asm关系信息的问题列表
    worksheet_asms = worksheet_asm_service.get_worksheet_unit_questions_with_asm(
        db=db,
        tenant_id=tenant_id,
        worksheet_id=worksheet_id,
        unit_id=unit_id,
    )

    return [
        WorksheetUnitQuestionResponse(
            id=asm.id,
            qid=asm.qid,
            title=asm.question.title,
            notes=asm.question.notes,
        )
        for asm in worksheet_asms
    ]


@router.put("/unit/questions/batch/order", response_model=WorksheetAsmBatchOrderResponse)
def batch_update_worksheet_asm_order(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: WorksheetAsmBatchOrderRequest,
):
    """
    批量调整作业单构成关系顺序

    ## 功能描述
    批量调整指定工作表下的问题显示顺序，支持一次性调整多个问题的priority值。
    可以调整整个工作表的问题顺序，也可以调整指定单元下的问题顺序。

    ## 请求参数
    - **request** (WorksheetAsmBatchOrderRequest): 批量顺序调整请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **worksheet_id** (int): 工作表ID，必填
        - **unit_id** (int, optional): 单元ID，可选，不传则调整整个工作表的问题顺序
        - **items** (list[WorksheetAsmOrderItem]): 构成关系顺序列表，必填
            - **id** (int): 构成关系ID
            - **priority** (int): 新的展示顺序（从小到大）

    ## 响应
    - **200**: 更新成功
        - 返回类型: WorksheetAsmBatchOrderResponse
        - 包含成功更新的数量、总数量和更新后的问题列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **404**: 部分构成关系不存在或不属于指定租户和工作表

    ## 注意事项
    - 只有属于指定租户、工作表（和单元）的构成关系才会被更新
    - 不存在或不属于指定条件的构成关系ID会被忽略，不会报错
    - 返回的成功数量可能小于请求的总数量
    - 如果指定了unit_id，则只调整该单元下的问题顺序
    """
    updated_asms, success_count, total_count = (
        worksheet_asm_service.batch_update_worksheet_asm_order(
            db=db,
            tenant_id=request.tenant_id,
            worksheet_id=request.worksheet_id,
            unit_id=request.unit_id,
            asm_orders=request.items,
        )
    )

    # 转换为响应模型
    question_responses = []
    for asm in updated_asms:
        question_responses.append(
            WorksheetUnitQuestionResponse(
                id=asm.id,
                qid=asm.qid,
                title=asm.question.title,
                notes=asm.question.notes,
            )
        )

    return WorksheetAsmBatchOrderResponse(
        success_count=success_count,
        total_count=total_count,
        updated_questions=question_responses,
    )



@router.delete("/unit/questions/batch", response_model=WorksheetAsmBatchDeleteResponse)
def batch_delete_worksheet_asms(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: WorksheetAsmBatchDeleteRequest,
):
    """
    批量删除作业单构成关系

    ## 功能描述
    批量删除指定工作表下的问题构成关系，可以删除整个工作表的关系，也可以删除指定单元下的关系，整个操作在事务中进行。

    ## 请求参数
    - **request** (WorksheetAsmBatchDeleteRequest): 批量删除请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **worksheet_id** (int): 工作表ID，必填
        - **unit_id** (int, optional): 单元ID，可选，不传则删除整个工作表的指定构成关系
        - **asm_ids** (list[int]): 构成关系ID列表，必填

    ## 响应
    - **200**: 删除成功
        - 返回类型: WorksheetAsmBatchDeleteResponse
        - 包含成功删除的数量和删除的构成关系ID列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **500**: 删除操作失败或数据库事务失败时会自动回滚

    ## 注意事项
    - 只有属于指定租户、工作表（和单元）的构成关系才会被删除
    - 不存在或不属于指定条件的构成关系ID会被忽略，不会报错
    - 整个操作在数据库事务中进行，保证数据一致性
    - 如果过程中出现错误，所有操作会回滚
    - 如果指定了unit_id，则只删除该单元下的构成关系
    """
    try:
        deleted_asm_ids, success_count = (
            worksheet_asm_service.batch_delete_worksheet_asms(
                db=db,
                tenant_id=request.tenant_id,
                worksheet_id=request.worksheet_id,
                unit_id=request.unit_id,
                asm_ids=request.asm_ids,
            )
        )

        return WorksheetAsmBatchDeleteResponse(
            success_count=success_count,
            total_count=len(request.asm_ids),
            deleted_asm_ids=deleted_asm_ids,
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除操作失败: {str(e)}")


@router.put("/unit/questions", response_model=UnitQuestionsUpdateResponse)
def update_unit_questions(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: UnitQuestionsUpdateRequest,
):
    """
    批量更新单元问题

    ## 功能描述
    根据传入的问题列表，批量添加/删除/更新指定单元下的问题构成关系。
    此操作会将单元的问题配置完全更新为请求中指定的问题列表。

    ## 请求参数
    - **request** (UnitQuestionsUpdateRequest): 单元问题更新请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **worksheet_id** (int): 工作表ID，必填
        - **unit_id** (int): 单元ID，必填
        - **questions** (list[UnitQuestionItem]): 问题列表，必填
            - **id** (int): 问题ID
            - **priority** (int): 展示顺序（从小到大）

    ## 响应
    - **200**: 更新成功
        - 返回类型: UnitQuestionsUpdateResponse
        - 包含添加、更新、删除的数量统计和最终的问题列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **500**: 更新操作失败或数据库事务失败时会自动回滚

    ## 注意事项
    - 此操作会完全替换指定单元下的问题配置
    - 不在请求列表中的现有问题关系将被删除
    - 请求列表中的新问题将被添加
    - 请求列表中的现有问题优先级将被更新
    - 整个操作在数据库事务中进行，保证数据一致性
    - 如果过程中出现错误，所有操作会回滚
    """
    try:
        added_count, updated_count, deleted_count, final_asms = (
            worksheet_asm_service.batch_update_unit_questions(
                db=db,
                tenant_id=request.tenant_id,
                worksheet_id=request.worksheet_id,
                unit_id=request.unit_id,
                questions=request.questions,
            )
        )

        # 转换为响应模型
        question_responses = []
        for asm in final_asms:
            question_responses.append(
                WorksheetUnitQuestionResponse(
                    id=asm.id,
                    qid=asm.qid,
                    title=asm.question.title,
                    notes=asm.question.notes,
                )
            )

        return UnitQuestionsUpdateResponse(
            success=True,
            added_count=added_count,
            updated_count=updated_count,
            deleted_count=deleted_count,
            total_count=len(final_asms),
            questions=question_responses,
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新操作失败: {str(e)}")


@router.put("/unit/questions/batch/move", response_model=WorksheetAsmBatchMoveResponse)
def batch_move_worksheet_asms(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    request: WorksheetAsmBatchMoveRequest,
):
    """
    批量移动作业单构成关系到指定单元

    ## 功能描述
    将多个问题构成关系批量移动到指定的目标单元中，整个操作在事务中进行。

    ## 请求参数
    - **request** (WorksheetAsmBatchMoveRequest): 批量移动请求数据，请求体
        - **tenant_id** (int): 租户ID，必填
        - **worksheet_id** (int): 工作表ID，必填
        - **target_unit_id** (int): 目标单元ID，必填
        - **asm_ids** (list[int]): 要移动的构成关系ID列表，必填

    ## 响应
    - **200**: 移动成功
        - 返回类型: WorksheetAsmBatchMoveResponse
        - 包含成功移动的数量和移动的构成关系ID列表

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **422**: 参数验证失败
    - **404**: 目标单元不存在或不属于指定租户和工作表
    - **500**: 移动操作失败或数据库事务失败时会自动回滚

    ## 注意事项
    - 只有属于指定租户和工作表的构成关系才会被移动
    - 目标单元必须存在且属于同一租户和工作表
    - 不存在或不属于指定条件的构成关系ID会被忽略，不会报错
    - 整个操作在数据库事务中进行，保证数据一致性
    - 如果过程中出现错误，所有操作会回滚
    """
    try:
        moved_asm_ids, success_count = worksheet_asm_service.batch_move_worksheet_asms(
            db=db,
            tenant_id=request.tenant_id,
            worksheet_id=request.worksheet_id,
            target_unit_id=request.target_unit_id,
            asm_ids=request.asm_ids,
        )

        return WorksheetAsmBatchMoveResponse(
            success_count=success_count,
            total_count=len(request.asm_ids),
            moved_asm_ids=moved_asm_ids,
            target_unit_id=request.target_unit_id,
        )
    
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"移动操作失败: {str(e)}")
