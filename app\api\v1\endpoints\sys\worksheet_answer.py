from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import SysAdmin
from app.schemas.worksheet_answer import (
    WorksheetAnswerCreate,
    WorksheetAnswerResponse,
    WorksheetAnswerUpdate,
)
from app.services import worksheet_answer as worksheet_answer_service
from app.services.sys.auth import get_current_sys_admin

router = APIRouter()


@router.get("/", response_model=List[WorksheetAnswerResponse])
def get_worksheet_answers(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    tenant_id: int = Query(..., description="租户ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    worksheet_id: Optional[int] = Query(None),
):
    """
    获取工作表答案列表

    ## 功能描述
    获取指定租户下的工作表答案列表，支持分页查询和筛选。

    ## 请求参数
    - **tenant_id** (int): 租户ID，必填查询参数
    - **skip** (int, optional): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int, optional): 返回的记录数限制，默认为100，范围1-100
    - **worksheet_id** (int, optional): 工作表ID筛选，用于查询特定工作表的答案

    ## 响应
    - **200**: 成功返回工作表答案列表
        - 返回类型: List[WorksheetAnswerResponse]
        - 包含工作表答案的详细信息数组

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    """
    worksheet_answers = worksheet_answer_service.get_worksheet_answers(
        db=db,
        tenant_id=tenant_id,
        skip=skip,
        limit=limit,
        worksheet_id=worksheet_id,
    )
    return worksheet_answers


@router.post("/", response_model=WorksheetAnswerResponse)
def create_worksheet_answer(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    worksheet_answer_in: WorksheetAnswerCreate,
):
    """
    创建工作表答案

    ## 功能描述
    创建新的工作表答案记录。

    ## 请求参数
    - **worksheet_answer_in** (WorksheetAnswerCreate): 工作表答案创建信息，请求体
        - 包含工作表ID、答案内容、租户ID等基本信息

    ## 响应
    - **200**: 成功创建工作表答案
        - 返回类型: WorksheetAnswerResponse
        - 包含新创建工作表答案的完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - 参数验证错误时返回422错误
    - 数据库约束违反时返回400错误
    """
    worksheet_answer = worksheet_answer_service.create_worksheet_answer(
        db=db, worksheet_answer_in=worksheet_answer_in
    )
    return worksheet_answer


@router.get("/{worksheet_answer_id}", response_model=WorksheetAnswerResponse)
def get_worksheet_answer(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    worksheet_answer_id: int,
):
    """
    获取工作表答案信息

    ## 功能描述
    根据工作表答案ID获取工作表答案的详细信息。

    ## 请求参数
    - **worksheet_answer_id** (int): 工作表答案ID，路径参数

    ## 响应
    - **200**: 成功返回工作表答案信息
        - 返回类型: WorksheetAnswerResponse
        - 包含工作表答案的完整详细信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 工作表答案不存在
    """
    worksheet_answer = worksheet_answer_service.get_worksheet_answer(
        db=db, worksheet_answer_id=worksheet_answer_id
    )
    if not worksheet_answer:
        raise HTTPException(status_code=404, detail="工作表答案不存在")
    return worksheet_answer


@router.put("/{worksheet_answer_id}", response_model=WorksheetAnswerResponse)
def update_worksheet_answer(
    *,
    db: Session = Depends(get_db),
    current_admin: SysAdmin = Depends(get_current_sys_admin),
    worksheet_answer_id: int,
    worksheet_answer_in: WorksheetAnswerUpdate,
):
    """
    更新工作表答案信息

    ## 功能描述
    更新指定工作表答案的信息。

    ## 请求参数
    - **worksheet_answer_id** (int): 工作表答案ID，路径参数
    - **worksheet_answer_in** (WorksheetAnswerUpdate): 工作表答案更新信息，请求体
        - 包含需要更新的工作表答案字段信息

    ## 响应
    - **200**: 成功更新工作表答案信息
        - 返回类型: WorksheetAnswerResponse
        - 包含更新后的工作表答案完整信息

    ## 权限要求
    - 需要系统管理员权限

    ## 错误处理
    - **404**: 工作表答案不存在
    - 参数验证错误时返回422错误
    """
    worksheet_answer = worksheet_answer_service.update_worksheet_answer(
        db=db,
        worksheet_answer_id=worksheet_answer_id,
        worksheet_answer_in=worksheet_answer_in,
    )
    if not worksheet_answer:
        raise HTTPException(status_code=404, detail="工作表答案不存在")
    return worksheet_answer
