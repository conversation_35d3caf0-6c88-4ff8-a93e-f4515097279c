from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.base import DeleteResponse
from app.schemas.tnt.auth import (
    LoginRequest,
    LoginResponse,
    LogoutResponse,
    TntAdminPasswordUpdate,
    TntAdminProfile,
)
from app.services.tnt.auth import (
    get_current_tnt_admin,
    get_tnt_admin_profile,
    login,
    logout,
    update_tnt_admin_password,
)

router = APIRouter()


@router.post("/login", response_model=LoginResponse)
def login_api(request: LoginRequest, db: Session = Depends(get_db)):
    """
    租户管理员登录

    ## 功能描述
    租户管理员使用用户名和密码进行身份验证登录，获取访问令牌。

    ## 请求参数
    - **request** (LoginRequest): 登录请求信息
        - username: 租户管理员用户名
        - password: 租户管理员密码

    ## 响应
    - **200**: 登录成功，返回访问令牌和用户信息
    - **400**: 登录失败，用户名或密码错误

    ## 权限要求
    - 无需身份验证（这是登录接口）

    ## 错误处理
    - 用户名不存在或密码错误时返回400错误
    - 账户被禁用时返回400错误
    """
    admin, token, error = login(db, request.username, request.password)
    if error:
        raise HTTPException(status_code=400, detail=error)
    return {
        "token": token,
        "token_type": "bearer",
        "uid": admin.uid if admin else None,
        "username": admin.user.username if admin else None,
        "name": admin.name if admin else None,
        "role": admin.role if admin else None,
        "tenant_id": admin.tenant_id if admin else None,
    }


@router.post("/logout", response_model=LogoutResponse)
def logout_api(
    db: Session = Depends(get_db), current_admin=Depends(get_current_tnt_admin)
):
    """
    租户管理员登出

    ## 功能描述
    租户管理员注销登录，使当前访问令牌失效。

    ## 请求参数
    - 无需额外参数，通过Authorization header传递Bearer token

    ## 响应
    - **200**: 登出成功，返回成功消息

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 无效令牌时返回401错误
    - 令牌过期时返回401错误
    """
    logout(db, current_admin.uid)
    return {"message": "登出成功"}


@router.get("/profile", response_model=TntAdminProfile)
def get_profile(
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    db: Session = Depends(get_db),
):
    """
    获取租户管理员个人信息

    ## 功能描述
    获取当前登录租户管理员的个人资料信息。

    ## 请求参数
    - 无需额外参数，通过Authorization header传递Bearer token

    ## 响应
    - **200**: 成功返回个人信息
    - **404**: 管理员信息不存在

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 管理员记录不存在时返回404错误
    - 无效令牌时返回401错误
    """
    admin = get_tnt_admin_profile(db, current_admin.id)
    if not admin:
        raise HTTPException(status_code=404, detail="Admin not found")

    # 构造响应对象，包含用户信息
    return TntAdminProfile(
        tenant_id=admin.tenant_id,
        uid=admin.uid,
        username=admin.user.username,
        name=admin.name,
        role=admin.role,
    )


@router.put("/password", response_model=DeleteResponse)
def update_password(
    password_update: TntAdminPasswordUpdate,
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    db: Session = Depends(get_db),
):
    """
    更新租户管理员密码

    ## 功能描述
    允许租户管理员更新自己的登录密码。

    ## 请求参数
    - **password_update** (TntAdminPasswordUpdate): 密码更新信息
        - old_password: 当前密码
        - new_password: 新密码
        - confirm_password: 确认新密码

    ## 响应
    - **200**: 密码更新成功
    - **400**: 密码更新失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 当前密码验证失败时返回400错误
    - 管理员不存在时返回400错误
    - 新密码格式不符合要求时返回400错误
    """
    if not update_tnt_admin_password(db, current_admin.id, password_update):
        raise HTTPException(
            status_code=400, detail="Incorrect password or admin not found"
        )
    return {"message": "密码更新成功"}
