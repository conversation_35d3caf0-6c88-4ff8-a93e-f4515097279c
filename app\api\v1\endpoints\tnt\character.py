from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.character import CharacterCreate, CharacterResponse, CharacterUpdate
from app.services import character as character_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/", response_model=List[CharacterResponse])
def get_characters(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    published: Optional[int] = None,
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取人物列表

    ## 功能描述
    获取当前租户下的人物角色列表，支持分页查询、多种状态筛选和排序。

    ## 请求参数
    - **skip** (int): 跳过的记录数，默认为0
    - **limit** (int): 返回的记录数限制，默认为100
    - **active** (Optional[int]): 人物状态筛选，0=非激活，1=激活，None=全部
    - **published** (Optional[int]): 发布状态筛选，0=未发布，1=已发布，None=全部
    - **sort_by** (Optional[str]): 排序字段，可选值：id, ctime
    - **sort_order** (Optional[str]): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回人物列表

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 无效令牌时返回401错误
    - 权限不足时返回403错误
    """
    characters = character_service.get_characters(
        db=db,
        tenant_id=current_admin.tenant_id,
        skip=skip,
        limit=limit,
        active=active,
        published=published,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    return characters


@router.post("/", response_model=CharacterResponse)
def create_character(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    character_in: CharacterCreate,
):
    """
    创建人物

    ## 功能描述
    在当前租户下创建新的人物角色记录。

    ## 请求参数
    - **character_in** (CharacterCreate): 人物创建信息
        - name: 人物名称（必填）
        - description: 人物描述（可选）
        - avatar: 人物头像URL（可选）
        - personality: 人物性格特征（可选）
        - voice_settings: 语音设置（可选）
        - background_story: 背景故事（可选）
        - is_published: 是否发布，默认为未发布
        - is_active: 人物状态，默认为激活状态

    ## 响应
    - **200**: 成功创建人物
    - **400**: 创建失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 必填字段缺失时返回400错误
    - 人物名称重复时返回400错误
    - 数据格式错误时返回400错误
    """
    character_in.tenant_id = current_admin.tenant_id
    character = character_service.create_character(db=db, character_in=character_in)
    return character


@router.get("/{character_id}", response_model=CharacterResponse)
def get_character(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    character_id: int,
):
    """
    获取人物信息

    ## 功能描述
    根据人物ID获取指定人物的详细信息。

    ## 请求参数
    - **character_id** (int): 人物ID

    ## 响应
    - **200**: 成功返回人物信息
    - **404**: 人物不存在
    - **403**: 无权访问该人物

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的人物信息

    ## 错误处理
    - 人物ID不存在时返回404错误
    - 人物不属于当前租户时返回403错误
    """
    character = character_service.get_character(db=db, character_id=character_id)
    if not character:
        raise HTTPException(status_code=404, detail="人物不存在")
    if character.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=403, detail="无权访问该人物")
    return character


@router.put("/{character_id}", response_model=CharacterResponse)
def update_character(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    character_id: int,
    character_in: CharacterUpdate,
):
    """
    更新人物信息

    ## 功能描述
    更新指定人物的信息，支持部分字段更新。

    ## 请求参数
    - **character_id** (int): 人物ID
    - **character_in** (CharacterUpdate): 人物更新信息
        - name: 人物名称（可选）
        - description: 人物描述（可选）
        - avatar: 人物头像URL（可选）
        - personality: 人物性格特征（可选）
        - voice_settings: 语音设置（可选）
        - background_story: 背景故事（可选）
        - is_published: 是否发布（可选）
        - is_active: 人物状态（可选）

    ## 响应
    - **200**: 成功更新人物信息
    - **404**: 人物不存在
    - **403**: 无权访问该人物
    - **400**: 更新失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的人物信息

    ## 错误处理
    - 人物ID不存在时返回404错误
    - 人物不属于当前租户时返回403错误
    - 人物名称重复时返回400错误
    - 数据格式错误时返回400错误
    """
    character = character_service.get_character(db=db, character_id=character_id)
    if not character:
        raise HTTPException(status_code=404, detail="人物不存在")
    if character.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=403, detail="无权访问该人物")
    character = character_service.update_character(
        db=db, character_id=character_id, character_in=character_in
    )
    return character
