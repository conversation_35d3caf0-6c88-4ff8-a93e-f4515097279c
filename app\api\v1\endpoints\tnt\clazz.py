from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.base import DeleteResponse
from app.schemas.clazz import (
    AdminClassCreate,
    AdminClassResponse,
    ClassCreate,
    ClassExerciseCreate,
    ClassExerciseResponse,
    ClassExerciseUpdate,
    ClassResponse,
    ClassStudentCreate,
    ClassStudentResponse,
    ClassStudentUpdate,
    ClassUpdate,
)
from app.services import clazz as class_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/classes", response_model=List[ClassResponse])
def get_classes(
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active: Optional[int] = Query(None, ge=0, le=1),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    获取班级列表

    ## 功能描述
    获取当前租户下的班级列表，支持分页查询、状态筛选和排序。

    ## 请求参数
    - **skip** (int): 跳过的记录数，默认为0，最小值为0
    - **limit** (int): 返回的记录数限制，默认为100，范围为1-100
    - **active** (Optional[int]): 班级状态筛选，0=非激活，1=激活，None=全部
    - **sort_by** (Optional[str]): 排序字段，可选值：id, ctime
    - **sort_order** (Optional[str]): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回班级列表

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 无效令牌时返回401错误
    - 权限不足时返回403错误
    """
    return class_service.get_classes(
        db, current_admin.tenant_id, skip, limit, active, sort_by, sort_order
    )


@router.post("/classes", response_model=ClassResponse)
def create_class(
    class_in: ClassCreate,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    创建班级

    ## 功能描述
    在当前租户下创建新的班级记录。

    ## 请求参数
    - **class_in** (ClassCreate): 班级创建信息
        - tenant_id: 租户ID（必须与当前管理员租户ID匹配）
        - name: 班级名称（必填）
        - code: 班级编号（可选）
        - description: 班级描述（可选）
        - start_date: 开班日期（可选）
        - end_date: 结业日期（可选）
        - max_students: 最大学员数（可选，默认为50）
        - status: 班级状态（可选，默认为激活状态）

    ## 响应
    - **200**: 成功创建班级
    - **400**: 创建失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 租户ID不匹配时返回400错误
    - 班级编号重复时返回400错误
    - 必填字段缺失时返回400错误
    - 日期格式错误时返回400错误
    """
    if class_in.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=400, detail="租户ID不匹配")
    return class_service.create_class(db, class_in)


@router.get("/classes/{class_id}", response_model=ClassResponse)
def get_class(
    class_id: int,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    获取班级信息

    ## 功能描述
    根据班级ID获取指定班级的详细信息。

    ## 请求参数
    - **class_id** (int): 班级ID

    ## 响应
    - **200**: 成功返回班级信息
    - **404**: 班级不存在

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的班级信息

    ## 错误处理
    - 班级ID不存在时返回404错误
    - 班级不属于当前租户时返回404错误
    """
    db_class = class_service.get_class(db, class_id)
    if not db_class or db_class.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=404, detail="班级不存在")
    return db_class


@router.put("/classes/{class_id}", response_model=ClassResponse)
def update_class(
    class_id: int,
    class_in: ClassUpdate,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    更新班级信息

    ## 功能描述
    更新指定班级的信息，支持部分字段更新。

    ## 请求参数
    - **class_id** (int): 班级ID
    - **class_in** (ClassUpdate): 班级更新信息
        - name: 班级名称（可选）
        - code: 班级编号（可选）
        - description: 班级描述（可选）
        - start_date: 开班日期（可选）
        - end_date: 结业日期（可选）
        - max_students: 最大学员数（可选）
        - status: 班级状态（可选）

    ## 响应
    - **200**: 成功更新班级信息
    - **404**: 班级不存在
    - **400**: 更新失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的班级信息

    ## 错误处理
    - 班级ID不存在时返回404错误
    - 班级不属于当前租户时返回404错误
    - 班级编号重复时返回400错误
    - 日期格式错误时返回400错误
    """
    db_class = class_service.get_class(db, class_id)
    if not db_class or db_class.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=404, detail="班级不存在")
    return class_service.update_class(db, class_id, class_in)


@router.get("/classes/{class_id}/students", response_model=List[ClassStudentResponse])
def get_class_students(
    class_id: int,
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active: Optional[int] = Query(None, ge=0, le=1),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    获取班级学员列表

    ## 功能描述
    获取指定班级下的学员列表，支持分页查询和状态筛选。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 返回的记录数限制，默认为100，范围为1-100
    - **active** (Optional[int]): 学员状态筛选，可选值：
        - None: 不筛选，返回所有状态的学员
        - 1: 只返回激活状态的学员
        - 0: 只返回非激活状态的学员

    ## 响应
    - **200**: 成功返回班级学员列表
        - 返回类型: List[ClassStudentInDB]
        - 包含班级学员关系的详细信息数组
    - **404**: 班级不存在
        - 当班级ID不存在或不属于当前租户时返回此错误

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的班级学员

    ## 业务逻辑
    - 验证班级是否存在且属于当前租户
    - 根据班级ID筛选学员数据
    - 支持按状态筛选和分页查询
    """
    db_class = class_service.get_class(db, class_id)
    if not db_class or db_class.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=404, detail="班级不存在")
    return class_service.get_class_students(db, class_id, skip, limit, active)


@router.post("/classes/{class_id}/students", response_model=ClassStudentResponse)
def create_class_student(
    class_id: int,
    class_student_in: ClassStudentCreate,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    创建班级学员关系

    ## 功能描述
    将学员添加到指定班级中，建立班级与学员的关联关系。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **class_student_in** (ClassStudentCreate): 班级学员关系创建信息，请求体
        - tenant_id: 租户ID（必须与当前管理员租户ID匹配）
        - cid: 班级ID（必须与路径参数class_id匹配）
        - student_id: 学员ID（必填）
        - join_date: 加入日期（可选）
        - status: 关系状态（可选，默认为激活状态）
        - notes: 备注信息（可选）

    ## 响应
    - **200**: 成功创建班级学员关系
        - 返回类型: ClassStudentInDB
        - 包含新创建的班级学员关系完整信息
    - **404**: 班级不存在
        - 当班级ID不存在或不属于当前租户时返回此错误
    - **400**: 创建失败
        - 租户ID不匹配时返回此错误
        - 班级ID不匹配时返回此错误
        - 学员已在班级中时返回此错误
        - 班级已满员时返回此错误

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能在当前租户下的班级中添加学员

    ## 业务逻辑
    - 验证班级是否存在且属于当前租户
    - 验证租户ID和班级ID的一致性
    - 检查学员是否已在该班级中
    - 检查班级是否已达到最大学员数限制
    - 创建班级学员关系记录

    ## 数据验证
    - 租户ID必须匹配当前管理员的租户ID
    - 班级ID必须匹配路径参数
    - 学员必须存在且属于当前租户
    - 防止重复添加同一学员到同一班级
    - 检查班级容量限制
    """
    db_class = class_service.get_class(db, class_id)
    if not db_class or db_class.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=404, detail="班级不存在")
    if (
        class_student_in.tenant_id != current_admin.tenant_id
        or class_student_in.cid != class_id
    ):
        raise HTTPException(status_code=400, detail="租户ID或班级ID不匹配")
    return class_service.create_class_student(db, class_student_in)


@router.put(
    "/classes/{class_id}/students/{class_student_id}",
    response_model=ClassStudentResponse,
)
def update_class_student(
    class_id: int,
    class_student_id: int,
    class_student_in: ClassStudentUpdate,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    更新班级学员关系

    ## 功能描述
    更新指定班级学员关系的信息，支持部分字段更新。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **class_student_id** (int): 班级学员关系ID，路径参数
    - **class_student_in** (ClassStudentUpdate): 班级学员关系更新信息，请求体
        - join_date: 加入日期（可选）
        - status: 关系状态（可选）
        - notes: 备注信息（可选）
        - completion_rate: 完成率（可选）
        - last_activity: 最后活动时间（可选）

    ## 响应
    - **200**: 成功更新班级学员关系
        - 返回类型: ClassStudentInDB
        - 包含更新后的班级学员关系完整信息
    - **404**: 资源不存在
        - 当班级不存在或不属于当前租户时返回此错误
        - 当班级学员关系不存在时返回此错误
        - 当班级学员关系不属于指定班级时返回此错误
    - **400**: 更新失败
        - 数据格式错误时返回此错误
        - 状态值无效时返回此错误

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的班级学员关系

    ## 业务逻辑
    - 验证班级是否存在且属于当前租户
    - 验证班级学员关系是否存在且属于指定班级
    - 支持部分字段更新，未提供的字段保持不变
    - 更新学员在班级中的状态和相关信息

    ## 数据验证
    - 验证班级学员关系的存在性和归属性
    - 验证状态值的有效性
    - 验证日期格式的正确性
    - 确保只能更新当前租户下的数据

    ## 安全验证
    - 防止跨租户数据访问
    - 确保班级学员关系属于指定班级
    """
    db_class = class_service.get_class(db, class_id)
    if not db_class or db_class.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=404, detail="班级不存在")
    db_class_student = class_service.get_class_student(db, class_student_id)
    if (
        not db_class_student
        or db_class_student.tenant_id != current_admin.tenant_id
        or db_class_student.cid != class_id
    ):
        raise HTTPException(status_code=404, detail="班级学员关系不存在")
    return class_service.update_class_student(db, class_student_id, class_student_in)


@router.get("/classes/{class_id}/exercises", response_model=List[ClassExerciseResponse])
def get_class_exercises(
    class_id: int,
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active: Optional[int] = Query(None, ge=0, le=1),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    获取班级练习列表

    ## 功能描述
    获取指定班级下的练习列表，支持分页查询和状态筛选。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 返回的记录数限制，默认为100，范围为1-100
    - **active** (Optional[int]): 练习状态筛选，可选值：
        - None: 不筛选，返回所有状态的练习
        - 1: 只返回激活状态的练习
        - 0: 只返回非激活状态的练习

    ## 响应
    - **200**: 成功返回班级练习列表
        - 返回类型: List[ClassExerciseInDB]
        - 包含班级练习关系的详细信息数组
            - id: 班级练习关系ID
            - tenant_id: 租户ID
            - cid: 班级ID
            - exercise_id: 练习ID
            - assigned_date: 分配日期
            - due_date: 截止日期
            - status: 关系状态
            - completion_rate: 完成率
            - created_at: 创建时间
            - updated_at: 更新时间
    - **404**: 班级不存在
        - 当班级ID不存在或不属于当前租户时返回此错误

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的班级练习

    ## 业务逻辑
    - 验证班级是否存在且属于当前租户
    - 根据班级ID筛选练习数据
    - 支持按状态筛选和分页查询
    - 返回练习的分配和完成情况统计

    ## 查询优化
    - 使用分页参数限制返回数据量
    - 支持状态筛选以满足不同业务需求
    - 包含练习完成率等统计信息
    """
    db_class = class_service.get_class(db, class_id)
    if not db_class or db_class.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=404, detail="班级不存在")
    return class_service.get_class_exercises(db, class_id, skip, limit, active)


@router.post("/classes/{class_id}/exercises", response_model=ClassExerciseResponse)
def create_class_exercise(
    class_id: int,
    class_exercise_in: ClassExerciseCreate,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    创建班级练习关系

    ## 功能描述
    将练习分配给指定班级，建立班级与练习的关联关系。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **class_exercise_in** (ClassExerciseCreate): 班级练习关系创建信息，请求体
        - tenant_id: 租户ID（必须与当前管理员租户ID匹配）
        - cid: 班级ID（必须与路径参数class_id匹配）
        - exercise_id: 练习ID（必填）
        - assigned_date: 分配日期（可选，默认为当前日期）
        - due_date: 截止日期（可选）
        - status: 关系状态（可选，默认为激活状态）
        - instructions: 练习说明（可选）

    ## 响应
    - **200**: 成功创建班级练习关系
        - 返回类型: ClassExerciseInDB
        - 包含新创建的班级练习关系完整信息
    - **404**: 班级不存在
        - 当班级ID不存在或不属于当前租户时返回此错误
    - **400**: 创建失败
        - 租户ID不匹配时返回此错误
        - 班级ID不匹配时返回此错误
        - 练习已分配给该班级时返回此错误
        - 练习不存在时返回此错误

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能在当前租户下的班级中分配练习

    ## 业务逻辑
    - 验证班级是否存在且属于当前租户
    - 验证租户ID和班级ID的一致性
    - 检查练习是否已分配给该班级
    - 验证练习是否存在且可用
    - 创建班级练习关系记录

    ## 数据验证
    - 租户ID必须匹配当前管理员的租户ID
    - 班级ID必须匹配路径参数
    - 练习必须存在且属于当前租户
    - 防止重复分配同一练习到同一班级
    - 验证日期的合理性（截止日期应晚于分配日期）

    ## 注意事项
    - 分配练习后，班级内所有学员都可以访问该练习
    - 可以设置截止日期来控制练习的有效期
    """
    db_class = class_service.get_class(db, class_id)
    if not db_class or db_class.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=404, detail="班级不存在")
    if (
        class_exercise_in.tenant_id != current_admin.tenant_id
        or class_exercise_in.cid != class_id
    ):
        raise HTTPException(status_code=400, detail="租户ID或班级ID不匹配")
    return class_service.create_class_exercise(db, class_exercise_in)


@router.put(
    "/classes/{class_id}/exercises/{class_exercise_id}",
    response_model=ClassExerciseResponse,
)
def update_class_exercise(
    class_id: int,
    class_exercise_id: int,
    class_exercise_in: ClassExerciseUpdate,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    更新班级练习关系

    ## 功能描述
    更新指定班级练习关系的信息，支持部分字段更新。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **class_exercise_id** (int): 班级练习关系ID，路径参数
    - **class_exercise_in** (ClassExerciseUpdate): 班级练习关系更新信息，请求体
        - assigned_date: 分配日期（可选）
        - due_date: 截止日期（可选）
        - status: 关系状态（可选）
        - instructions: 练习说明（可选）
        - completion_rate: 完成率（可选）
        - last_activity: 最后活动时间（可选）

    ## 响应
    - **200**: 成功更新班级练习关系
        - 返回类型: ClassExerciseInDB
        - 包含更新后的班级练习关系完整信息
    - **404**: 资源不存在
        - 当班级不存在或不属于当前租户时返回此错误
        - 当班级练习关系不存在时返回此错误
        - 当班级练习关系不属于指定班级时返回此错误
    - **400**: 更新失败
        - 数据格式错误时返回此错误
        - 日期逻辑错误时返回此错误
        - 状态值无效时返回此错误

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的班级练习关系

    ## 业务逻辑
    - 验证班级是否存在且属于当前租户
    - 验证班级练习关系是否存在且属于指定班级
    - 支持部分字段更新，未提供的字段保持不变
    - 更新练习在班级中的分配状态和相关信息
    - 可以调整截止日期和练习说明

    ## 数据验证
    - 验证班级练习关系的存在性和归属性
    - 验证状态值的有效性
    - 验证日期格式和逻辑的正确性
    - 确保截止日期晚于分配日期
    - 确保只能更新当前租户下的数据

    ## 安全验证
    - 防止跨租户数据访问
    - 确保班级练习关系属于指定班级

    ## 注意事项
    - 更新截止日期会影响学员的练习访问权限
    - 状态变更会影响练习的可见性和可用性
    """
    db_class = class_service.get_class(db, class_id)
    if not db_class or db_class.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=404, detail="班级不存在")
    db_class_exercise = class_service.get_class_exercise(db, class_exercise_id)
    if (
        not db_class_exercise
        or db_class_exercise.tenant_id != current_admin.tenant_id
        or db_class_exercise.cid != class_id
    ):
        raise HTTPException(status_code=404, detail="班级练习关系不存在")
    return class_service.update_class_exercise(db, class_exercise_id, class_exercise_in)


@router.get("/classes/{class_id}/admins", response_model=List[AdminClassResponse])
def get_admin_classes(
    class_id: int,
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    获取班级管理员列表

    ## 功能描述
    获取指定班级的管理员（跟班）列表，支持分页查询。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 返回的记录数限制，默认为100，范围为1-100

    ## 响应
    - **200**: 成功返回班级管理员列表
        - 返回类型: List[AdminClassInDB]
        - 包含班级管理员关系的详细信息数组
            - id: 管理员班级关系ID
            - tenant_id: 租户ID
            - cid: 班级ID
            - admin_id: 管理员ID
            - role: 管理员在班级中的角色
            - assigned_date: 分配日期
            - status: 关系状态
            - permissions: 权限设置
            - created_at: 创建时间
            - updated_at: 更新时间
    - **404**: 班级不存在
        - 当班级ID不存在或不属于当前租户时返回此错误

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的班级管理员

    ## 业务逻辑
    - 验证班级是否存在且属于当前租户
    - 根据班级ID筛选管理员数据
    - 支持分页查询以处理大量数据
    - 返回管理员的角色和权限信息

    ## 查询优化
    - 使用分页参数限制返回数据量
    - 包含管理员的详细信息和权限设置

    ## 注意事项
    - 跟班管理员可以协助主管理员管理班级
    - 不同角色的管理员具有不同的权限级别
    """
    db_class = class_service.get_class(db, class_id)
    if not db_class or db_class.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=404, detail="班级不存在")
    return class_service.get_admin_classes(db, class_id, skip, limit)


@router.post("/classes/{class_id}/admins", response_model=AdminClassResponse)
def create_admin_class(
    class_id: int,
    admin_class_in: AdminClassCreate,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    创建班级管理员关系

    ## 功能描述
    将管理员分配到指定班级，建立管理员与班级的跟班关系。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **admin_class_in** (AdminClassCreate): 管理员班级关系创建信息，请求体
        - tenant_id: 租户ID（必须与当前管理员租户ID匹配）
        - cid: 班级ID（必须与路径参数class_id匹配）
        - admin_id: 管理员ID（必填）
        - role: 管理员角色（可选，默认为助教）
        - assigned_date: 分配日期（可选，默认为当前日期）
        - permissions: 权限设置（可选）
        - status: 关系状态（可选，默认为激活状态）

    ## 响应
    - **200**: 成功创建班级管理员关系
        - 返回类型: AdminClassInDB
        - 包含新创建的班级管理员关系完整信息
    - **404**: 班级不存在
        - 当班级ID不存在或不属于当前租户时返回此错误
    - **400**: 创建失败
        - 租户ID不匹配时返回此错误
        - 班级ID不匹配时返回此错误
        - 管理员已分配到该班级时返回此错误
        - 管理员不存在时返回此错误

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能在当前租户下的班级中分配管理员
    - 需要有分配管理员的权限

    ## 业务逻辑
    - 验证班级是否存在且属于当前租户
    - 验证租户ID和班级ID的一致性
    - 检查管理员是否已分配到该班级
    - 验证管理员是否存在且属于当前租户
    - 创建班级管理员关系记录

    ## 数据验证
    - 租户ID必须匹配当前管理员的租户ID
    - 班级ID必须匹配路径参数
    - 管理员必须存在且属于当前租户
    - 防止重复分配同一管理员到同一班级
    - 验证角色和权限设置的有效性

    ## 安全验证
    - 确保只能分配同租户下的管理员
    - 验证权限设置的合理性

    ## 注意事项
    - 跟班管理员可以协助管理班级学员和练习
    - 不同角色具有不同的管理权限
    - 可以设置特定的权限范围
    """
    db_class = class_service.get_class(db, class_id)
    if not db_class or db_class.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=404, detail="班级不存在")
    if (
        admin_class_in.tenant_id != current_admin.tenant_id
        or admin_class_in.cid != class_id
    ):
        raise HTTPException(status_code=400, detail="租户ID或班级ID不匹配")
    return class_service.create_admin_class(db, admin_class_in)


@router.delete(
    "/classes/{class_id}/admins/{admin_class_id}", response_model=DeleteResponse
)
def delete_admin_class(
    class_id: int,
    admin_class_id: int,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    删除班级管理员关系

    ## 功能描述
    删除指定的班级管理员关系，取消管理员的跟班权限。

    ## 请求参数
    - **class_id** (int): 班级ID，路径参数
    - **admin_class_id** (int): 管理员班级关系ID，路径参数

    ## 响应
    - **200**: 成功删除班级管理员关系
        - 返回类型: dict
        - 包含删除成功的消息
            - message: "删除成功"
    - **404**: 资源不存在
        - 当班级不存在或不属于当前租户时返回此错误
        - 当管理员班级关系不存在时返回此错误
        - 当管理员班级关系不属于指定班级时返回此错误
    - **400**: 删除失败
        - 当删除操作失败时返回此错误
        - 当存在依赖关系无法删除时返回此错误

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能删除当前租户下的班级管理员关系
    - 需要有管理班级管理员的权限

    ## 业务逻辑
    - 验证班级是否存在且属于当前租户
    - 验证管理员班级关系是否存在且属于指定班级
    - 检查是否存在依赖关系阻止删除
    - 执行删除操作并返回结果

    ## 数据验证
    - 验证班级管理员关系的存在性和归属性
    - 确保只能删除当前租户下的数据
    - 检查删除操作的合法性

    ## 安全验证
    - 防止跨租户数据访问
    - 确保管理员班级关系属于指定班级
    - 验证删除权限

    ## 注意事项
    - 删除跟班关系后，该管理员将失去对班级的管理权限
    - 删除操作不可逆，请谨慎操作
    - 如果管理员正在处理班级相关任务，建议先完成任务再删除关系
    """
    db_class = class_service.get_class(db, class_id)
    if not db_class or db_class.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=404, detail="班级不存在")
    db_admin_class = class_service.get_admin_class(db, admin_class_id)
    if (
        not db_admin_class
        or db_admin_class.tenant_id != current_admin.tenant_id
        or db_admin_class.cid != class_id
    ):
        raise HTTPException(status_code=404, detail="跟班关系不存在")
    if class_service.delete_admin_class(db, admin_class_id):
        return {"message": "删除成功"}
    raise HTTPException(status_code=400, detail="删除失败")
