from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.exercise import ExerciseCreate, ExerciseResponse, ExerciseUpdate
from app.services import exercise as exercise_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/", response_model=List[ExerciseResponse])
def get_exercises(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    published: Optional[int] = None,
    type: Optional[int] = None,
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取练习列表

    ## 功能描述
    获取当前租户下的练习列表，支持分页查询、多种状态筛选和排序。

    ## 请求参数
    - **skip** (int): 跳过的记录数，默认为0
    - **limit** (int): 返回的记录数限制，默认为100
    - **active** (Optional[int]): 练习状态筛选，0=非激活，1=激活，None=全部
    - **published** (Optional[int]): 发布状态筛选，0=未发布，1=已发布，None=全部
    - **type** (Optional[int]): 练习类型筛选，None=全部类型
    - **sort_by** (Optional[str]): 排序字段，可选值：id, ctime
    - **sort_order** (Optional[str]): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回练习列表

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 无效令牌时返回401错误
    - 权限不足时返回403错误
    """
    exercises = exercise_service.get_exercises(
        db=db,
        tenant_id=current_admin.tenant_id,
        skip=skip,
        limit=limit,
        active=active,
        published=published,
        type=type,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    return exercises


@router.post("/", response_model=ExerciseResponse)
def create_exercise(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    exercise_in: ExerciseCreate,
):
    """
    创建练习

    ## 功能描述
    在当前租户下创建新的练习记录。

    ## 请求参数
    - **exercise_in** (ExerciseCreate): 练习创建信息
        - title: 练习标题（必填）
        - description: 练习描述（可选）
        - exercise_type: 练习类型（必填）
        - difficulty_level: 难度等级，范围1-5（可选，默认为1）
        - estimated_time: 预计完成时间（分钟）（可选）
        - subject_id: 所属科目ID（可选）
        - questions: 题目列表（可选）
        - total_points: 总分值（可选，自动计算）
        - is_published: 是否发布，默认为未发布
        - is_active: 练习状态，默认为激活状态

    ## 响应
    - **200**: 成功创建练习
    - **400**: 创建失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 练习类型不支持时返回400错误
    - 必填字段缺失时返回400错误
    - 难度等级超出范围时返回400错误
    - 科目ID不存在时返回400错误
    """
    exercise_in.tenant_id = current_admin.tenant_id
    exercise = exercise_service.create_exercise(db=db, exercise_in=exercise_in)
    return exercise


@router.get("/{exercise_id}", response_model=ExerciseResponse)
def get_exercise(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    exercise_id: int,
):
    """
    获取练习信息

    ## 功能描述
    根据练习ID获取指定练习的详细信息。

    ## 请求参数
    - **exercise_id** (int): 练习ID

    ## 响应
    - **200**: 成功返回练习信息
    - **404**: 练习不存在
    - **403**: 无权访问该练习

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的练习信息

    ## 错误处理
    - 练习ID不存在时返回404错误
    - 练习不属于当前租户时返回403错误
    """
    exercise = exercise_service.get_exercise(db=db, exercise_id=exercise_id)
    if not exercise:
        raise HTTPException(status_code=404, detail="练习不存在")
    if exercise.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=403, detail="无权访问该练习")
    return exercise


@router.put("/{exercise_id}", response_model=ExerciseResponse)
def update_exercise(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    exercise_id: int,
    exercise_in: ExerciseUpdate,
):
    """
    更新练习信息

    ## 功能描述
    更新指定练习的信息，支持部分字段更新。

    ## 请求参数
    - **exercise_id** (int): 练习ID
    - **exercise_in** (ExerciseUpdate): 练习更新信息
        - title: 练习标题（可选）
        - description: 练习描述（可选）
        - exercise_type: 练习类型（可选）
        - difficulty_level: 难度等级，范围1-5（可选）
        - estimated_time: 预计完成时间（分钟）（可选）
        - subject_id: 所属科目ID（可选）
        - questions: 题目列表（可选）
        - total_points: 总分值（可选）
        - is_published: 是否发布（可选）
        - is_active: 练习状态（可选）

    ## 响应
    - **200**: 成功更新练习信息
    - **404**: 练习不存在
    - **403**: 无权访问该练习
    - **400**: 更新失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的练习信息

    ## 错误处理
    - 练习ID不存在时返回404错误
    - 练习不属于当前租户时返回403错误
    - 练习类型不支持时返回400错误
    - 难度等级超出范围时返回400错误
    - 科目ID不存在时返回400错误
    """
    exercise = exercise_service.get_exercise(db=db, exercise_id=exercise_id)
    if not exercise:
        raise HTTPException(status_code=404, detail="练习不存在")
    if exercise.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=403, detail="无权访问该练习")
    exercise = exercise_service.update_exercise(
        db=db, exercise_id=exercise_id, exercise_in=exercise_in
    )
    return exercise
