from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.framework import FrameworkCreate, FrameworkResponse, FrameworkUpdate
from app.services import framework as framework_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/", response_model=List[FrameworkResponse])
def get_frameworks(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取框架列表

    ## 功能描述
    获取当前租户下的所有框架信息，支持分页和筛选功能。

    ## 请求参数
    - **skip** (int): 跳过的记录数，默认为0
    - **limit** (int): 每页返回的记录数，默认为100，最大为100
    - **active** (Optional[int]): 框架状态筛选，0=禁用，1=启用，None=全部
    - **sort_by** (Optional[str]): 排序字段，可选值：id
    - **sort_order** (Optional[str]): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回框架列表

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 无效令牌时返回401错误
    - 权限不足时返回403错误
    """
    frameworks = framework_service.get_frameworks(
        db=db,
        tenant_id=current_admin.tenant_id,
        skip=skip,
        limit=limit,
        active=active,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    return frameworks


@router.post("/", response_model=FrameworkResponse)
def create_framework(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    framework_in: FrameworkCreate,
):
    """
    创建框架

    ## 功能描述
    在当前租户下创建一个新的框架。

    ## 请求参数
    - **framework_in** (FrameworkCreate): 框架创建数据，包含框架的基本信息

    ## 响应
    - **200**: 成功创建框架
    - **400**: 创建失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 必填字段缺失时返回400错误
    - 数据格式错误时返回400错误
    """
    framework_in.tenant_id = current_admin.tenant_id
    framework = framework_service.create_framework(db=db, framework_in=framework_in)
    return framework


@router.get("/{framework_id}", response_model=FrameworkResponse)
def get_framework(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    framework_id: int,
):
    """
    获取框架信息

    ## 功能描述
    根据框架ID获取单个框架的详细信息。

    ## 请求参数
    - **framework_id** (int): 框架ID

    ## 响应
    - **200**: 成功返回框架信息
    - **404**: 框架不存在
    - **403**: 无权访问该框架

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查看当前租户下的框架

    ## 错误处理
    - 框架ID不存在时返回404错误
    - 框架不属于当前租户时返回403错误
    """
    framework = framework_service.get_framework(db=db, framework_id=framework_id)
    if not framework:
        raise HTTPException(status_code=404, detail="Framework not found")
    if framework.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=403, detail="无权访问该框架")
    return framework


@router.put("/{framework_id}", response_model=FrameworkResponse)
def update_framework(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    framework_id: int,
    framework_in: FrameworkUpdate,
):
    """
    更新框架信息

    ## 功能描述
    根据框架ID更新框架的信息。

    ## 请求参数
    - **framework_id** (int): 框架ID
    - **framework_in** (FrameworkUpdate): 框架更新数据，包含需要更新的字段

    ## 响应
    - **200**: 成功更新框架信息
    - **404**: 框架不存在
    - **403**: 无权访问该框架

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的框架

    ## 错误处理
    - 框架ID不存在时返回404错误
    - 框架不属于当前租户时返回403错误
    - 数据格式错误时返回400错误
    """
    framework = framework_service.get_framework(db=db, framework_id=framework_id)
    if not framework:
        raise HTTPException(status_code=404, detail="Framework not found")
    if framework.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=403, detail="无权访问该框架")
    framework = framework_service.update_framework(
        db=db, framework_id=framework_id, framework_in=framework_in
    )
    return framework
