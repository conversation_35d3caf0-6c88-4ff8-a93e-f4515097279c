from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.line import LineCreate, LineResponse, LineUpdate
from app.services import line as line_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/", response_model=List[LineResponse])
def get_lines(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    sort_by: str = Query(None, description="排序字段，可选值：id"),
    sort_order: str = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取线路列表

    ## 功能描述
    获取当前租户下的所有线路信息，支持分页和筛选功能。

    ## 请求参数
    - **skip** (int): 跳过的记录数，默认为0
    - **limit** (int): 每页返回的记录数，默认为100，最大为100
    - **active** (Optional[int]): 线路状态筛选，0=禁用，1=启用，None=全部
    - **sort_by** (Optional[str]): 排序字段，可选值：id
    - **sort_order** (Optional[str]): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回线路列表

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 无效令牌时返回401错误
    - 权限不足时返回403错误
    """
    lines = line_service.get_lines(
        db=db,
        tenant_id=current_admin.tenant_id,
        skip=skip,
        limit=limit,
        active=active,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    return lines


@router.post("/", response_model=LineResponse)
def create_line(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    line_in: LineCreate,
):
    """
    创建线路

    ## 功能描述
    在当前租户下创建一个新的线路。

    ## 请求参数
    - **line_in** (LineCreate): 线路创建数据，包含线路的基本信息

    ## 响应
    - **200**: 成功创建线路
    - **400**: 创建失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 必填字段缺失时返回400错误
    - 数据格式错误时返回400错误
    """
    line_in.tenant_id = current_admin.tenant_id
    line = line_service.create_line(db=db, line_in=line_in)
    return line


@router.get("/{line_id}", response_model=LineResponse)
def get_line(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    line_id: int,
):
    """
    获取线路信息

    ## 功能描述
    根据线路ID获取单个线路的详细信息。

    ## 请求参数
    - **line_id** (int): 线路ID

    ## 响应
    - **200**: 成功返回线路信息
    - **404**: 线路不存在
    - **403**: 无权访问该线路

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查看当前租户下的线路

    ## 错误处理
    - 线路ID不存在时返回404错误
    - 线路不属于当前租户时返回403错误
    """
    line = line_service.get_line(db=db, line_id=line_id)
    if not line:
        raise HTTPException(status_code=404, detail="Line not found")
    if line.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=403, detail="无权访问该线路")
    return line


@router.put("/{line_id}", response_model=LineResponse)
def update_line(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    line_id: int,
    line_in: LineUpdate,
):
    """
    更新线路信息

    ## 功能描述
    根据线路ID更新线路的信息。

    ## 请求参数
    - **line_id** (int): 线路ID
    - **line_in** (LineUpdate): 线路更新数据，包含需要更新的字段

    ## 响应
    - **200**: 成功更新线路信息
    - **404**: 线路不存在
    - **403**: 无权访问该线路

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的线路

    ## 错误处理
    - 线路ID不存在时返回404错误
    - 线路不属于当前租户时返回403错误
    - 数据格式错误时返回400错误
    """
    line = line_service.get_line(db=db, line_id=line_id)
    if not line:
        raise HTTPException(status_code=404, detail="Line not found")
    if line.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=403, detail="无权访问该线路")
    line = line_service.update_line(db=db, line_id=line_id, line_in=line_in)
    return line
