from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.module import ModuleCreate, ModuleResponse, ModuleUpdate
from app.services import module as module_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/", response_model=List[ModuleResponse])
def get_modules(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    fid: Optional[int] = None,
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取模块列表

    ## 功能描述
    获取当前租户下的所有模块信息，支持分页和筛选功能。

    ## 请求参数
    - **skip** (int): 跳过的记录数，默认为0
    - **limit** (int): 每页返回的记录数，默认为100，最大为100
    - **active** (Optional[int]): 模块状态筛选，0=禁用，1=启用，None=全部
    - **fid** (Optional[int]): 框架ID筛选，None=返回所有框架下的模块
    - **sort_by** (Optional[str]): 排序字段，可选值：id, ctime
    - **sort_order** (Optional[str]): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回模块列表

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 无效令牌时返回401错误
    - 权限不足时返回403错误
    """
    modules = module_service.get_modules(
        db=db,
        tenant_id=current_admin.tenant_id,
        skip=skip,
        limit=limit,
        active=active,
        fid=fid,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    return modules


@router.post("/", response_model=ModuleResponse)
def create_module(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    module_in: ModuleCreate,
):
    """
    创建模块

    ## 功能描述
    在当前租户下创建一个新的模块。

    ## 请求参数
    - **module_in** (ModuleCreate): 模块创建数据，包含模块的基本信息和所属框架等

    ## 响应
    - **200**: 成功创建模块
    - **400**: 创建失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 必填字段缺失时返回400错误
    - 数据格式错误时返回400错误
    - 框架ID不存在时返回400错误
    """
    module_in.tenant_id = current_admin.tenant_id
    module = module_service.create_module(db=db, module_in=module_in)
    return module


@router.get("/{module_id}", response_model=ModuleResponse)
def get_module(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    module_id: int,
):
    """
    获取模块信息

    ## 功能描述
    根据模块ID获取单个模块的详细信息。

    ## 请求参数
    - **module_id** (int): 模块ID

    ## 响应
    - **200**: 成功返回模块信息
    - **404**: 模块不存在

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查看当前租户下的模块

    ## 错误处理
    - 模块ID不存在时返回404错误
    - 模块不属于当前租户时返回404错误
    """
    module = module_service.get_module(
        db=db, module_id=module_id, tenant_id=current_admin.tenant_id
    )
    if not module:
        raise HTTPException(status_code=404, detail="Module not found")
    return module


@router.put("/{module_id}", response_model=ModuleResponse)
def update_module(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    module_id: int,
    module_in: ModuleUpdate,
):
    """
    更新模块信息

    ## 功能描述
    根据模块ID更新模块的信息。

    ## 请求参数
    - **module_id** (int): 模块ID
    - **module_in** (ModuleUpdate): 模块更新数据，包含需要更新的字段

    ## 响应
    - **200**: 成功更新模块信息
    - **404**: 模块不存在

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的模块

    ## 错误处理
    - 模块ID不存在时返回404错误
    - 模块不属于当前租户时返回404错误
    - 数据格式错误时返回400错误
    """
    module = module_service.update_module(
        db=db,
        module_id=module_id,
        module_in=module_in,
        tenant_id=current_admin.tenant_id,
    )
    if not module:
        raise HTTPException(status_code=404, detail="Module not found")
    return module
