from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.base import StatusResponse
from app.schemas.plan import PlanCreate, PlanResponse, PlanUpdate
from app.services import plan as plan_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/", response_model=List[PlanResponse])
def get_plans(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取练习计划列表

    ## 功能描述
    获取当前租户下的所有练习计划信息，支持分页和筛选功能。

    ## 请求参数
    - **skip** (int): 跳过的记录数，默认为0
    - **limit** (int): 每页返回的记录数，默认为100，最大为100
    - **active** (Optional[int]): 计划状态筛选，0=禁用，1=启用，None=全部
    - **sort_by** (Optional[str]): 排序字段，可选值：id
    - **sort_order** (Optional[str]): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回练习计划列表

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 无效令牌时返回401错误
    - 权限不足时返回403错误
    """
    plans = plan_service.get_plans(
        db=db,
        tenant_id=current_admin.tenant_id,
        skip=skip,
        limit=limit,
        active=active,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    return plans


@router.post("/", response_model=PlanResponse)
def create_plan(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    plan_in: PlanCreate,
):
    """
    创建练习计划

    ## 功能描述
    在当前租户下创建一个新的练习计划。

    ## 请求参数
    - **plan_in** (PlanCreate): 练习计划创建数据，包含计划的基本信息、练习内容等

    ## 响应
    - **200**: 成功创建练习计划
    - **400**: 创建失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 必填字段缺失时返回400错误
    - 数据格式错误时返回400错误
    """
    plan_in.tenant_id = current_admin.tenant_id
    plan = plan_service.create_plan(db=db, plan_in=plan_in)
    return plan


@router.get("/{plan_id}", response_model=PlanResponse)
def get_plan(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    plan_id: int,
):
    """
    获取练习计划信息

    ## 功能描述
    根据计划ID获取单个练习计划的详细信息。

    ## 请求参数
    - **plan_id** (int): 练习计划ID

    ## 响应
    - **200**: 成功返回练习计划信息
    - **404**: 练习计划不存在

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查看当前租户下的练习计划

    ## 错误处理
    - 计划ID不存在时返回404错误
    - 计划不属于当前租户时返回404错误
    """
    plan = plan_service.get_plan(
        db=db, plan_id=plan_id, tenant_id=current_admin.tenant_id
    )
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")
    return plan


@router.put("/{plan_id}", response_model=PlanResponse)
def update_plan(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    plan_id: int,
    plan_in: PlanUpdate,
):
    """
    更新练习计划信息

    ## 功能描述
    根据计划ID更新练习计划的信息。

    ## 请求参数
    - **plan_id** (int): 练习计划ID
    - **plan_in** (PlanUpdate): 练习计划更新数据，包含需要更新的字段

    ## 响应
    - **200**: 成功更新练习计划信息
    - **404**: 练习计划不存在

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的练习计划

    ## 错误处理
    - 计划ID不存在时返回404错误
    - 计划不属于当前租户时返回404错误
    - 数据格式错误时返回400错误
    """
    plan = plan_service.update_plan(
        db=db, plan_id=plan_id, plan_in=plan_in, tenant_id=current_admin.tenant_id
    )
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")
    return plan


@router.delete("/{plan_id}", response_model=StatusResponse)
def delete_plan(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    plan_id: int,
):
    """
    删除练习计划

    ## 功能描述
    根据计划ID删除指定的练习计划。

    ## 请求参数
    - **plan_id** (int): 练习计划ID

    ## 响应
    - **200**: 成功删除练习计划
    - **404**: 练习计划不存在

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能删除当前租户下的练习计划

    ## 错误处理
    - 计划ID不存在时返回404错误
    - 计划不属于当前租户时返回404错误
    """
    plan = plan_service.delete_plan(
        db=db, plan_id=plan_id, tenant_id=current_admin.tenant_id
    )
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")
    return {"status": "success"}
