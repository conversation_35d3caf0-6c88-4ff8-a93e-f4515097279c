from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.base import StatusResponse
from app.schemas.question import QuestionCreate, QuestionResponse, QuestionUpdate
from app.services import question as question_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/", response_model=List[QuestionResponse])
def get_questions(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取问题列表

    ## 功能描述
    获取当前租户下的问题列表，支持分页查询和状态筛选。

    ## 请求参数
    - **skip** (int): 跳过的记录数，默认为0
    - **limit** (int): 返回的记录数限制，默认为100
    - **active** (Optional[int]): 问题状态筛选，0=非激活，1=激活，None=全部
    - **sort_by** (Optional[str]): 排序字段，可选值：id, ctime
    - **sort_order** (Optional[str]): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回问题列表

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 无效令牌时返回401错误
    - 权限不足时返回403错误
    """
    questions = question_service.get_questions(
        db=db,
        tenant_id=current_admin.tenant_id,
        skip=skip,
        limit=limit,
        active=active,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    return questions


@router.post("/", response_model=QuestionResponse)
def create_question(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    question_in: QuestionCreate,
):
    """
    创建问题

    ## 功能描述
    在当前租户下创建新的问题记录。

    ## 请求参数
    - **question_in** (QuestionCreate): 问题创建信息
        - title: 问题标题（必填）
        - content: 问题内容（必填）
        - question_type: 问题类型（必填），如："single_choice", "multiple_choice", "fill_blank", "essay"
        - difficulty_level: 难度等级，范围1-5（可选，默认为1）
        - options: 选项列表（选择题必填）
        - correct_answer: 正确答案（必填）
        - explanation: 答案解析（可选）
        - points: 分值（可选，默认为1）
        - tags: 标签列表（可选）
        - is_active: 问题状态，默认为激活状态

    ## 响应
    - **200**: 成功创建问题
    - **400**: 创建失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 问题类型不支持时返回400错误
    - 选择题缺少选项时返回400错误
    - 必填字段缺失时返回400错误
    - 难度等级超出范围时返回400错误
    """
    question_in.tenant_id = current_admin.tenant_id
    question = question_service.create_question(db=db, question_in=question_in)
    return question


@router.get("/{question_id}", response_model=QuestionResponse)
def get_question(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    question_id: int,
):
    """
    获取问题信息

    ## 功能描述
    根据问题ID获取指定问题的详细信息。

    ## 请求参数
    - **question_id** (int): 问题ID

    ## 响应
    - **200**: 成功返回问题信息
    - **404**: 问题不存在

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的问题信息

    ## 错误处理
    - 问题ID不存在时返回404错误
    - 问题不属于当前租户时返回404错误
    """
    question = question_service.get_question(
        db=db, question_id=question_id, tenant_id=current_admin.tenant_id
    )
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")
    return question


@router.put("/{question_id}", response_model=QuestionResponse)
def update_question(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    question_id: int,
    question_in: QuestionUpdate,
):
    """
    更新问题信息

    ## 功能描述
    更新指定问题的信息，支持部分字段更新。

    ## 请求参数
    - **question_id** (int): 问题ID
    - **question_in** (QuestionUpdate): 问题更新信息
        - title: 问题标题（可选）
        - content: 问题内容（可选）
        - question_type: 问题类型（可选）
        - difficulty_level: 难度等级，范围1-5（可选）
        - options: 选项列表（可选）
        - correct_answer: 正确答案（可选）
        - explanation: 答案解析（可选）
        - points: 分值（可选）
        - tags: 标签列表（可选）
        - is_active: 问题状态（可选）

    ## 响应
    - **200**: 成功更新问题信息
    - **404**: 问题不存在
    - **400**: 更新失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的问题信息

    ## 错误处理
    - 问题ID不存在时返回404错误
    - 问题不属于当前租户时返回404错误
    - 问题类型不支持时返回400错误
    - 选择题缺少选项时返回400错误
    - 难度等级超出范围时返回400错误
    """
    question = question_service.update_question(
        db=db,
        question_id=question_id,
        question_in=question_in,
        tenant_id=current_admin.tenant_id,
    )
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")
    return question


@router.delete("/{question_id}", response_model=StatusResponse)
def delete_question(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    question_id: int,
):
    """
    删除问题

    ## 功能描述
    删除指定的问题记录。这是软删除操作，不会真正从数据库中删除记录。

    ## 请求参数
    - **question_id** (int): 问题ID

    ## 响应
    - **200**: 成功删除问题
    - **404**: 问题不存在
    - **400**: 删除失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能删除当前租户下的问题

    ## 错误处理
    - 问题ID不存在时返回404错误
    - 问题不属于当前租户时返回404错误
    - 问题已被作业单引用时返回400错误
    - 问题已有学员答案时返回400错误
    """
    question = question_service.delete_question(
        db=db, question_id=question_id, tenant_id=current_admin.tenant_id
    )
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")
    return {"status": "success"}
