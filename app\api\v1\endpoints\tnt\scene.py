from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.base import StatusResponse
from app.schemas.scene import SceneCreate, SceneResponse, SceneUpdate
from app.services import scene as scene_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/", response_model=List[SceneResponse])
def get_scenes(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取场景列表

    ## 功能描述
    获取当前租户下的场景列表，支持分页查询和状态筛选。

    ## 请求参数
    - **skip** (int): 跳过的记录数，默认为0
    - **limit** (int): 返回的记录数限制，默认为100
    - **active** (Optional[int]): 场景状态筛选，0=非激活，1=激活，None=全部
    - **sort_by** (Optional[str]): 排序字段，可选值：id, ctime
    - **sort_order** (Optional[str]): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回场景列表

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 无效令牌时返回401错误
    - 权限不足时返回403错误
    """
    scenes = scene_service.get_scenes(
        db=db,
        tenant_id=current_admin.tenant_id,
        skip=skip,
        limit=limit,
        active=active,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    return scenes


@router.post("/", response_model=SceneResponse)
def create_scene(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    scene_in: SceneCreate,
):
    """
    创建场景

    ## 功能描述
    在当前租户下创建新的场景记录。

    ## 请求参数
    - **scene_in** (SceneCreate): 场景创建信息
        - name: 场景名称（必填）
        - description: 场景描述（可选）
        - scene_type: 场景类型（必填）
        - background_info: 背景信息（可选）
        - characters: 角色列表（可选）
        - setting: 场景设置（可选）
        - is_active: 场景状态，默认为激活状态

    ## 响应
    - **200**: 成功创建场景
    - **400**: 创建失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 场景类型不支持时返回400错误
    - 必填字段缺失时返回400错误
    - 角色ID不存在时返回400错误
    """
    scene_in.tenant_id = current_admin.tenant_id
    scene = scene_service.create_scene(db=db, scene_in=scene_in)
    return scene


@router.get("/{scene_id}", response_model=SceneResponse)
def get_scene(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    scene_id: int,
):
    """
    获取场景信息

    ## 功能描述
    根据场景ID获取指定场景的详细信息。

    ## 请求参数
    - **scene_id** (int): 场景ID

    ## 响应
    - **200**: 成功返回场景信息
    - **404**: 场景不存在

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的场景信息

    ## 错误处理
    - 场景ID不存在时返回404错误
    - 场景不属于当前租户时返回404错误
    """
    scene = scene_service.get_scene(
        db=db, scene_id=scene_id, tenant_id=current_admin.tenant_id
    )
    if not scene:
        raise HTTPException(status_code=404, detail="Scene not found")
    return scene


@router.put("/{scene_id}", response_model=SceneResponse)
def update_scene(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    scene_id: int,
    scene_in: SceneUpdate,
):
    """
    更新场景信息

    ## 功能描述
    更新指定场景的信息，支持部分字段更新。

    ## 请求参数
    - **scene_id** (int): 场景ID
    - **scene_in** (SceneUpdate): 场景更新信息
        - name: 场景名称（可选）
        - description: 场景描述（可选）
        - scene_type: 场景类型（可选）
        - background_info: 背景信息（可选）
        - characters: 角色列表（可选）
        - setting: 场景设置（可选）
        - is_active: 场景状态（可选）

    ## 响应
    - **200**: 成功更新场景信息
    - **404**: 场景不存在
    - **400**: 更新失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的场景信息

    ## 错误处理
    - 场景ID不存在时返回404错误
    - 场景不属于当前租户时返回404错误
    - 场景类型不支持时返回400错误
    - 角色ID不存在时返回400错误
    """
    scene = scene_service.update_scene(
        db=db, scene_id=scene_id, scene_in=scene_in, tenant_id=current_admin.tenant_id
    )
    if not scene:
        raise HTTPException(status_code=404, detail="Scene not found")
    return scene


@router.delete("/{scene_id}", response_model=StatusResponse)
def delete_scene(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    scene_id: int,
):
    """
    删除场景

    ## 功能描述
    删除指定的场景记录。这是软删除操作，不会真正从数据库中删除记录。

    ## 请求参数
    - **scene_id** (int): 场景ID

    ## 响应
    - **200**: 成功删除场景
    - **404**: 场景不存在
    - **400**: 删除失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能删除当前租户下的场景

    ## 错误处理
    - 场景ID不存在时返回404错误
    - 场景不属于当前租户时返回404错误
    - 场景还有关联的对话或练习时返回400错误
    """
    scene = scene_service.delete_scene(
        db=db, scene_id=scene_id, tenant_id=current_admin.tenant_id
    )
    if not scene:
        raise HTTPException(status_code=404, detail="Scene not found")
    return {"status": "success"}
