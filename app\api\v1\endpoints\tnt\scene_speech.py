from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.base import StatusResponse
from app.schemas.scene_speech import (
    SceneSpeechCreate,
    SceneSpeechResponse,
    SceneSpeechUpdate,
)
from app.services import scene_speech as scene_speech_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/", response_model=List[SceneSpeechResponse])
def get_scene_speeches(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    scene_id: int,
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
):
    """
    获取场景台词列表

    ## 功能描述
    获取指定场景下的台词列表，支持分页查询和状态筛选。

    ## 请求参数
    - **scene_id** (int): 场景ID
    - **skip** (int): 跳过的记录数，默认为0
    - **limit** (int): 返回的记录数限制，默认为100
    - **active** (Optional[int]): 台词状态筛选，0=非激活，1=激活，None=全部

    ## 响应
    - **200**: 成功返回台词列表

    ## 权限要求
    - 需要有效的租户管理员身份令牌

    ## 错误处理
    - 无效令牌时返回401错误
    - 权限不足时返回403错误
    - 场景不存在时返回404错误
    """
    speeches = scene_speech_service.get_scene_speeches(
        db=db,
        scene_id=scene_id,
        tenant_id=current_admin.tenant_id,
        skip=skip,
        limit=limit,
        active=active,
    )
    return speeches


@router.post("/", response_model=SceneSpeechResponse)
def create_scene_speech(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    scene_id: int,
    speech_in: SceneSpeechCreate,
):
    """
    创建场景台词

    ## 功能描述
    在指定场景下创建新的台词记录。

    ## 请求参数
    - **scene_id** (int): 场景ID
    - **speech_in** (SceneSpeechCreate): 台词创建信息
        - character_id: 角色ID（必填）
        - content: 台词内容（必填）
        - order_num: 台词顺序（可选，自动计算）
        - emotion: 情感标签（可选）
        - voice_settings: 语音设置（可选）
        - is_active: 台词状态，默认为激活状态

    ## 响应
    - **200**: 成功创建台词
    - **400**: 创建失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能在当前租户下的场景中创建台词

    ## 错误处理
    - 场景不属于当前租户时返回400错误
    - 角色ID不存在时返回400错误
    - 必填字段缺失时返回400错误
    """
    # Note: scene_speech schema uses elid, not scene_id
    # This needs to be mapped appropriately based on business logic
    speech_in.tenant_id = current_admin.tenant_id
    speech = scene_speech_service.create_scene_speech(db=db, scene_speech_in=speech_in)
    return speech


@router.get("/{speech_id}", response_model=SceneSpeechResponse)
def get_scene_speech(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    scene_id: int,
    speech_id: int,
):
    """
    获取场景台词信息

    ## 功能描述
    根据台词ID获取指定台词的详细信息。

    ## 请求参数
    - **scene_id** (int): 场景ID
    - **speech_id** (int): 台词ID

    ## 响应
    - **200**: 成功返回台词信息
    - **404**: 台词不存在

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的台词信息

    ## 错误处理
    - 台词ID不存在时返回404错误
    - 台词不属于指定场景时返回404错误
    - 台词不属于当前租户时返回404错误
    """
    speech = scene_speech_service.get_scene_speech(
        db=db, scene_speech_id=speech_id, scene_id=scene_id, tenant_id=current_admin.tenant_id
    )
    if not speech:
        raise HTTPException(status_code=404, detail="Scene speech not found")
    return speech


@router.put("/{speech_id}", response_model=SceneSpeechResponse)
def update_scene_speech(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    scene_id: int,
    speech_id: int,
    speech_in: SceneSpeechUpdate,
):
    """
    更新场景台词信息

    ## 功能描述
    更新指定台词的信息，支持部分字段更新。

    ## 请求参数
    - **scene_id** (int): 场景ID
    - **speech_id** (int): 台词ID
    - **speech_in** (SceneSpeechUpdate): 台词更新信息
        - character_id: 角色ID（可选）
        - content: 台词内容（可选）
        - order_num: 台词顺序（可选）
        - emotion: 情感标签（可选）
        - voice_settings: 语音设置（可选）
        - is_active: 台词状态（可选）

    ## 响应
    - **200**: 成功更新台词信息
    - **404**: 台词不存在
    - **400**: 更新失败

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的台词信息

    ## 错误处理
    - 台词ID不存在时返回404错误
    - 台词不属于指定场景时返回404错误
    - 台词不属于当前租户时返回404错误
    - 角色ID不存在时返回400错误
    - 顺序号重复时返回400错误
    """
    speech = scene_speech_service.update_scene_speech(
        db=db,
        scene_speech_id=speech_id,
        scene_speech_in=speech_in,
        scene_id=scene_id,
        tenant_id=current_admin.tenant_id,
    )
    if not speech:
        raise HTTPException(status_code=404, detail="Scene speech not found")
    return speech


@router.delete("/{speech_id}", response_model=StatusResponse)
def delete_scene_speech(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    scene_id: int,
    speech_id: int,
):
    """
    删除场景台词

    ## 功能描述
    删除指定的台词记录。这是软删除操作，不会真正从数据库中删除记录。

    ## 请求参数
    - **scene_id** (int): 场景ID
    - **speech_id** (int): 台词ID

    ## 响应
    - **200**: 成功删除台词
    - **404**: 台词不存在

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能删除当前租户下的台词

    ## 错误处理
    - 台词ID不存在时返回404错误
    - 台词不属于指定场景时返回404错误
    - 台词不属于当前租户时返回404错误
    """
    speech = scene_speech_service.delete_scene_speech(
        db=db, scene_speech_id=speech_id, scene_id=scene_id, tenant_id=current_admin.tenant_id
    )
    if not speech:
        raise HTTPException(status_code=404, detail="Scene speech not found")
    return {"status": "success"}
