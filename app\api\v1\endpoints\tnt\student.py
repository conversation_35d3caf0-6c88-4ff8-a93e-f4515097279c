from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.student import StudentCreate, StudentResponse, StudentUpdate
from app.services import student as student_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/students", response_model=List[StudentResponse])
def get_students(
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id, ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取学员列表

    ## 功能描述
    获取当前租户下的学员列表，支持分页查询和排序。

    ## 请求参数
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 返回的记录数限制，默认为100，范围为1-100
    - **sort_by** (Optional[str]): 排序字段，可选值：id, ctime
    - **sort_order** (Optional[str]): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回学员列表，返回类型：List[StudentResponse]

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的学员

    ## 错误处理
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    return student_service.get_students(
        db, current_admin.tenant_id, skip, limit, sort_by=sort_by, sort_order=sort_order
    )


@router.post("/students", response_model=StudentResponse)
def create_student(
    student_in: StudentCreate,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    创建学员

    ## 功能描述
    在当前租户下创建新的学员记录。

    ## 请求参数
    - **student_in** (StudentCreate): 学员创建信息，请求体

    ## 响应
    - **200**: 成功创建学员，返回类型：StudentResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能在当前租户下创建学员

    ## 错误处理
    - **400**: 租户ID不匹配或学员编号重复
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    if student_in.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=400, detail="租户ID不匹配")
    return student_service.create_student(db, student_in)


@router.get("/students/{student_id}", response_model=StudentResponse)
def get_student(
    student_id: int,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    获取学员信息

    ## 功能描述
    根据学员ID获取指定学员的详细信息。

    ## 请求参数
    - **student_id** (int): 学员ID，路径参数

    ## 响应
    - **200**: 成功返回学员信息，返回类型：StudentResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的学员信息

    ## 错误处理
    - **404**: 学员不存在或不属于当前租户
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    db_student = student_service.get_student(db, student_id)
    if not db_student or db_student.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=404, detail="学员不存在")
    return db_student


@router.put("/students/{student_id}", response_model=StudentResponse)
def update_student(
    student_id: int,
    student_in: StudentUpdate,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
):
    """
    更新学员信息

    ## 功能描述
    更新指定学员的信息，支持部分字段更新。

    ## 请求参数
    - **student_id** (int): 学员ID，路径参数
    - **student_in** (StudentUpdate): 学员更新信息，请求体

    ## 响应
    - **200**: 成功更新学员信息，返回类型：StudentResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的学员信息

    ## 错误处理
    - **404**: 学员不存在或不属于当前租户
    - **400**: 学员编号重复或数据格式错误
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    db_student = student_service.get_student(db, student_id)
    if not db_student or db_student.tenant_id != current_admin.tenant_id:
        raise HTTPException(status_code=404, detail="学员不存在")
    return student_service.update_student(db, student_id, student_in)
