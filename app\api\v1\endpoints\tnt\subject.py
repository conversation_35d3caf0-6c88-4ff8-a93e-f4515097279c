from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.base import StatusResponse
from app.schemas.subject import SubjectCreate, SubjectResponse, SubjectUpdate
from app.services import subject as subject_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/", response_model=List[SubjectResponse])
def get_subjects(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    skip: int = 0,
    limit: int = 100,
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取主题列表

    ## 功能描述
    获取当前租户下的主题列表，支持分页查询和排序。

    ## 请求参数
    - **skip** (int): 跳过的记录数，用于分页，默认为0
    - **limit** (int): 返回的记录数限制，默认为100
    - **sort_by** (Optional[str]): 排序字段，可选值：id
    - **sort_order** (Optional[str]): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回主题列表，返回类型：List[SubjectResponse]

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的主题

    ## 错误处理
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    subjects = subject_service.get_subjects(
        db=db,
        tenant_id=current_admin.tenant_id,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    return subjects


@router.post("/", response_model=SubjectResponse)
def create_subject(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    subject_in: SubjectCreate,
):
    """
    创建主题

    ## 功能描述
    在当前租户下创建新的主题记录。

    ## 请求参数
    - **subject_in** (SubjectCreate): 主题创建信息，请求体

    ## 响应
    - **200**: 成功创建主题，返回类型：SubjectResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能在当前租户下创建主题

    ## 错误处理
    - **400**: 主题编码重复或必填字段缺失
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    subject_in.tenant_id = current_admin.tenant_id
    subject = subject_service.create_subject(db=db, subject_in=subject_in)
    return subject


@router.get("/{subject_id}", response_model=SubjectResponse)
def get_subject(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    subject_id: int,
):
    """
    获取主题信息

    ## 功能描述
    根据主题ID获取指定主题的详细信息。

    ## 请求参数
    - **subject_id** (int): 主题ID，路径参数

    ## 响应
    - **200**: 成功返回主题信息，返回类型：SubjectResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的主题信息

    ## 错误处理
    - **404**: 主题不存在或不属于当前租户
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    subject = subject_service.get_subject(
        db=db, subject_id=subject_id, tenant_id=current_admin.tenant_id
    )
    if not subject:
        raise HTTPException(status_code=404, detail="Subject not found")
    return subject


@router.put("/{subject_id}", response_model=SubjectResponse)
def update_subject(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    subject_id: int,
    subject_in: SubjectUpdate,
):
    """
    更新主题信息

    ## 功能描述
    更新指定主题的信息，支持部分字段更新。

    ## 请求参数
    - **subject_id** (int): 主题ID，路径参数
    - **subject_in** (SubjectUpdate): 主题更新信息，请求体

    ## 响应
    - **200**: 成功更新主题信息，返回类型：SubjectResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的主题信息

    ## 错误处理
    - **404**: 主题不存在或不属于当前租户
    - **400**: 主题编码重复或数据格式错误
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    subject = subject_service.update_subject(
        db=db,
        subject_id=subject_id,
        subject_in=subject_in,
        tenant_id=current_admin.tenant_id,
    )
    if not subject:
        raise HTTPException(status_code=404, detail="Subject not found")
    return subject


@router.delete("/{subject_id}", response_model=StatusResponse)
def delete_subject(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    subject_id: int,
):
    """
    删除主题

    ## 功能描述
    删除指定的主题记录，执行软删除操作。

    ## 请求参数
    - **subject_id** (int): 主题ID，路径参数

    ## 响应
    - **200**: 成功删除主题，返回状态消息

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能删除当前租户下的主题

    ## 错误处理
    - **404**: 主题不存在或不属于当前租户
    - **400**: 主题下还有关联数据，无法删除
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    subject = subject_service.delete_subject(
        db=db, subject_id=subject_id, tenant_id=current_admin.tenant_id
    )
    if not subject:
        raise HTTPException(status_code=404, detail="Subject not found")
    return {"status": "success"}
