from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.teacher import TeacherCreate, TeacherResponse, TeacherUpdate
from app.services import teacher as teacher_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/", response_model=List[TeacherResponse])
def get_teachers(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    skip: int = 0,
    limit: int = 100,
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id，ctime"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取教师列表

    ## 功能描述
    获取当前租户下的教师列表，支持分页查询和排序。

    ## 请求参数
    - **skip** (int): 跳过的记录数，用于分页，默认为0
    - **limit** (int): 返回的记录数限制，默认为100
    - **sort_by** (Optional[str]): 排序字段，可选值：id，ctime
    - **sort_order** (Optional[str]): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回教师列表，返回类型：List[TeacherResponse]

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的教师

    ## 错误处理
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    teachers = teacher_service.get_teachers(
        db=db,
        tenant_id=current_admin.tenant_id,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    return teachers


@router.post("/", response_model=TeacherResponse)
def create_teacher(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    teacher_in: TeacherCreate,
):
    """
    创建教师

    ## 功能描述
    在当前租户下创建新的教师记录。

    ## 请求参数
    - **teacher_in** (TeacherCreate): 教师创建信息，请求体

    ## 响应
    - **200**: 成功创建教师，返回类型：TeacherResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能在当前租户下创建教师

    ## 错误处理
    - **400**: 教师编号重复、科目ID不存在或必填字段缺失
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    teacher_in.tenant_id = current_admin.tenant_id
    teacher = teacher_service.create_teacher(db=db, teacher_in=teacher_in)
    return teacher


@router.get("/{teacher_id}", response_model=TeacherResponse)
def get_teacher(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    teacher_id: int,
):
    """
    获取教师信息

    ## 功能描述
    根据教师ID获取指定教师的详细信息。

    ## 请求参数
    - **teacher_id** (int): 教师ID，路径参数

    ## 响应
    - **200**: 成功返回教师信息，返回类型：TeacherResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的教师信息

    ## 错误处理
    - **404**: 教师不存在或不属于当前租户
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    teacher = teacher_service.get_teacher(
        db=db, teacher_id=teacher_id, tenant_id=current_admin.tenant_id
    )
    if not teacher:
        raise HTTPException(status_code=404, detail="Teacher not found")
    return teacher


@router.put("/{teacher_id}", response_model=TeacherResponse)
def update_teacher(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    teacher_id: int,
    teacher_in: TeacherUpdate,
):
    """
    更新教师信息

    ## 功能描述
    更新指定教师的信息，支持部分字段更新。

    ## 请求参数
    - **teacher_id** (int): 教师ID，路径参数
    - **teacher_in** (TeacherUpdate): 教师更新信息，请求体

    ## 响应
    - **200**: 成功更新教师信息，返回类型：TeacherResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的教师信息

    ## 错误处理
    - **404**: 教师不存在或不属于当前租户
    - **400**: 教师编号重复、科目ID不存在或数据格式错误
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    teacher = teacher_service.update_teacher(
        db=db,
        teacher_id=teacher_id,
        teacher_in=teacher_in,
        tenant_id=current_admin.tenant_id,
    )
    if not teacher:
        raise HTTPException(status_code=404, detail="Teacher not found")
    return teacher


@router.delete("/{teacher_id}")
def delete_teacher(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    teacher_id: int,
):
    """
    删除教师

    ## 功能描述
    删除指定的教师记录，只能删除当前租户下的教师。

    ## 请求参数
    - **teacher_id** (int): 教师ID，路径参数

    ## 响应
    - **200**: 成功删除教师
        - 返回删除成功的确认信息

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能删除当前租户下的教师

    ## 错误处理
    - **404**: 教师不存在或不属于当前租户
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    # 首先验证教师是否存在且属于当前租户
    teacher = teacher_service.get_teacher(
        db=db, teacher_id=teacher_id, tenant_id=current_admin.tenant_id
    )
    if not teacher:
        raise HTTPException(status_code=404, detail="Teacher not found")
    
    # 删除教师
    success = teacher_service.delete_teacher(db=db, teacher_id=teacher_id)
    if not success:
        raise HTTPException(status_code=404, detail="Teacher not found")
    
    return {"message": "教师删除成功", "teacher_id": teacher_id}
