from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.base import StatusResponse
from app.schemas.unit import UnitCreate, UnitResponse, UnitUpdate
from app.services import unit as unit_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/", response_model=List[UnitResponse])
def get_units(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    worksheet_id: int,
    skip: int = 0,
    limit: int = 100,
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
):
    """
    获取单元列表

    ## 功能描述
    获取指定作业单下的单元列表，支持分页查询和排序。

    ## 请求参数
    - **worksheet_id** (int): 作业单ID，查询参数
    - **skip** (int): 跳过的记录数，用于分页，默认为0
    - **limit** (int): 返回的记录数限制，默认为100
    - **sort_by** (Optional[str]): 排序字段，可选值：id
    - **sort_order** (Optional[str]): 排序方式，可选值：asc, desc

    ## 响应
    - **200**: 成功返回单元列表，返回类型：List[UnitResponse]

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的单元

    ## 错误处理
    - **404**: 作业单不存在或不属于当前租户
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    units = unit_service.get_units(
        db=db,
        worksheet_id=worksheet_id,
        tenant_id=current_admin.tenant_id,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    return units


@router.post("/", response_model=UnitResponse)
def create_unit(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    worksheet_id: int,
    unit_in: UnitCreate,
):
    """
    创建单元

    ## 功能描述
    在指定作业单下创建新的单元记录。

    ## 请求参数
    - **worksheet_id** (int): 作业单ID，查询参数
    - **unit_in** (UnitCreate): 单元创建信息，请求体

    ## 响应
    - **200**: 成功创建单元，返回类型：UnitResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能在当前租户下的作业单中创建单元

    ## 错误处理
    - **400**: 作业单不属于当前租户或必填字段缺失
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    unit_in.wid = worksheet_id
    unit_in.tenant_id = current_admin.tenant_id
    unit = unit_service.create_unit(db=db, unit_in=unit_in)
    return unit


@router.get("/{unit_id}", response_model=UnitResponse)
def get_unit(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    worksheet_id: int,
    unit_id: int,
):
    """
    获取单元信息

    ## 功能描述
    根据单元ID获取指定单元的详细信息。

    ## 请求参数
    - **worksheet_id** (int): 作业单ID，查询参数
    - **unit_id** (int): 单元ID，路径参数

    ## 响应
    - **200**: 成功返回单元信息，返回类型：UnitResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的单元信息

    ## 错误处理
    - **404**: 单元不存在或不属于当前租户
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    unit = unit_service.get_unit(
        db=db,
        unit_id=unit_id,
        worksheet_id=worksheet_id,
        tenant_id=current_admin.tenant_id,
    )
    if not unit:
        raise HTTPException(status_code=404, detail="Unit not found")
    return unit


@router.put("/{unit_id}", response_model=UnitResponse)
def update_unit(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    worksheet_id: int,
    unit_id: int,
    unit_in: UnitUpdate,
):
    """
    更新单元信息

    ## 功能描述
    更新指定单元的信息，支持部分字段更新。

    ## 请求参数
    - **worksheet_id** (int): 作业单ID，查询参数
    - **unit_id** (int): 单元ID，路径参数
    - **unit_in** (UnitUpdate): 单元更新信息，请求体

    ## 响应
    - **200**: 成功更新单元信息，返回类型：UnitResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的单元信息

    ## 错误处理
    - **404**: 单元不存在或不属于当前租户
    - **400**: 单元类型不支持或数据格式错误
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    unit = unit_service.update_unit(
        db=db,
        unit_id=unit_id,
        worksheet_id=worksheet_id,
        unit_in=unit_in,
        tenant_id=current_admin.tenant_id,
    )
    if not unit:
        raise HTTPException(status_code=404, detail="Unit not found")
    return unit


@router.delete("/{unit_id}", response_model=StatusResponse)
def delete_unit(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    worksheet_id: int,
    unit_id: int,
):
    """
    删除单元

    ## 功能描述
    删除指定的单元记录，执行软删除操作。

    ## 请求参数
    - **worksheet_id** (int): 作业单ID，查询参数
    - **unit_id** (int): 单元ID，路径参数

    ## 响应
    - **200**: 成功删除单元，返回状态消息

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能删除当前租户下的单元

    ## 错误处理
    - **404**: 单元不存在或不属于当前租户
    - **400**: 单元下还有关联数据，无法删除
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    unit = unit_service.delete_unit(
        db=db,
        unit_id=unit_id,
        worksheet_id=worksheet_id,
        tenant_id=current_admin.tenant_id,
    )
    if not unit:
        raise HTTPException(status_code=404, detail="Unit not found")
    return {"status": "success"}
