from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.base import StatusResponse
from app.schemas.worksheet import WorksheetCreate, WorksheetResponse, WorksheetUpdate
from app.services import worksheet as worksheet_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/", response_model=List[WorksheetResponse])
def get_worksheets(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    skip: int = Query(
        0, ge=0, description="跳过的记录数，用于分页，默认为0，最小值为0"
    ),
    limit: int = Query(
        100, ge=1, le=100, description="返回的记录数限制，默认为100，范围为1-100"
    ),
    sort_by: Optional[str] = Query(None, description="排序字段，可选值：id"),
    sort_order: Optional[str] = Query(None, description="排序方式，可选值：asc, desc"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
):
    """
    获取作业单列表

    ## 功能描述
    获取当前租户下的作业单列表，支持分页查询、排序和时间筛选。

    ## 请求参数
    - **skip** (int): 跳过的记录数，用于分页，默认为0，最小值为0
    - **limit** (int): 返回的记录数限制，默认为100，范围为1-100
    - **sort_by** (Optional[str]): 排序字段，可选值：id
    - **sort_order** (Optional[str]): 排序方式，可选值：asc, desc
    - **start_time** (Optional[datetime]): 开始时间
    - **end_time** (Optional[datetime]): 结束时间

    ## 响应
    - **200**: 成功返回作业单列表，返回类型：List[WorksheetResponse]

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的作业单

    ## 错误处理
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    worksheets = worksheet_service.get_worksheets(
        db=db,
        tenant_id=current_admin.tenant_id,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        sort_order=sort_order,
        start_time=start_time,
        end_time=end_time,
    )
    return worksheets


@router.post("/", response_model=WorksheetResponse)
def create_worksheet(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    worksheet_in: WorksheetCreate,
):
    """
    创建作业单

    ## 功能描述
    在当前租户下创建新的作业单记录。

    ## 请求参数
    - **worksheet_in** (WorksheetCreate): 作业单创建信息，请求体

    ## 响应
    - **200**: 成功创建作业单，返回类型：WorksheetResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能在当前租户下创建作业单

    ## 错误处理
    - **400**: 科目ID不存在或必填字段缺失
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    worksheet_in.tenant_id = current_admin.tenant_id
    worksheet = worksheet_service.create_worksheet(db=db, worksheet_in=worksheet_in)
    return worksheet


@router.get("/{worksheet_id}", response_model=WorksheetResponse)
def get_worksheet(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    worksheet_id: int,
):
    """
    获取作业单详情

    ## 功能描述
    根据作业单ID获取指定作业单的详细信息。

    ## 请求参数
    - **worksheet_id** (int): 作业单ID，路径参数

    ## 响应
    - **200**: 成功返回作业单详情，返回类型：WorksheetResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的作业单信息

    ## 错误处理
    - **404**: 作业单不存在或不属于当前租户
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    worksheet = worksheet_service.get_worksheet(
        db=db, worksheet_id=worksheet_id, tenant_id=current_admin.tenant_id
    )
    if not worksheet:
        raise HTTPException(status_code=404, detail="Worksheet not found")
    return worksheet


@router.put("/{worksheet_id}", response_model=WorksheetResponse)
def update_worksheet(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    worksheet_id: int,
    worksheet_in: WorksheetUpdate,
):
    """
    更新作业单信息

    ## 功能描述
    更新指定作业单的信息，支持部分字段更新。

    ## 请求参数
    - **worksheet_id** (int): 作业单ID，路径参数
    - **worksheet_in** (WorksheetUpdate): 作业单更新信息，请求体

    ## 响应
    - **200**: 成功更新作业单信息，返回类型：WorksheetResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的作业单信息

    ## 错误处理
    - **404**: 作业单不存在或不属于当前租户
    - **400**: 科目ID不存在或数据格式错误
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    worksheet = worksheet_service.update_worksheet(
        db=db,
        worksheet_id=worksheet_id,
        worksheet_in=worksheet_in,
        tenant_id=current_admin.tenant_id,
    )
    if not worksheet:
        raise HTTPException(status_code=404, detail="Worksheet not found")
    return worksheet


@router.delete("/{worksheet_id}", response_model=StatusResponse)
def delete_worksheet(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    worksheet_id: int,
):
    """
    删除作业单

    ## 功能描述
    删除指定的作业单记录，执行软删除操作。

    ## 请求参数
    - **worksheet_id** (int): 作业单ID，路径参数

    ## 响应
    - **200**: 成功删除作业单，返回状态消息

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能删除当前租户下的作业单

    ## 错误处理
    - **404**: 作业单不存在或不属于当前租户
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    worksheet = worksheet_service.delete_worksheet(
        db=db, worksheet_id=worksheet_id, tenant_id=current_admin.tenant_id
    )
    if not worksheet:
        raise HTTPException(status_code=404, detail="Worksheet not found")
    return {"status": "success"}
