from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntAdmin
from app.schemas.base import StatusResponse
from app.schemas.worksheet_answer import (
    WorksheetAnswerCreate,
    WorksheetAnswerResponse,
    WorksheetAnswerUpdate,
)
from app.services import worksheet_answer as worksheet_answer_service
from app.services.tnt.auth import get_current_tnt_admin

router = APIRouter()


@router.get("/", response_model=List[WorksheetAnswerResponse])
def get_worksheet_answers(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    worksheet_id: int,
    student_id: int,
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
):
    """
    获取作业单答案列表

    ## 功能描述
    获取指定作业单和学员的答案列表，支持分页查询和状态筛选。

    ## 请求参数
    - **worksheet_id** (int): 作业单ID，查询参数
    - **student_id** (int): 学员ID，查询参数
    - **skip** (int): 跳过的记录数，用于分页，默认为0
    - **limit** (int): 返回的记录数限制，默认为100
    - **active** (Optional[int]): 答案状态筛选，可选值：1(有效), 0(无效), None(全部)

    ## 响应
    - **200**: 成功返回答案列表，返回类型：List[WorksheetAnswerResponse]

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的答案

    ## 错误处理
    - **404**: 作业单或学员不存在或不属于当前租户
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    answers = worksheet_answer_service.get_worksheet_answers(
        db=db,
        worksheet_id=worksheet_id,
        student_id=student_id,
        tenant_id=current_admin.tenant_id,
        skip=skip,
        limit=limit,
        active=active,
    )
    return answers


@router.post("/", response_model=WorksheetAnswerResponse)
def create_worksheet_answer(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    worksheet_id: int,
    student_id: int,
    answer_in: WorksheetAnswerCreate,
):
    """
    创建作业单答案

    ## 功能描述
    为指定的作业单和学员创建新的答案记录。

    ## 请求参数
    - **worksheet_id** (int): 作业单ID，查询参数
    - **student_id** (int): 学员ID，查询参数
    - **answer_in** (WorksheetAnswerCreate): 答案创建信息，请求体

    ## 响应
    - **200**: 成功创建答案，返回类型：WorksheetAnswerResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能在当前租户下创建答案

    ## 错误处理
    - **400**: 作业单或学员不属于当前租户，或答案已存在
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    answer_in.tenant_id = current_admin.tenant_id
    answer = worksheet_answer_service.create_worksheet_answer(
        db=db, worksheet_answer_in=answer_in
    )
    return answer


@router.get("/{answer_id}", response_model=WorksheetAnswerResponse)
def get_worksheet_answer(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    worksheet_id: int,
    student_id: int,
    answer_id: int,
):
    """
    获取作业单答案信息

    ## 功能描述
    根据答案ID获取指定答案的详细信息。

    ## 请求参数
    - **worksheet_id** (int): 作业单ID，查询参数
    - **student_id** (int): 学员ID，查询参数
    - **answer_id** (int): 答案ID，路径参数

    ## 响应
    - **200**: 成功返回答案信息，返回类型：WorksheetAnswerResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能查询当前租户下的答案信息

    ## 错误处理
    - **404**: 答案不存在或不属于当前租户
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    answer = worksheet_answer_service.get_worksheet_answer(
        db=db,
        worksheet_answer_id=answer_id,
        worksheet_id=worksheet_id,
        student_id=student_id,
        tenant_id=current_admin.tenant_id,
    )
    if not answer:
        raise HTTPException(status_code=404, detail="Worksheet answer not found")
    return answer


@router.put("/{answer_id}", response_model=WorksheetAnswerResponse)
def update_worksheet_answer(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    worksheet_id: int,
    student_id: int,
    answer_id: int,
    answer_in: WorksheetAnswerUpdate,
):
    """
    更新作业单答案信息

    ## 功能描述
    更新指定答案的信息，支持部分字段更新，通常用于批改作业。

    ## 请求参数
    - **worksheet_id** (int): 作业单ID，查询参数
    - **student_id** (int): 学员ID，查询参数
    - **answer_id** (int): 答案ID，路径参数
    - **answer_in** (WorksheetAnswerUpdate): 答案更新信息，请求体

    ## 响应
    - **200**: 成功更新答案信息，返回类型：WorksheetAnswerResponse

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能更新当前租户下的答案信息

    ## 错误处理
    - **404**: 答案不存在或不属于当前租户
    - **400**: 得分超出范围或数据格式错误
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    answer = worksheet_answer_service.update_worksheet_answer(
        db=db,
        worksheet_answer_id=answer_id,
        worksheet_answer_in=answer_in,
        worksheet_id=worksheet_id,
        student_id=student_id,
        tenant_id=current_admin.tenant_id,
    )
    if not answer:
        raise HTTPException(status_code=404, detail="Worksheet answer not found")
    return answer


@router.delete("/{answer_id}", response_model=StatusResponse)
def delete_worksheet_answer(
    *,
    db: Session = Depends(get_db),
    current_admin: TntAdmin = Depends(get_current_tnt_admin),
    worksheet_id: int,
    student_id: int,
    answer_id: int,
):
    """
    删除作业单答案

    ## 功能描述
    删除指定的答案记录，执行软删除操作。

    ## 请求参数
    - **worksheet_id** (int): 作业单ID，查询参数
    - **student_id** (int): 学员ID，查询参数
    - **answer_id** (int): 答案ID，路径参数

    ## 响应
    - **200**: 成功删除答案，返回状态消息

    ## 权限要求
    - 需要有效的租户管理员身份令牌
    - 只能删除当前租户下的答案

    ## 错误处理
    - **404**: 答案不存在或不属于当前租户
    - **401**: 认证失败，无效的身份令牌
    - **403**: 权限不足，非租户管理员
    """
    answer = worksheet_answer_service.delete_worksheet_answer(
        db=db,
        worksheet_answer_id=answer_id,
        worksheet_id=worksheet_id,
        student_id=student_id,
        tenant_id=current_admin.tenant_id,
    )
    if not answer:
        raise HTTPException(status_code=404, detail="Worksheet answer not found")
    return {"status": "success"}
