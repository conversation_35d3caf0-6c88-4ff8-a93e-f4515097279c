from datetime import datetime

from pydantic import BaseModel, Field


class BaseSchema(BaseModel):
    """基础Schema类"""

    class Config:
        from_attributes = True


class BaseCreateSchema(BaseSchema):
    """创建基础Schema类"""

    pass


class BaseUpdateSchema(BaseSchema):
    """更新基础Schema类"""

    pass


class BaseResponseSchema(BaseSchema):
    """响应基础Schema类"""

    id: int
    ctime: datetime
    active: int


class BaseListResponseSchema(BaseSchema):
    """列表响应基础Schema类"""

    total: int
    items: list


class DeleteResponse(BaseModel):
    """删除操作响应Schema类"""
    
    message: str = Field(..., description="删除结果消息")

    class Config:
        from_attributes = True


class StatusResponse(BaseModel):
    """状态响应Schema类"""
    
    status: str = Field(..., description="操作状态")

    class Config:
        from_attributes = True
