from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class CharacterCreate(BaseModel):
    """创建人物"""
    
    tenant_id: int = Field(..., description="租户ID")
    name: str = Field(..., description="姓名")
    gender: int = Field(0, description="性别（0：未知；1：男；2：女；）")
    timbre_type: int = Field(0, description="音色类型（0：未设置；1：火山引擎；）")
    timbre: Optional[str] = Field(None, description="语音音色")
    notes: Optional[str] = Field(None, description="备注")
    profile: str = Field(..., description="人物资料")

    class Config:
        from_attributes = True


class CharacterUpdate(BaseModel):
    """更新人物"""
    
    name: Optional[str] = Field(None, description="姓名")
    gender: Optional[int] = Field(None, description="性别（0：未知；1：男；2：女；）")
    avatar: Optional[str] = Field(None, description="头像URL")
    profile: Optional[str] = Field(None, description="人物资料")
    timbre_type: Optional[int] = Field(None, description="音色类型（0：未设置；1：火山引擎；）")
    timbre: Optional[str] = Field(None, description="语音音色")
    notes: Optional[str] = Field(None, description="备注")
    pv_profile: Optional[str] = Field(None, description="人物资料（提示词变量，默认为profile，后台用）")
    pv_ability: Optional[str] = Field(None, description="人物能力（提示词变量，后台用）")
    pv_restriction: Optional[str] = Field(None, description="人物限制（提示词变量，后台用）")
    published: Optional[int] = Field(None, description="是否发布（0：未发布；1：已发布）")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class CharacterResponse(BaseModel):
    """人物响应"""
    
    id: int = Field(..., description="人物ID")
    tenant_id: int = Field(..., description="租户ID")
    name: str = Field(..., description="姓名")
    gender: int = Field(..., description="性别（0：未知；1：男；2：女；）")
    avatar: Optional[str] = Field(None, description="头像URL")
    profile: str = Field(..., description="人物资料")
    timbre_type: int = Field(..., description="音色类型（0：未设置；1：火山引擎；）")
    timbre: Optional[str] = Field(None, description="语音音色")
    notes: Optional[str] = Field(None, description="备注")
    pv_profile: Optional[str] = Field(None, description="人物资料（提示词变量，默认为profile，后台用）")
    pv_ability: Optional[str] = Field(None, description="人物能力（提示词变量，后台用）")
    pv_restriction: Optional[str] = Field(None, description="人物限制（提示词变量，后台用）")
    published: int = Field(..., description="是否发布（0：未发布；1：已发布）")
    ctime: datetime = Field(..., description="创建时间")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class CharacterListItemResponse(BaseModel):
    """人物列表项响应（用于列表展示，不包含后台字段）"""
    
    id: int = Field(..., description="人物ID")
    tenant_id: int = Field(..., description="租户ID")
    name: str = Field(..., description="姓名")
    gender: int = Field(..., description="性别（0：未知；1：男；2：女；）")
    avatar: Optional[str] = Field(None, description="头像URL")
    profile: str = Field(..., description="人物资料")
    timbre_type: int = Field(..., description="音色类型（0：未设置；1：火山引擎；）")
    timbre: Optional[str] = Field(None, description="语音音色")
    notes: Optional[str] = Field(None, description="备注")
    published: int = Field(..., description="是否发布（0：未发布；1：已发布）")
    ctime: datetime = Field(..., description="创建时间")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class CharacterListResponse(BaseModel):
    """人物列表响应"""
    
    total: int
    items: list[CharacterListItemResponse]

    class Config:
        from_attributes = True


class AvatarUploadURL(BaseModel):
    """头像上传URL响应"""
    
    upload_url: str = Field(..., description="上传URL")
    file_path: str = Field(..., description="文件路径")
    file_url: str = Field(..., description="文件访问URL")
    expires: int = Field(..., description="过期时间戳")

    class Config:
        from_attributes = True


class AvatarDeleteRequest(BaseModel):
    """删除头像文件请求"""
    
    file_path: str = Field(..., description="要删除的文件OSS signed URL")

    class Config:
        from_attributes = True
