from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class ClassCreate(BaseModel):
    """创建班级"""
    
    tenant_id: int = Field(..., description="租户ID")
    name: str = Field(..., description="班级名称")
    pic: Optional[str] = Field(None, description="图片URL")
    description: Optional[str] = Field(None, description="描述")
    notes: Optional[str] = Field(None, description="备注")
    btime: datetime = Field(..., description="开始时间")
    etime: datetime = Field(..., description="结束时间")

    class Config:
        from_attributes = True


class ClassUpdate(BaseModel):
    """更新班级"""
    
    name: Optional[str] = Field(None, description="班级名称")
    pic: Optional[str] = Field(None, description="图片URL")
    description: Optional[str] = Field(None, description="描述")
    notes: Optional[str] = Field(None, description="备注")
    btime: Optional[datetime] = Field(None, description="开始时间")
    etime: Optional[datetime] = Field(None, description="结束时间")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class ClassResponse(BaseModel):
    """班级响应"""
    
    id: int = Field(..., description="班级ID")
    tenant_id: int = Field(..., description="租户ID")
    name: str = Field(..., description="班级名称")
    pic: Optional[str] = Field(None, description="图片URL")
    description: Optional[str] = Field(None, description="描述")
    notes: Optional[str] = Field(None, description="备注")
    btime: datetime = Field(..., description="开始时间")
    etime: datetime = Field(..., description="结束时间")
    ctime: datetime = Field(..., description="创建时间")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class ClassListResponse(BaseModel):
    """班级列表响应"""
    
    total: int
    items: list[ClassResponse]

    class Config:
        from_attributes = True


class ClassStudentCreate(BaseModel):
    """创建班级学员关系"""
    
    tenant_id: int = Field(..., description="租户ID")
    cid: int = Field(..., description="班级ID")
    sid: int = Field(..., description="学员ID")
    priority: int = Field(1, description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class ClassStudentUpdate(BaseModel):
    """更新班级学员关系"""
    
    priority: Optional[int] = Field(None, description="展示顺序（从小到大）")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class ClassStudentResponse(BaseModel):
    """班级学员关系响应"""
    
    id: int = Field(..., description="关系ID")
    tenant_id: int = Field(..., description="租户ID")
    cid: int = Field(..., description="班级ID")
    sid: int = Field(..., description="学员ID")
    priority: int = Field(..., description="展示顺序（从小到大）")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class ClassExerciseCreate(BaseModel):
    """创建班级练习关系"""
    
    tenant_id: int = Field(..., description="租户ID")
    cid: int = Field(..., description="班级ID")
    eid: int = Field(..., description="练习ID")
    tid: Optional[int] = Field(None, description="老师ID")
    depend: int = Field(1, description="是否依赖前一个练习结束（0：不依赖；1：依赖）")
    priority: int = Field(1, description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class ClassExerciseUpdate(BaseModel):
    """更新班级练习关系"""
    
    tid: Optional[int] = Field(None, description="老师ID")
    depend: Optional[int] = Field(None, description="是否依赖前一个练习结束（0：不依赖；1：依赖）")
    priority: Optional[int] = Field(None, description="展示顺序（从小到大）")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class ClassExerciseResponse(BaseModel):
    """班级练习关系响应"""
    
    id: int = Field(..., description="关系ID")
    tenant_id: int = Field(..., description="租户ID")
    cid: int = Field(..., description="班级ID")
    eid: int = Field(..., description="练习ID")
    tid: Optional[int] = Field(None, description="老师ID")
    depend: int = Field(..., description="是否依赖前一个练习结束（0：不依赖；1：依赖）")
    priority: int = Field(..., description="展示顺序（从小到大）")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class ClassExerciseWithExerciseInfoResponse(BaseModel):
    """带练习信息的班级练习关系响应"""
    
    id: int = Field(..., description="关系ID")
    tenant_id: int = Field(..., description="租户ID")
    cid: int = Field(..., description="班级ID")
    eid: int = Field(..., description="练习ID")
    tid: Optional[int] = Field(None, description="老师ID")
    depend: int = Field(..., description="是否依赖前一个练习结束（0：不依赖；1：依赖）")
    priority: int = Field(..., description="展示顺序（从小到大）")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")
    
    # 练习相关信息
    title: str = Field(..., description="练习标题")
    type: int = Field(..., description="练习类型（1：作业单；2：角色扮演；）")
    pic: Optional[str] = Field(None, description="练习图片URL")
    intro: Optional[str] = Field(None, description="练习简介")
    
    # 老师相关信息
    tname: Optional[str] = Field(None, description="老师姓名")
    tavatar: Optional[str] = Field(None, description="老师头像签名URL")

    class Config:
        from_attributes = True


class ClassExerciseListResponse(BaseModel):
    """班级练习关系列表响应"""
    
    total: int
    items: list[ClassExerciseWithExerciseInfoResponse]

    class Config:
        from_attributes = True


class ClassExerciseOrderItem(BaseModel):
    """班级练习关系顺序项"""
    
    id: int = Field(..., description="关系ID")
    priority: int = Field(..., description="新的展示顺序（从小到大）")

    class Config:
        from_attributes = True


class ClassExerciseBatchOrderRequest(BaseModel):
    """批量调整班级练习关系顺序请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    class_id: int = Field(..., description="班级ID")
    class_exercises: list[ClassExerciseOrderItem] = Field(..., description="班级练习关系顺序列表")

    class Config:
        from_attributes = True


class ClassExerciseBatchOrderResponse(BaseModel):
    """批量调整班级练习关系顺序响应"""
    
    success_count: int = Field(..., description="成功更新的数量")
    total_count: int = Field(..., description="总请求数量")
    updated_class_exercises: list[ClassExerciseWithExerciseInfoResponse] = Field(..., description="更新后的班级练习关系列表")

    class Config:
        from_attributes = True


class AdminClassCreate(BaseModel):
    """创建跟班关系"""
    
    tenant_id: int = Field(..., description="租户ID")
    aid: int = Field(..., description="教学运营人员ID")
    cid: int = Field(..., description="班级ID")

    class Config:
        from_attributes = True


class AdminClassResponse(BaseModel):
    """跟班关系响应"""
    
    id: int = Field(..., description="关系ID")
    tenant_id: int = Field(..., description="租户ID")
    aid: int = Field(..., description="教学运营人员ID")
    cid: int = Field(..., description="班级ID")

    class Config:
        from_attributes = True


class PicUploadURL(BaseModel):
    """班级图片上传URL响应"""
    
    upload_url: str = Field(..., description="上传URL")
    file_path: str = Field(..., description="文件路径")
    file_url: str = Field(..., description="文件访问URL")
    expires: int = Field(..., description="过期时间（秒）")

    class Config:
        from_attributes = True


class PicDeleteRequest(BaseModel):
    """班级图片删除请求"""
    
    file_path: str = Field(..., description="要删除的文件路径")


class ClassStudentWithStudentInfoResponse(BaseModel):
    """带学员信息的班级学员关系响应"""
    
    id: int = Field(..., description="关系ID")
    tenant_id: int = Field(..., description="租户ID")
    cid: int = Field(..., description="班级ID")
    sid: int = Field(..., description="学员ID")
    priority: int = Field(..., description="展示顺序（从小到大）")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")
    
    # 学员相关信息
    name: str = Field(..., description="学员姓名")
    gender: int = Field(..., description="学员性别（0：未知；1：男；2：女）")
    notes: Optional[str] = Field(None, description="学员备注")

    class Config:
        from_attributes = True


class ClassStudentListResponse(BaseModel):
    """班级学员关系列表响应"""
    
    total: int
    items: list[ClassStudentWithStudentInfoResponse]

    class Config:
        from_attributes = True


class ClassStudentOrderItem(BaseModel):
    """班级学员关系顺序项"""
    
    id: int = Field(..., description="关系ID")
    priority: int = Field(..., description="新的展示顺序（从小到大）")

    class Config:
        from_attributes = True


class ClassStudentBatchOrderRequest(BaseModel):
    """批量调整班级学员关系顺序请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    class_id: int = Field(..., description="班级ID")
    class_students: list[ClassStudentOrderItem] = Field(..., description="班级学员关系顺序列表")

    class Config:
        from_attributes = True


class ClassStudentBatchOrderResponse(BaseModel):
    """批量调整班级学员关系顺序响应"""
    
    success_count: int = Field(..., description="成功更新的数量")
    total_count: int = Field(..., description="总请求数量")
    updated_class_students: list[ClassStudentWithStudentInfoResponse] = Field(..., description="更新后的班级学员关系列表")

    class Config:
        from_attributes = True


class ClassStudentBatchDeleteRequest(BaseModel):
    """批量删除班级学员关系请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    class_id: int = Field(..., description="班级ID")
    class_student_ids: list[int] = Field(..., description="班级学员关系ID列表")

    class Config:
        from_attributes = True


class ClassStudentBatchDeleteResponse(BaseModel):
    """批量删除班级学员关系响应"""
    
    success_count: int = Field(..., description="成功删除的数量")
    total_count: int = Field(..., description="总请求数量")
    deleted_class_student_ids: list[int] = Field(..., description="成功删除的班级学员关系ID列表")

    class Config:
        from_attributes = True


class ClassImportPlanRequest(BaseModel):
    """导入计划请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    class_id: int = Field(..., description="班级ID")
    plan_id: int = Field(..., description="计划ID")
    teacher_id: Optional[int] = Field(None, description="老师ID，可选，将分配给所有导入的练习")

    class Config:
        from_attributes = True


class ClassImportPlanResponse(BaseModel):
    """导入计划响应"""
    
    success_count: int = Field(..., description="成功导入的练习数量")
    total_count: int = Field(..., description="计划中的练习总数")
    imported_class_exercises: list[ClassExerciseWithExerciseInfoResponse] = Field(..., description="导入的班级练习关系列表")
    message: str = Field(..., description="导入结果信息")

    class Config:
        from_attributes = True


class ClassExerciseBatchSetTeacherRequest(BaseModel):
    """批量设置班级练习老师请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    class_id: int = Field(..., description="班级ID")
    teacher_id: Optional[int] = Field(None, description="老师ID，传null则清空所有练习的老师")

    class Config:
        from_attributes = True


class ClassExerciseBatchSetTeacherResponse(BaseModel):
    """批量设置班级练习老师响应"""
    
    success_count: int = Field(..., description="成功更新的数量")
    total_count: int = Field(..., description="总数量")
    message: str = Field(..., description="操作结果信息")

    class Config:
        from_attributes = True


class ClassStudentBatchCreateRequest(BaseModel):
    """批量创建班级学员关系请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    class_id: int = Field(..., description="班级ID")
    student_ids: list[int] = Field(..., description="学员ID列表")

    class Config:
        from_attributes = True


class ClassStudentBatchCreateResponse(BaseModel):
    """批量创建班级学员关系响应"""
    
    success_count: int = Field(..., description="成功创建的数量")
    total_count: int = Field(..., description="总请求数量")
    message: str = Field(..., description="创建结果信息")

    class Config:
        from_attributes = True
