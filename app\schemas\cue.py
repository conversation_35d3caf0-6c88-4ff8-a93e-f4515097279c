from typing import Optional

from pydantic import BaseModel, Field


class CueCreate(BaseModel):
    """创建剧本提示"""
    
    tenant_id: int = Field(..., description="租户ID")
    sid: int = Field(..., description="场景ID")
    cid: int = Field(..., description="人物ID（触发人物）")
    content: str = Field(..., description="触发内容")
    serial: int = Field(1, description="是否顺序发言（0：并行发言；1：顺序发言）")
    priority: int = Field(1, description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class CueUpdate(BaseModel):
    """更新剧本提示"""
    
    content: Optional[str] = Field(None, description="触发内容")
    serial: Optional[int] = Field(None, description="是否顺序发言（0：并行发言；1：顺序发言）")
    priority: Optional[int] = Field(None, description="展示顺序（从小到大）")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class CueResponse(BaseModel):
    """剧本提示响应"""
    
    id: int = Field(..., description="剧本提示ID")
    tenant_id: int = Field(..., description="租户ID")
    sid: int = Field(..., description="场景ID")
    cid: int = Field(..., description="人物ID（触发人物）")
    content: str = Field(..., description="触发内容")
    serial: int = Field(..., description="是否顺序发言（0：并行发言；1：顺序发言）")
    priority: int = Field(..., description="展示顺序（从小到大）")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class CueListResponse(BaseModel):
    """剧本提示列表响应"""
    
    total: int
    items: list[CueResponse]

    class Config:
        from_attributes = True


class CueOrderItem(BaseModel):
    """剧本提示顺序项"""
    
    id: int = Field(..., description="剧本提示ID")
    priority: int = Field(..., description="新的展示顺序")


class CueBatchOrderRequest(BaseModel):
    """批量调整剧本提示顺序请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    sid: int = Field(..., description="场景ID")
    cues: list[CueOrderItem] = Field(..., description="剧本提示顺序列表")

    class Config:
        from_attributes = True


class CueBatchOrderResponse(BaseModel):
    """批量调整剧本提示顺序响应"""
    
    success_count: int = Field(..., description="成功更新的数量")
    total_count: int = Field(..., description="总数量")
    updated_cues: list[CueResponse] = Field(..., description="更新后的剧本提示列表")

    class Config:
        from_attributes = True
