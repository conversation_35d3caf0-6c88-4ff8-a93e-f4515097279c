from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class ExerciseCreate(BaseModel):
    """创建练习"""
    
    tenant_id: int = Field(..., description="租户ID")
    title: str = Field(..., description="标题")
    type: int = Field(1, description="类型（1：作业单；2：角色扮演；）")
    pic: Optional[str] = Field(None, description="图片URL")
    intro: Optional[str] = Field(None, description="简介")
    duration: Optional[int] = Field(None, description="预估练习时长（分钟）")
    version: Optional[str] = Field(None, description="版本")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    notes: Optional[str] = Field(None, description="备注")
    published: int = Field(0, description="是否发布（0：未发布；1：已发布）")

    class Config:
        from_attributes = True


class ExerciseUpdate(BaseModel):
    """更新练习"""
    
    title: Optional[str] = Field(None, description="标题")
    type: Optional[int] = Field(None, description="类型（1：作业单；2：角色扮演；）")
    pic: Optional[str] = Field(None, description="图片URL")
    intro: Optional[str] = Field(None, description="简介")
    duration: Optional[int] = Field(None, description="预估练习时长（分钟）")
    version: Optional[str] = Field(None, description="版本")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    notes: Optional[str] = Field(None, description="备注")
    published: Optional[int] = Field(None, description="是否发布（0：未发布；1：已发布）")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class ExerciseResponse(BaseModel):
    """练习响应"""
    
    id: int = Field(..., description="练习ID")
    tenant_id: int = Field(..., description="租户ID")
    title: str = Field(..., description="标题")
    type: int = Field(..., description="类型（1：作业单；2：角色扮演；）")
    pic: Optional[str] = Field(None, description="图片URL")
    intro: Optional[str] = Field(None, description="简介")
    duration: Optional[int] = Field(None, description="预估练习时长（分钟）")
    version: Optional[str] = Field(None, description="版本")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    notes: Optional[str] = Field(None, description="备注")
    published: int = Field(..., description="是否发布（0：未发布；1：已发布）")
    ctime: datetime = Field(..., description="创建时间")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class ExerciseListResponse(BaseModel):
    """练习列表响应"""
    
    total: int
    items: list[ExerciseResponse]

    class Config:
        from_attributes = True


class PicUploadURL(BaseModel):
    """图片上传URL响应"""
    
    upload_url: str = Field(..., description="上传URL")
    file_path: str = Field(..., description="文件路径")
    file_url: str = Field(..., description="文件访问URL")
    expires: int = Field(..., description="过期时间（秒）")


class PicDeleteRequest(BaseModel):
    """图片删除请求"""
    
    file_path: str = Field(..., description="要删除的文件OSS signed URL")
