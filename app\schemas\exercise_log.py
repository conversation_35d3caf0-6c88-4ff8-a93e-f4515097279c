from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class ExerciseLogCreate(BaseModel):
    """创建练习情况"""
    
    tenant_id: int = Field(..., description="租户ID")
    cid: int = Field(..., description="班级ID")
    sid: int = Field(..., description="学员ID")
    eid: int = Field(..., description="练习ID")
    report: Optional[str] = Field(None, description="整体点评报告URL")
    btime: datetime = Field(..., description="开始时间")
    etime: datetime = Field(..., description="结束时间")
    utime: datetime = Field(..., description="上次更新时间")

    class Config:
        from_attributes = True


class ExerciseLogUpdate(BaseModel):
    """更新练习情况"""
    
    report: Optional[str] = Field(None, description="整体点评报告URL")
    btime: Optional[datetime] = Field(None, description="开始时间")
    etime: Optional[datetime] = Field(None, description="结束时间")
    utime: Optional[datetime] = Field(None, description="上次更新时间")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class ExerciseLogResponse(BaseModel):
    """练习情况响应"""
    
    id: int = Field(..., description="练习情况ID")
    tenant_id: int = Field(..., description="租户ID")
    cid: int = Field(..., description="班级ID")
    sid: int = Field(..., description="学员ID")
    eid: int = Field(..., description="练习ID")
    report: Optional[str] = Field(None, description="整体点评报告URL")
    btime: datetime = Field(..., description="开始时间")
    etime: datetime = Field(..., description="结束时间")
    utime: datetime = Field(..., description="上次更新时间")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class ExerciseLogListResponse(BaseModel):
    """练习情况列表响应"""
    
    total: int
    items: list[ExerciseLogResponse]

    class Config:
        from_attributes = True
