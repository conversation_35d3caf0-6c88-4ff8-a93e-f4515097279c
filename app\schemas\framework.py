from typing import Optional

from pydantic import BaseModel, Field


class FrameworkCreate(BaseModel):
    """创建理论框架"""
    
    tenant_id: int = Field(..., description="租户ID")
    name: str = Field(..., description="理论框架名称")
    description: Optional[str] = Field(None, description="详细描述")
    logo: Optional[str] = Field(None, description="Logo URL")
    priority: int = Field(1, description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class FrameworkUpdate(BaseModel):
    """更新理论框架"""
    
    name: Optional[str] = Field(None, description="理论框架名称")
    description: Optional[str] = Field(None, description="详细描述")
    logo: Optional[str] = Field(None, description="Logo URL")
    priority: Optional[int] = Field(None, description="展示顺序（从小到大）")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class FrameworkResponse(BaseModel):
    """理论框架响应"""
    
    id: int = Field(..., description="理论框架ID")
    tenant_id: int = Field(..., description="租户ID")
    name: str = Field(..., description="理论框架名称")
    description: Optional[str] = Field(None, description="详细描述")
    logo: Optional[str] = Field(None, description="Logo URL")
    priority: int = Field(..., description="展示顺序（从小到大）")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class FrameworkListResponse(BaseModel):
    """理论框架列表响应"""
    
    total: int
    items: list[FrameworkResponse]

    class Config:
        from_attributes = True


class LogoUploadURL(BaseModel):
    """Logo上传URL响应"""
    
    upload_url: str = Field(..., description="上传URL")
    file_path: str = Field(..., description="文件路径")
    file_url: str = Field(..., description="文件访问URL")
    expires: int = Field(..., description="过期时间戳")

    class Config:
        from_attributes = True


class LogoDeleteRequest(BaseModel):
    """删除Logo文件请求"""
    
    file_path: str = Field(..., description="要删除的文件OSS signed URL")

    class Config:
        from_attributes = True


class FrameworkOrderItem(BaseModel):
    """框架顺序项"""
    
    id: int = Field(..., description="框架ID")
    priority: int = Field(..., description="新的展示顺序（从小到大）")

    class Config:
        from_attributes = True


class FrameworkBatchOrderRequest(BaseModel):
    """批量调整框架顺序请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    frameworks: list[FrameworkOrderItem] = Field(..., description="框架顺序列表")

    class Config:
        from_attributes = True


class FrameworkBatchOrderResponse(BaseModel):
    """批量调整框架顺序响应"""
    
    success_count: int = Field(..., description="成功更新的框架数量")
    total_count: int = Field(..., description="总框架数量")
    updated_frameworks: list[FrameworkResponse] = Field(..., description="更新后的框架列表")

    class Config:
        from_attributes = True 