from typing import Optional

from pydantic import BaseModel, Field


class LineCreate(BaseModel):
    """创建台词"""
    
    tenant_id: int = Field(..., description="租户ID")
    cueid: int = Field(..., description="剧本提示ID")
    cid: int = Field(..., description="人物ID（发言人物）")
    pv_topic: str = Field(..., description="发言主题（提示词变量，后台用）")
    pv_ability: str = Field(..., description="人物能力（提示词变量，后台用）")
    pv_restriction: str = Field(..., description="人物限制（提示词变量，后台用）")
    priority: int = Field(1, description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class LineUpdate(BaseModel):
    """更新台词"""
    
    pv_topic: Optional[str] = Field(None, description="发言主题（提示词变量，后台用）")
    pv_ability: Optional[str] = Field(None, description="人物能力（提示词变量，后台用）")
    pv_restriction: Optional[str] = Field(None, description="人物限制（提示词变量，后台用）")
    priority: Optional[int] = Field(None, description="展示顺序（从小到大）")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class LineResponse(BaseModel):
    """台词响应"""
    
    id: int = Field(..., description="台词ID")
    tenant_id: int = Field(..., description="租户ID")
    cueid: int = Field(..., description="剧本提示ID")
    cid: int = Field(..., description="人物ID（发言人物）")
    pv_topic: str = Field(..., description="发言主题（提示词变量，后台用）")
    pv_ability: str = Field(..., description="人物能力（提示词变量，后台用）")
    pv_restriction: str = Field(..., description="人物限制（提示词变量，后台用）")
    priority: int = Field(..., description="展示顺序（从小到大）")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class LineListResponse(BaseModel):
    """台词列表响应"""
    
    total: int
    items: list[LineResponse]

    class Config:
        from_attributes = True


class LineOrderItem(BaseModel):
    """台词顺序项"""
    
    id: int = Field(..., description="台词ID")
    priority: int = Field(..., description="新的展示顺序")


class LineBatchOrderRequest(BaseModel):
    """批量调整台词顺序请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    lines: list[LineOrderItem] = Field(..., description="台词顺序列表")

    class Config:
        from_attributes = True


class LineBatchOrderResponse(BaseModel):
    """批量调整台词顺序响应"""
    
    success_count: int = Field(..., description="成功更新的数量")
    total_count: int = Field(..., description="总数量")
    updated_lines: list[LineResponse] = Field(..., description="更新后的台词列表")

    class Config:
        from_attributes = True


class LineBatchMoveRequest(BaseModel):
    """批量移动台词请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    target_cueid: int = Field(..., description="目标剧本提示ID")
    line_ids: list[int] = Field(..., description="要移动的台词ID列表")

    class Config:
        from_attributes = True


class LineBatchMoveResponse(BaseModel):
    """批量移动台词响应"""
    
    success_count: int = Field(..., description="成功移动的数量")
    total_count: int = Field(..., description="总数量")
    moved_line_ids: list[int] = Field(..., description="移动的台词ID列表")
    target_cueid: int = Field(..., description="目标剧本提示ID")

    class Config:
        from_attributes = True
