from typing import Optional

from pydantic import BaseModel, Field


class ModuleCreate(BaseModel):
    """创建理论模块"""
    
    tenant_id: int = Field(..., description="租户ID")
    fid: int = Field(..., description="理论框架ID")
    name: str = Field(..., description="理论模块名称")
    description: Optional[str] = Field(None, description="详细描述")
    priority: Optional[int] = Field(None, description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class ModuleUpdate(BaseModel):
    """更新理论模块"""
    
    name: Optional[str] = Field(None, description="理论模块名称")
    description: Optional[str] = Field(None, description="详细描述")
    priority: Optional[int] = Field(None, description="展示顺序（从小到大）")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class ModuleResponse(BaseModel):
    """理论模块响应"""
    
    id: int = Field(..., description="理论模块ID")
    tenant_id: int = Field(..., description="租户ID")
    fid: int = Field(..., description="理论框架ID")
    name: str = Field(..., description="理论模块名称")
    description: Optional[str] = Field(None, description="详细描述")
    priority: int = Field(..., description="展示顺序（从小到大）")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class ModuleListResponse(BaseModel):
    """理论模块列表响应"""
    
    total: int
    items: list[ModuleResponse]

    class Config:
        from_attributes = True


class ModuleOrderItem(BaseModel):
    """模块顺序项"""
    
    id: int = Field(..., description="模块ID")
    priority: int = Field(..., description="新的展示顺序（从小到大）")

    class Config:
        from_attributes = True


class ModuleBatchOrderRequest(BaseModel):
    """批量调整模块顺序请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    modules: list[ModuleOrderItem] = Field(..., description="模块顺序列表")

    class Config:
        from_attributes = True


class ModuleBatchOrderResponse(BaseModel):
    """批量调整模块顺序响应"""
    
    success_count: int = Field(..., description="成功更新的模块数量")
    total_count: int = Field(..., description="总模块数量")
    updated_modules: list[ModuleResponse] = Field(..., description="更新后的模块列表")

    class Config:
        from_attributes = True
