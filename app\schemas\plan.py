from typing import Optional

from pydantic import BaseModel, Field


class PlanCreate(BaseModel):
    """创建计划"""
    
    tenant_id: int = Field(..., description="租户ID")
    name: str = Field(..., description="计划名称")
    pic: Optional[str] = Field(None, description="图片URL")
    description: Optional[str] = Field(None, description="描述")
    notes: Optional[str] = Field(None, description="备注")

    class Config:
        from_attributes = True


class PlanUpdate(BaseModel):
    """更新计划"""
    
    name: Optional[str] = Field(None, description="计划名称")
    pic: Optional[str] = Field(None, description="图片URL")
    description: Optional[str] = Field(None, description="描述")
    notes: Optional[str] = Field(None, description="备注")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class PlanResponse(BaseModel):
    """计划响应"""
    
    id: int = Field(..., description="计划ID")
    tenant_id: int = Field(..., description="租户ID")
    name: str = Field(..., description="计划名称")
    pic: Optional[str] = Field(None, description="图片URL")
    description: Optional[str] = Field(None, description="描述")
    notes: Optional[str] = Field(None, description="备注")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class PlanListResponse(BaseModel):
    """计划列表响应"""
    
    total: int
    items: list[PlanResponse]

    class Config:
        from_attributes = True


class PicUploadURL(BaseModel):
    """计划图片上传URL响应"""
    
    upload_url: str = Field(..., description="上传URL")
    file_path: str = Field(..., description="文件路径")
    file_url: str = Field(..., description="文件访问URL")
    expires: int = Field(..., description="过期时间戳")

    class Config:
        from_attributes = True


class PicDeleteRequest(BaseModel):
    """计划图片删除请求"""
    
    file_path: str = Field(..., description="要删除的文件OSS signed URL")

    class Config:
        from_attributes = True


# ===== PlanExercise 相关 Schema =====

class PlanExerciseCreate(BaseModel):
    """创建计划练习关系"""
    
    tenant_id: int = Field(..., description="租户ID")
    pid: int = Field(..., description="计划ID")
    eid: int = Field(..., description="练习ID")
    depend: int = Field(1, description="是否依赖前一个练习结束（0：不依赖；1：依赖）")
    priority: int = Field(1, description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class PlanExerciseUpdate(BaseModel):
    """更新计划练习关系"""
    
    depend: Optional[int] = Field(None, description="是否依赖前一个练习结束（0：不依赖；1：依赖）")

    class Config:
        from_attributes = True


class PlanExerciseResponse(BaseModel):
    """计划练习关系响应"""
    
    id: int = Field(..., description="关系ID")
    tenant_id: int = Field(..., description="租户ID")
    pid: int = Field(..., description="计划ID")
    eid: int = Field(..., description="练习ID")
    depend: int = Field(..., description="是否依赖前一个练习结束（0：不依赖；1：依赖）")
    priority: int = Field(..., description="展示顺序（从小到大）")
    
    # 练习相关信息
    title: str = Field(..., description="练习标题")
    type: int = Field(..., description="练习类型（1：作业单；2：角色扮演；）")
    pic: Optional[str] = Field(None, description="练习图片URL")
    intro: Optional[str] = Field(None, description="练习简介")

    class Config:
        from_attributes = True


class PlanExerciseListResponse(BaseModel):
    """计划练习关系列表响应"""
    
    total: int
    items: list[PlanExerciseResponse]

    class Config:
        from_attributes = True


class PlanExerciseOrderItem(BaseModel):
    """计划练习关系顺序项"""
    
    id: int = Field(..., description="关系ID")
    priority: int = Field(..., description="新的展示顺序（从小到大）")

    class Config:
        from_attributes = True


class PlanExerciseBatchOrderRequest(BaseModel):
    """批量调整计划练习关系顺序请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    plan_id: int = Field(..., description="计划ID")
    plan_exercises: list[PlanExerciseOrderItem] = Field(..., description="计划练习关系顺序列表")

    class Config:
        from_attributes = True


class PlanExerciseBatchOrderResponse(BaseModel):
    """批量调整计划练习关系顺序响应"""
    
    success_count: int = Field(..., description="成功更新的数量")
    total_count: int = Field(..., description="总请求数量")
    updated_plan_exercises: list[PlanExerciseResponse] = Field(..., description="更新后的计划练习关系列表")

    class Config:
        from_attributes = True
