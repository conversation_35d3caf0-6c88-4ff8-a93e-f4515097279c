from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class QuestionCreate(BaseModel):
    """创建问题"""
    
    tenant_id: int = Field(..., description="租户ID")
    title: str = Field(..., description="问题标题")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    notes: Optional[str] = Field(None, description="备注")
    pv_skills: Optional[str] = Field(None, description="点评能力（提示词变量，后台用）")
    pv_rules: Optional[str] = Field(None, description="点评细则（提示词变量，后台用）")
    pv_formats: Optional[str] = Field(None, description="点评格式（提示词变量，后台用）")

    class Config:
        from_attributes = True


class QuestionUpdate(BaseModel):
    """更新问题"""
    
    title: Optional[str] = Field(None, description="问题标题")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    notes: Optional[str] = Field(None, description="备注")
    pv_skills: Optional[str] = Field(None, description="点评能力（提示词变量，后台用）")
    pv_rules: Optional[str] = Field(None, description="点评细则（提示词变量，后台用）")
    pv_formats: Optional[str] = Field(None, description="点评格式（提示词变量，后台用）")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class QuestionResponse(BaseModel):
    """问题响应"""
    
    id: int = Field(..., description="问题ID")
    tenant_id: int = Field(..., description="租户ID")
    title: str = Field(..., description="问题标题")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    notes: Optional[str] = Field(None, description="备注")
    pv_skills: Optional[str] = Field(None, description="点评能力（提示词变量，后台用）")
    pv_rules: Optional[str] = Field(None, description="点评细则（提示词变量，后台用）")
    pv_formats: Optional[str] = Field(None, description="点评格式（提示词变量，后台用）")
    ctime: datetime = Field(..., description="创建时间")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class QuestionListItemResponse(BaseModel):
    """问题列表项响应（简化版）"""
    
    id: int = Field(..., description="问题ID")
    tenant_id: int = Field(..., description="租户ID")
    title: str = Field(..., description="问题标题")
    notes: Optional[str] = Field(None, description="备注")
    ctime: datetime = Field(..., description="创建时间")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class QuestionListResponse(BaseModel):
    """问题列表响应"""
    
    total: int
    items: list[QuestionListItemResponse]

    class Config:
        from_attributes = True


# 问题作答指南相关Schema
class QuestionGuideCreate(BaseModel):
    """创建问题作答指南"""
    
    tenant_id: int = Field(..., description="租户ID")
    qid: int = Field(..., description="问题ID")
    title: str = Field(..., description="指南标题")
    details: str = Field(..., description="指南详情")
    priority: int = Field(1, description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class QuestionGuideUpdate(BaseModel):
    """更新问题作答指南"""
    
    title: Optional[str] = Field(None, description="指南标题")
    details: Optional[str] = Field(None, description="指南详情")
    priority: Optional[int] = Field(None, description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class QuestionGuideResponse(BaseModel):
    """问题作答指南响应"""
    
    id: int = Field(..., description="作答指南ID")
    tenant_id: int = Field(..., description="租户ID")
    qid: int = Field(..., description="问题ID")
    title: str = Field(..., description="指南标题")
    details: str = Field(..., description="指南详情")
    priority: int = Field(..., description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class QuestionGuideListResponse(BaseModel):
    """问题作答指南列表响应"""
    
    total: int
    items: list[QuestionGuideResponse]

    class Config:
        from_attributes = True


class QuestionGuideOrderItem(BaseModel):
    """问题作答指南顺序项"""
    
    id: int = Field(..., description="作答指南ID")
    priority: int = Field(..., description="新的展示顺序（从小到大）")

    class Config:
        from_attributes = True


class QuestionGuideBatchOrderRequest(BaseModel):
    """批量调整问题作答指南顺序请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    guides: list[QuestionGuideOrderItem] = Field(..., description="作答指南顺序列表")

    class Config:
        from_attributes = True


class QuestionGuideBatchOrderResponse(BaseModel):
    """批量调整问题作答指南顺序响应"""
    
    success_count: int = Field(..., description="成功更新的作答指南数量")
    total_count: int = Field(..., description="总作答指南数量")
    updated_guides: list[QuestionGuideResponse] = Field(..., description="更新后的作答指南列表")

    class Config:
        from_attributes = True


# 问题-理论模块关系相关Schema
class QuestionModuleCreate(BaseModel):
    """创建问题-理论模块关系"""
    
    tenant_id: int = Field(..., description="租户ID")
    qid: int = Field(..., description="问题ID")
    mid: int = Field(..., description="理论模块ID")

    class Config:
        from_attributes = True


class QuestionModuleBatchCreate(BaseModel):
    """批量创建问题-理论模块关系"""
    
    items: list[QuestionModuleCreate] = Field(..., description="问题-理论模块关系列表")

    class Config:
        from_attributes = True


class QuestionModuleBatchDelete(BaseModel):
    """批量删除问题-理论模块关系"""
    
    tenant_id: int = Field(..., description="租户ID")
    relation_ids: Optional[list[int]] = Field(None, description="关系ID列表，与question_id二选一")
    question_id: Optional[int] = Field(None, description="问题ID，删除该问题的所有关系，与relation_ids二选一")

    class Config:
        from_attributes = True


class QuestionModuleBatchUpdate(BaseModel):
    """批量更新问题-理论模块关系"""
    
    tenant_id: int = Field(..., description="租户ID")
    question_id: int = Field(..., description="问题ID")
    module_ids: list[int] = Field(..., description="理论模块ID列表")

    class Config:
        from_attributes = True


class QuestionModuleResponse(BaseModel):
    """问题-理论模块关系响应"""
    
    id: int = Field(..., description="关系ID")
    tenant_id: int = Field(..., description="租户ID")
    qid: int = Field(..., description="问题ID")
    mid: int = Field(..., description="理论模块ID")
    module_name: str = Field(..., description="理论模块名称")

    class Config:
        from_attributes = True


class QuestionModuleBatchResponse(BaseModel):
    """问题-理论模块关系批量操作响应"""
    
    success_count: int = Field(..., description="成功操作的数量")
    failed_count: int = Field(0, description="失败操作的数量")

    class Config:
        from_attributes = True


class QuestionModuleUpdateResponse(BaseModel):
    """问题-理论模块关系更新响应"""
    
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作结果信息")

    class Config:
        from_attributes = True


# 问题-主题关系相关Schema
class QuestionSubjectBatchUpdate(BaseModel):
    """批量更新问题-主题关系"""
    
    tenant_id: int = Field(..., description="租户ID")
    question_id: int = Field(..., description="问题ID")
    subject_ids: list[int] = Field(..., description="主题ID列表")

    class Config:
        from_attributes = True


class QuestionSubjectResponse(BaseModel):
    """问题-主题关系响应"""
    
    id: int = Field(..., description="关系ID")
    tenant_id: int = Field(..., description="租户ID")
    qid: int = Field(..., description="问题ID")
    sid: int = Field(..., description="主题ID")
    subject_name: str = Field(..., description="主题名称")

    class Config:
        from_attributes = True


class QuestionSubjectUpdateResponse(BaseModel):
    """问题-主题关系更新响应"""
    
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作结果信息")

    class Config:
        from_attributes = True


# 问题关联的理论模块和主题合并响应
class QuestionRelationResponse(BaseModel):
    """问题关联的理论模块和主题响应"""
    
    modules: list[QuestionModuleResponse] = Field(default_factory=list, description="关联的理论模块列表")
    subjects: list[QuestionSubjectResponse] = Field(default_factory=list, description="关联的主题列表")
    module_total: int = Field(..., description="理论模块总数")
    subject_total: int = Field(..., description="主题总数")

    class Config:
        from_attributes = True
