from typing import Optional
from datetime import datetime

from pydantic import BaseModel, Field


class SceneCreate(BaseModel):
    """创建场景"""
    
    tenant_id: int = Field(..., description="租户ID")
    title: str = Field(..., description="标题")
    intro: Optional[str] = Field(None, description="简介")
    duration: Optional[int] = Field(None, description="预估练习时长（分钟）")
    version: Optional[str] = Field(None, description="版本")
    notes: Optional[str] = Field(None, description="备注")

    class Config:
        from_attributes = True


class SceneUpdate(BaseModel):
    """更新场景"""
    
    title: Optional[str] = Field(None, description="标题")
    intro: Optional[str] = Field(None, description="简介")
    duration: Optional[int] = Field(None, description="预估练习时长（分钟）")
    version: Optional[str] = Field(None, description="版本")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    notes: Optional[str] = Field(None, description="备注")
    published: Optional[int] = Field(None, description="是否发布（0：未发布；1：已发布）")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")
    pv_scripts: Optional[str] = Field(None, description="剧情脚本（提示词变量，后台用）")

    class Config:
        from_attributes = True


class SceneResponse(BaseModel):
    """场景响应"""
    
    id: int = Field(..., description="场景ID")
    tenant_id: int = Field(..., description="租户ID")
    eid: int = Field(..., description="练习ID")
    title: str = Field(..., description="标题")
    type: int = Field(..., description="类型（1：作业单；2：角色扮演；）")
    intro: Optional[str] = Field(None, description="简介")
    duration: Optional[int] = Field(None, description="预估练习时长（分钟）")
    version: Optional[str] = Field(None, description="版本")
    pic: Optional[str] = Field(None, description="图片URL")
    bgtext: Optional[str] = Field(None, description="背景文字")
    notes: Optional[str] = Field(None, description="备注")
    published: int = Field(..., description="是否发布（0：未发布；1：已发布）")
    ctime: datetime = Field(..., description="创建时间")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")
    pv_scripts: str = Field(..., description="剧情脚本（提示词变量，后台用）")

    class Config:
        from_attributes = True


class SceneListItemResponse(BaseModel):
    """场景列表项响应"""
    
    id: int = Field(..., description="场景ID")
    tenant_id: int = Field(..., description="租户ID")
    eid: int = Field(..., description="练习ID")
    title: str = Field(..., description="标题")
    type: int = Field(..., description="类型（1：作业单；2：角色扮演；）")
    intro: Optional[str] = Field(None, description="简介")
    duration: Optional[int] = Field(None, description="预估练习时长（分钟）")
    version: Optional[str] = Field(None, description="版本")
    pic: Optional[str] = Field(None, description="图片URL")
    notes: Optional[str] = Field(None, description="备注")
    published: int = Field(..., description="是否发布（0：未发布；1：已发布）")
    ctime: datetime = Field(..., description="创建时间")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class SceneListResponse(BaseModel):
    """场景列表响应"""
    
    total: int
    items: list[SceneListItemResponse]

    class Config:
        from_attributes = True


# 场景-人物关系相关Schema
class SceneCharacterResponse(BaseModel):
    """场景人物响应"""
    
    id: int = Field(..., description="关系ID")
    cid: int = Field(..., description="人物ID")
    name: str = Field(..., description="姓名")
    gender: int = Field(..., description="性别（0：未知；1：男；2：女；）")
    avatar: Optional[str] = Field(None, description="头像URL")
    profile: str = Field(..., description="人物资料")
    played: int = Field(..., description="是否为学员扮演（0：否；1：是）")

    class Config:
        from_attributes = True


class SceneCharacterCreate(BaseModel):
    """创建场景人物关系"""
    
    tenant_id: int = Field(..., description="租户ID")
    sid: int = Field(..., description="场景ID")
    cid: int = Field(..., description="人物ID")
    played: int = Field(0, description="是否为学员扮演（0：否；1：是）")
    priority: int = Field(1, description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class SceneCharacterUpdate(BaseModel):
    """更新场景人物关系"""
    
    tenant_id: int = Field(..., description="租户ID")
    played: int = Field(..., description="是否为学员扮演（0：否；1：是）")

    class Config:
        from_attributes = True


class SceneCharacterOrderItem(BaseModel):
    """场景人物顺序项"""
    
    id: int = Field(..., description="关系ID")
    priority: int = Field(..., description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class SceneCharacterBatchOrderRequest(BaseModel):
    """批量调整场景人物顺序请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    sid: int = Field(..., description="场景ID")
    characters: list[SceneCharacterOrderItem] = Field(..., description="人物顺序列表")

    class Config:
        from_attributes = True


class SceneCharacterBatchOrderResponse(BaseModel):
    """批量调整场景人物顺序响应"""
    
    success_count: int = Field(..., description="成功更新的数量")
    total_count: int = Field(..., description="总数量")
    updated_characters: list[SceneCharacterResponse] = Field(..., description="更新后的人物列表")

    class Config:
        from_attributes = True


class SceneCharacterBatchDeleteRequest(BaseModel):
    """批量删除场景人物关系请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    sid: int = Field(..., description="场景ID")
    character_ids: list[int] = Field(..., description="关系ID列表")

    class Config:
        from_attributes = True


class SceneCharacterBatchDeleteResponse(BaseModel):
    """批量删除场景人物关系响应"""
    
    success_count: int = Field(..., description="成功删除的数量")
    total_count: int = Field(..., description="总数量")
    deleted_character_ids: list[int] = Field(..., description="删除的关系ID列表")

    class Config:
        from_attributes = True


# 场景指南相关Schema
class SceneGuideResponse(BaseModel):
    """场景指南响应"""
    
    id: int = Field(..., description="指南ID")
    tenant_id: int = Field(..., description="租户ID")
    sid: int = Field(..., description="场景ID")
    title: str = Field(..., description="指南标题")
    details: str = Field(..., description="指南详情")
    priority: int = Field(..., description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class SceneGuideListResponse(BaseModel):
    """场景指南列表响应"""
    
    total: int
    items: list[SceneGuideResponse]

    class Config:
        from_attributes = True


class SceneGuideCreate(BaseModel):
    """创建场景指南"""
    
    tenant_id: int = Field(..., description="租户ID")
    sid: int = Field(..., description="场景ID")
    title: str = Field(..., description="指南标题")
    details: str = Field(..., description="指南详情")
    priority: int = Field(1, description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class SceneGuideUpdate(BaseModel):
    """更新场景指南"""
    
    title: Optional[str] = Field(None, description="指南标题")
    details: Optional[str] = Field(None, description="指南详情")
    priority: Optional[int] = Field(None, description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class SceneGuideOrderItem(BaseModel):
    """场景指南顺序项"""
    
    id: int = Field(..., description="指南ID")
    priority: int = Field(..., description="新的展示顺序（从小到大）")

    class Config:
        from_attributes = True


class SceneGuideBatchOrderRequest(BaseModel):
    """批量调整场景指南顺序请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    guides: list[SceneGuideOrderItem] = Field(..., description="指南顺序列表")

    class Config:
        from_attributes = True


class SceneGuideBatchOrderResponse(BaseModel):
    """批量调整场景指南顺序响应"""
    
    success_count: int = Field(..., description="成功更新的指南数量")
    total_count: int = Field(..., description="总指南数量")
    updated_guides: list[SceneGuideResponse] = Field(..., description="更新后的指南列表")

    class Config:
        from_attributes = True
