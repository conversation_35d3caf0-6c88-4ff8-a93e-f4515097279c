from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class SceneSpeechCreate(BaseModel):
    """创建场景语音"""
    
    tenant_id: int = Field(..., description="租户ID")
    elid: int = Field(..., description="练习情况ID")
    cid: int = Field(..., description="角色ID")
    played: int = Field(0, description="是否是学员扮演（0：否；1：是）")
    content: str = Field(..., description="发言内容")
    to_cids: Optional[str] = Field(None, description="@列表（人物ID列表，用逗号分隔，可为空）")

    class Config:
        from_attributes = True


class SceneSpeechUpdate(BaseModel):
    """更新场景语音"""
    
    content: Optional[str] = Field(None, description="发言内容")
    to_cids: Optional[str] = Field(None, description="@列表（人物ID列表，用逗号分隔，可为空）")

    class Config:
        from_attributes = True


class SceneSpeechResponse(BaseModel):
    """场景语音响应"""
    
    id: int = Field(..., description="场景练习情况ID")
    tenant_id: int = Field(..., description="租户ID")
    elid: int = Field(..., description="练习情况ID")
    cid: int = Field(..., description="角色ID")
    played: int = Field(..., description="是否是学员扮演（0：否；1：是）")
    content: str = Field(..., description="发言内容")
    to_cids: Optional[str] = Field(None, description="@列表（人物ID列表，用逗号分隔，可为空）")
    ctime: datetime = Field(..., description="发言时间")

    class Config:
        from_attributes = True


class SceneSpeechListResponse(BaseModel):
    """场景语音列表响应"""
    
    total: int
    items: list[SceneSpeechResponse]

    class Config:
        from_attributes = True
