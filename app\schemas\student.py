from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class StudentCreate(BaseModel):
    """创建学员"""
    
    tenant_id: int = Field(..., description="租户ID")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    name: str = Field(..., description="姓名")
    gender: int = Field(0, description="性别（0：未知；1：男；2：女；）")
    notes: Optional[str] = Field(None, description="备注")

    class Config:
        from_attributes = True


class StudentUpdate(BaseModel):
    """更新学员"""
    
    name: Optional[str] = Field(None, description="姓名")
    gender: Optional[int] = Field(None, description="性别（0：未知；1：男；2：女；）")
    notes: Optional[str] = Field(None, description="备注")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class StudentResponse(BaseModel):
    """学员响应"""
    
    id: int = Field(..., description="学员ID")
    tenant_id: int = Field(..., description="租户ID")
    uid: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    name: str = Field(..., description="姓名")
    gender: int = Field(..., description="性别（0：未知；1：男；2：女；）")
    notes: Optional[str] = Field(None, description="备注")
    ctime: datetime = Field(..., description="创建时间")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class StudentListResponse(BaseModel):
    """学员列表响应"""
    
    total: int
    items: list[StudentResponse]

    class Config:
        from_attributes = True
