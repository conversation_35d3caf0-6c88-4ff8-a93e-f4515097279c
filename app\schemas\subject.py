from typing import Optional

from pydantic import BaseModel, Field


class SubjectCreate(BaseModel):
    """创建主题"""
    
    tenant_id: int = Field(..., description="租户ID")
    name: str = Field(..., description="主题名称（如：项目管理、领导力、沟通技巧等）")

    class Config:
        from_attributes = True


class SubjectUpdate(BaseModel):
    """更新主题"""
    
    name: Optional[str] = Field(None, description="主题名称（如：项目管理、领导力、沟通技巧等）")

    class Config:
        from_attributes = True


class SubjectResponse(BaseModel):
    """主题响应"""
    
    id: int = Field(..., description="主题ID")
    tenant_id: int = Field(..., description="租户ID")
    name: str = Field(..., description="主题名称（如：项目管理、领导力、沟通技巧等）")

    class Config:
        from_attributes = True


class SubjectListResponse(BaseModel):
    """主题列表响应"""
    
    total: int
    items: list[SubjectResponse]

    class Config:
        from_attributes = True
