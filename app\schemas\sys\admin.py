from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class SysAdminCreate(BaseModel):
    """创建系统管理员"""
    
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    name: str = Field(..., description="姓名")
    role: int = Field(1, description="角色（0：超级管理员；1：管理员）")

    class Config:
        from_attributes = True


class SysAdminUpdate(BaseModel):
    """更新系统管理员"""
    
    username: Optional[str] = Field(None, description="用户名")
    password: Optional[str] = Field(None, description="密码")
    name: Optional[str] = Field(None, description="姓名")
    role: Optional[int] = Field(None, description="角色（0：超级管理员；1：管理员）")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class SysAdminResponse(BaseModel):
    """系统管理员响应"""
    
    id: int = Field(..., description="管理员ID")
    uid: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    name: str = Field(..., description="姓名")
    role: int = Field(..., description="角色（0：超级管理员；1：管理员）")
    token_version: int = Field(..., description="token版本")
    ctime: datetime = Field(..., description="创建时间")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class SysAdminListResponse(BaseModel):
    """系统管理员列表响应"""
    
    total: int
    items: list[SysAdminResponse]

    class Config:
        from_attributes = True
