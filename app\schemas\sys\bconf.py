from typing import Optional, Any

from pydantic import BaseModel, Field, model_validator


class BconfCreate(BaseModel):
    """创建机器人设置"""
    
    key: str = Field(..., description="AI设置key")
    bid: int = Field(..., description="机器人ID")
    notes: Optional[str] = Field(None, description="备注")

    class Config:
        from_attributes = True


class BconfUpdate(BaseModel):
    """更新机器人设置"""
    
    key: Optional[str] = Field(None, description="AI设置key")
    bid: Optional[int] = Field(None, description="机器人ID")
    notes: Optional[str] = Field(None, description="备注")

    class Config:
        from_attributes = True


class BconfResponse(BaseModel):
    """机器人设置响应"""
    
    key: str = Field(..., description="AI设置key")
    bid: int = Field(..., description="机器人ID")
    notes: Optional[str] = Field(None, description="备注")
    # 机器人信息
    bot_name: str = Field(..., description="机器人名称")

    @model_validator(mode='before')
    @classmethod
    def extract_bot_info(cls, data: Any) -> dict:
        """从SysBconf对象中提取机器人信息"""
        if hasattr(data, '__dict__'):
            # 如果是SQLAlchemy对象
            result = {
                'key': data.key,
                'bid': data.bid,
                'notes': data.notes,
            }
            
            # 从关联的bot对象获取信息
            if hasattr(data, 'bot') and data.bot:
                result.update({
                    'bot_name': data.bot.name,
                })
            else:
                # 如果没有关联的bot，使用默认值
                result.update({
                    'bot_name': "",
                })
            
            return result
        
        return data

    class Config:
        from_attributes = True


class BconfListResponse(BaseModel):
    """机器人设置列表响应"""
    
    total: int
    items: list[BconfResponse]

    class Config:
        from_attributes = True
