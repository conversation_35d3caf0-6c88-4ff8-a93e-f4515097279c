from typing import Optional

from pydantic import BaseModel, Field


class BotCreate(BaseModel):
    """创建机器人"""
    
    name: str = Field(..., description="名称")
    api_endpoint: Optional[str] = Field(None, description="API 路径")
    api_key: Optional[str] = Field(None, description="API key")
    sys_prompt: Optional[str] = Field(None, description="系统提示词模板")
    notes: Optional[str] = Field(None, description="备注")
    active: int = Field(1, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class BotUpdate(BaseModel):
    """更新机器人"""
    
    name: Optional[str] = Field(None, description="名称")
    api_endpoint: Optional[str] = Field(None, description="API 路径")
    api_key: Optional[str] = Field(None, description="API key")
    sys_prompt: Optional[str] = Field(None, description="系统提示词模板")
    notes: Optional[str] = Field(None, description="备注")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class BotResponse(BaseModel):
    """机器人响应"""
    
    id: int = Field(..., description="机器人ID")
    name: str = Field(..., description="名称")
    api_endpoint: Optional[str] = Field(None, description="API 路径")
    api_key: Optional[str] = Field(None, description="API key")
    sys_prompt: Optional[str] = Field(None, description="系统提示词模板")
    notes: Optional[str] = Field(None, description="备注")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class BotListResponse(BaseModel):
    """机器人列表响应"""
    
    total: int
    items: list[BotResponse]

    class Config:
        from_attributes = True
