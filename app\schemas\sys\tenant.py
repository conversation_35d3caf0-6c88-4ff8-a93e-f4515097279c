from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class TenantCreate(BaseModel):
    """创建租户"""
    
    code: str = Field(..., description="租户代号")
    name: str = Field(..., description="租户名称")
    notes: Optional[str] = Field(None, description="租户备注")
    active: int = Field(1, description="状态（0：禁用；1：启用）")

    class Config:
        from_attributes = True


class TenantUpdate(BaseModel):
    """更新租户"""
    
    code: Optional[str] = Field(None, description="租户代号")
    name: Optional[str] = Field(None, description="租户名称")
    notes: Optional[str] = Field(None, description="租户备注")
    active: Optional[int] = Field(None, description="状态（0：禁用；1：启用）")

    class Config:
        from_attributes = True


class TenantResponse(BaseModel):
    """租户响应"""
    
    id: int = Field(..., description="租户ID")
    code: str = Field(..., description="租户代号")
    name: str = Field(..., description="租户名称")
    notes: Optional[str] = Field(None, description="租户备注")
    ctime: datetime = Field(..., description="创建时间")
    active: int = Field(..., description="状态（0：禁用；1：启用）")

    class Config:
        from_attributes = True


class TenantListResponse(BaseModel):
    """租户列表响应"""
    
    total: int
    items: list[TenantResponse]

    class Config:
        from_attributes = True
