from datetime import datetime
from typing import Optional, Any

from pydantic import BaseModel, Field, model_validator


class TntAdminCreate(BaseModel):
    """创建租户管理员"""
    
    tenant_id: int = Field(..., description="租户ID")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    name: str = Field(..., description="姓名")
    role: int = Field(1, description="角色（1：管理员, 2：教学运营人员）")

    class Config:
        from_attributes = True


class TntAdminUpdate(BaseModel):
    """更新租户管理员"""
    
    username: Optional[str] = Field(None, description="用户名")
    password: Optional[str] = Field(None, description="密码")
    name: Optional[str] = Field(None, description="姓名")
    role: Optional[int] = Field(None, description="角色（1：管理员, 2：教学运营人员）")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class TntAdminResponse(BaseModel):
    """租户管理员响应"""
    
    id: int = Field(..., description="租户管理员ID")
    tenant_id: int = Field(..., description="租户ID")
    uid: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    name: str = Field(..., description="姓名")
    role: int = Field(..., description="角色（1：管理员, 2：教学运营人员）")
    token_version: int = Field(..., description="token版本")
    ctime: datetime = Field(..., description="创建时间")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    # 租户信息
    tenant_code: str = Field(..., description="租户代号")
    tenant_name: str = Field(..., description="租户名称")

    @model_validator(mode='before')
    @classmethod
    def extract_info(cls, data: Any) -> dict:
        """从TntAdmin对象中提取用户和租户信息"""
        if hasattr(data, '__dict__'):
            # 如果是SQLAlchemy对象
            result = {
                'id': data.id,
                'tenant_id': data.tenant_id,
                'uid': data.uid,
                'name': data.name,
                'role': data.role,
            }
            
            # 从关联的user对象获取信息
            if hasattr(data, 'user') and data.user:
                result.update({
                    'username': data.user.username,
                    'token_version': data.user.token_version,
                    'ctime': data.user.ctime,
                    'active': data.user.active,
                })
            else:
                # 如果没有关联的user，使用默认值
                result.update({
                    'username': "",
                    'token_version': 0,
                    'ctime': datetime.now(),
                    'active': 0,
                })
            
            # 从关联的tenant对象获取信息
            if hasattr(data, 'tenant') and data.tenant:
                result.update({
                    'tenant_code': data.tenant.code or "",
                    'tenant_name': data.tenant.name or "",
                })
            else:
                # 如果没有关联的tenant，使用默认值
                result.update({
                    'tenant_code': "",
                    'tenant_name': "",
                })
            
            return result
        
        return data

    class Config:
        from_attributes = True


class TntAdminListResponse(BaseModel):
    """租户管理员列表响应"""
    
    total: int
    items: list[TntAdminResponse]

    class Config:
        from_attributes = True
