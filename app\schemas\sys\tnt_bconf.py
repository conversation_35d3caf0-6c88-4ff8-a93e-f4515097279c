from typing import Optional, Any

from pydantic import BaseModel, Field, model_validator


class TntBconfCreate(BaseModel):
    """创建租户机器人设置"""
    
    tenant_id: int = Field(..., description="租户ID")
    key: str = Field(..., description="AI设置key")
    bid: int = Field(..., description="机器人ID")
    notes: Optional[str] = Field(None, description="备注")

    class Config:
        from_attributes = True


class TntBconfUpdate(BaseModel):
    """更新租户机器人设置"""
    
    key: Optional[str] = Field(None, description="AI设置key")
    bid: Optional[int] = Field(None, description="机器人ID")
    notes: Optional[str] = Field(None, description="备注")

    class Config:
        from_attributes = True


class TntBconfResponse(BaseModel):
    """租户机器人设置响应"""
    
    tenant_id: int = Field(..., description="租户ID")
    key: str = Field(..., description="AI设置key")
    bid: int = Field(..., description="机器人ID")
    notes: Optional[str] = Field(None, description="备注")
    # 机器人信息
    bot_name: str = Field(..., description="机器人名称")
    # 租户信息
    tenant_code: str = Field(..., description="租户代号")
    tenant_name: str = Field(..., description="租户名称")

    @model_validator(mode='before')
    @classmethod
    def extract_info(cls, data: Any) -> dict:
        """从TntBconf对象中提取机器人和租户信息"""
        if hasattr(data, '__dict__'):
            # 如果是SQLAlchemy对象
            result = {
                'tenant_id': data.tenant_id,
                'key': data.key,
                'bid': data.bid,
                'notes': data.notes,
            }
            
            # 从关联的bot对象获取信息
            if hasattr(data, 'bot') and data.bot:
                result.update({
                    'bot_name': data.bot.name,
                })
            else:
                # 如果没有关联的bot，使用默认值
                result.update({
                    'bot_name': "",
                })
            
            # 从关联的tenant对象获取信息
            if hasattr(data, 'tenant') and data.tenant:
                result.update({
                    'tenant_code': data.tenant.code,
                    'tenant_name': data.tenant.name or "",
                })
            else:
                # 如果没有关联的tenant，使用默认值
                result.update({
                    'tenant_code': "",
                    'tenant_name': "",
                })
            
            return result
        
        return data

    class Config:
        from_attributes = True


class TntBconfListResponse(BaseModel):
    """租户机器人设置列表响应"""
    
    total: int
    items: list[TntBconfResponse]

    class Config:
        from_attributes = True
