from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class SysUserCreate(BaseModel):
    """创建系统用户"""
    
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")

    class Config:
        from_attributes = True


class SysUserUpdate(BaseModel):
    """更新系统用户"""
    
    username: Optional[str] = Field(None, description="用户名")
    password: Optional[str] = Field(None, description="密码")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class SysUserResponse(BaseModel):
    """系统用户响应"""
    
    id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    token_version: int = Field(..., description="token版本")
    ctime: datetime = Field(..., description="创建时间")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class SysUserListResponse(BaseModel):
    """系统用户列表响应"""
    
    total: int
    items: list[SysUserResponse]

    class Config:
        from_attributes = True
