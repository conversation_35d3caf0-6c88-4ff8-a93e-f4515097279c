from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class TeacherCreate(BaseModel):
    """创建教师"""
    
    tenant_id: int = Field(..., description="租户ID")
    name: str = Field(..., description="教师姓名")
    gender: int = Field(0, description="性别（0：未知；1：男；2：女）")
    avatar: str = Field(..., description="头像URL")
    intro: Optional[str] = Field(None, description="简介")
    notes: Optional[str] = Field(None, description="备注")

    class Config:
        from_attributes = True


class TeacherUpdate(BaseModel):
    """更新教师"""
    
    name: Optional[str] = Field(None, description="教师姓名")
    gender: Optional[int] = Field(None, description="性别（0：未知；1：男；2：女）")
    avatar: Optional[str] = Field(None, description="头像URL")
    intro: Optional[str] = Field(None, description="简介")
    notes: Optional[str] = Field(None, description="备注")

    class Config:
        from_attributes = True


class TeacherResponse(BaseModel):
    """教师响应"""
    
    id: int = Field(..., description="教师ID")
    tenant_id: int = Field(..., description="租户ID")
    name: str = Field(..., description="教师姓名")
    gender: int = Field(..., description="性别（0：未知；1：男；2：女）")
    avatar: str = Field(..., description="头像URL")
    intro: Optional[str] = Field(None, description="简介")
    notes: Optional[str] = Field(None, description="备注")
    ctime: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class TeacherListResponse(BaseModel):
    """教师列表响应"""
    
    total: int
    items: list[TeacherResponse]

    class Config:
        from_attributes = True


class AvatarUploadURL(BaseModel):
    """头像上传URL响应"""
    
    upload_url: str = Field(..., description="上传URL")
    file_path: str = Field(..., description="文件路径")
    file_url: str = Field(..., description="文件访问URL")
    expires: int = Field(..., description="过期时间戳")

    class Config:
        from_attributes = True


class AvatarDeleteRequest(BaseModel):
    """删除头像文件请求"""
    
    file_path: str = Field(..., description="要删除的文件OSS signed URL")

    class Config:
        from_attributes = True
