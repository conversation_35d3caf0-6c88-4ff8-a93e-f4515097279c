from pydantic import BaseModel, Field


class LoginRequest(BaseModel):
    """登录请求"""

    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")

    class Config:
        from_attributes = True


class LoginResponse(BaseModel):
    """登录响应"""

    token: str = Field(..., description="访问令牌")
    token_type: str = Field("bearer", description="令牌类型")
    tenant_id: int = Field(..., description="租户ID")
    uid: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    name: str = Field(..., description="姓名")
    role: int = Field(..., description="角色")

    class Config:
        from_attributes = True


class LogoutResponse(BaseModel):
    """登出响应"""

    message: str = Field("登出成功", description="响应消息")

    class Config:
        from_attributes = True


class TntAdminLogin(BaseModel):
    """租户管理员登录schema"""

    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")

    class Config:
        from_attributes = True


class TntAdminToken(BaseModel):
    """租户管理员token schema"""

    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field("bearer", description="令牌类型")

    class Config:
        from_attributes = True


class TntAdminTokenPayload(BaseModel):
    """租户管理员token payload schema"""

    uid: int = Field(..., description="用户ID")
    utype: str = Field(..., description="用户类型（sys或tnt）")
    exp: int = Field(..., description="过期时间")

    class Config:
        from_attributes = True


class TntAdminProfile(BaseModel):
    """租户管理员个人信息schema"""

    tenant_id: int = Field(..., description="租户ID")
    uid: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    name: str = Field(..., description="姓名")
    role: int = Field(..., description="角色")

    class Config:
        from_attributes = True


class TntAdminPasswordUpdate(BaseModel):
    """租户管理员密码更新schema"""

    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., description="新密码")

    class Config:
        from_attributes = True
