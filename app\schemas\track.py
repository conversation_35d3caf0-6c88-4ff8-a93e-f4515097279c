from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List


class TrackExerciseResponse(BaseModel):
    """班级练习信息响应"""

    e_id: int = Field(..., description="练习ID")
    e_title: str = Field(..., description="练习标题")
    e_type: int = Field(..., description="练习类型")
    e_pic: Optional[str] = Field(None, description="练习图片URL")
    e_intro: Optional[str] = Field(None, description="练习简介")
    e_duration: Optional[int] = Field(None, description="练习时长（分钟）")
    t_id: Optional[int] = Field(None, description="老师ID")
    t_name: Optional[str] = Field(None, description="老师姓名")
    t_avatar: Optional[str] = Field(None, description="老师头像URL")
    t_intro: Optional[str] = Field(None, description="老师简介")
    depend: int = Field(..., description="练习依赖")
    w_id: Optional[int] = Field(None, description="作业单ID")
    s_id: Optional[int] = Field(None, description="场景ID")
    el_id: Optional[int] = Field(None, description="练习情况ID")
    el_status: Optional[int] = Field(None, description="练习状态（0：待练习；1：练习中；2：已提交）")
    el_btime: Optional[datetime] = Field(None, description="开始练习时间")
    el_stime: Optional[datetime] = Field(None, description="提交练习时间")
    el_utime: Optional[datetime] = Field(None, description="上次更新时间")

    class Config:
        from_attributes = True


class TrackExerciseListResponse(BaseModel):
    """班级练习列表响应"""

    class_name: str = Field(..., description="班级名称")
    exercises: List[TrackExerciseResponse] = Field(..., description="练习列表")

    class Config:
        from_attributes = True
