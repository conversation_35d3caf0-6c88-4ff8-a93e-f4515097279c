from typing import Optional

from pydantic import BaseModel, Field


class UnitCreate(BaseModel):
    """创建单元模块"""
    
    tenant_id: int = Field(..., description="租户ID")
    wid: int = Field(..., description="作业单ID")
    name: str = Field(..., description="名称")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    priority: int = Field(1, description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class UnitUpdate(BaseModel):
    """更新单元模块"""
    
    name: Optional[str] = Field(None, description="名称")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    priority: Optional[int] = Field(None, description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class UnitResponse(BaseModel):
    """单元模块响应"""
    
    id: int = Field(..., description="单元模块ID")
    tenant_id: int = Field(..., description="租户ID")
    wid: int = Field(..., description="作业单ID")
    name: str = Field(..., description="名称")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    priority: int = Field(..., description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class UnitListResponse(BaseModel):
    """单元模块列表响应"""
    
    total: int
    items: list[UnitResponse]

    class Config:
        from_attributes = True
