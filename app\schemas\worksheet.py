from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class WorksheetCreate(BaseModel):
    """创建作业单"""
    
    tenant_id: int = Field(..., description="租户ID")
    title: str = Field(..., description="标题")
    intro: Optional[str] = Field(None, description="简介")
    duration: Optional[int] = Field(None, description="预估练习时长（分钟）")
    version: Optional[str] = Field(None, description="版本")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    notes: Optional[str] = Field(None, description="备注")
    published: int = Field(0, description="是否发布（0：未发布；1：已发布）")

    class Config:
        from_attributes = True


class WorksheetUpdate(BaseModel):
    """更新作业单"""
    
    title: Optional[str] = Field(None, description="标题")
    intro: Optional[str] = Field(None, description="简介")
    duration: Optional[int] = Field(None, description="预估练习时长（分钟）")
    version: Optional[str] = Field(None, description="版本")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    notes: Optional[str] = Field(None, description="备注")
    published: Optional[int] = Field(None, description="是否发布（0：未发布；1：已发布）")
    active: Optional[int] = Field(None, description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class WorksheetResponse(BaseModel):
    """作业单响应"""
    
    id: int = Field(..., description="作业单ID")
    tenant_id: int = Field(..., description="租户ID")
    eid: int = Field(..., description="练习ID")
    title: str = Field(..., description="标题")
    type: int = Field(..., description="类型（1：作业单；2：角色扮演；）")
    intro: Optional[str] = Field(None, description="简介")
    duration: Optional[int] = Field(None, description="预估练习时长（分钟）")
    version: Optional[str] = Field(None, description="版本")
    pic: Optional[str] = Field(None, description="图片URL")
    bgtext: Optional[str] = Field(None, description="背景文字")
    notes: Optional[str] = Field(None, description="备注")
    published: int = Field(..., description="是否发布（0：未发布；1：已发布）")
    ctime: datetime = Field(..., description="创建时间")
    active: int = Field(..., description="是否有效（0：失效；1：有效）")

    class Config:
        from_attributes = True


class WorksheetListResponse(BaseModel):
    """作业单列表响应"""
    
    total: int
    items: list[WorksheetResponse]

    class Config:
        from_attributes = True


class WorksheetUnitResponse(BaseModel):
    """工作表单元响应（简化版）"""
    
    id: int = Field(..., description="单元ID")
    name: str = Field(..., description="单元名称")

    class Config:
        from_attributes = True


class WorksheetQuestionResponse(BaseModel):
    """工作表问题响应（简化版）"""
    
    id: int = Field(..., description="问题ID")
    title: str = Field(..., description="问题标题")
    notes: Optional[str] = Field(None, description="问题备注")

    class Config:
        from_attributes = True


class WorksheetUnitQuestionResponse(BaseModel):
    """工作表单元问题响应（包含asm关系信息）"""
    
    id: int = Field(..., description="作业单构成关系ID")
    qid: int = Field(..., description="问题ID")
    title: str = Field(..., description="问题标题")
    notes: Optional[str] = Field(None, description="问题备注")

    class Config:
        from_attributes = True


# 批量排序相关Schema
class UnitOrderItem(BaseModel):
    """单元顺序项"""
    
    id: int = Field(..., description="单元ID")
    priority: int = Field(..., description="新的展示顺序（从小到大）")

    class Config:
        from_attributes = True


class UnitBatchOrderRequest(BaseModel):
    """批量调整单元顺序请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    worksheet_id: int = Field(..., description="工作表ID")
    units: list[UnitOrderItem] = Field(..., description="单元顺序列表")

    class Config:
        from_attributes = True


class UnitBatchOrderResponse(BaseModel):
    """批量调整单元顺序响应"""
    
    success_count: int = Field(..., description="成功更新的单元数量")
    total_count: int = Field(..., description="总单元数量")
    updated_units: list[WorksheetUnitResponse] = Field(..., description="更新后的单元列表")

    class Config:
        from_attributes = True


class WorksheetAsmOrderItem(BaseModel):
    """作业单构成关系顺序项"""
    
    id: int = Field(..., description="关系ID")
    priority: int = Field(..., description="新的展示顺序（从小到大）")

    class Config:
        from_attributes = True


class WorksheetAsmBatchOrderRequest(BaseModel):
    """批量调整作业单构成关系顺序请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    worksheet_id: int = Field(..., description="工作表ID")
    unit_id: Optional[int] = Field(None, description="单元ID，可选，不传则调整整个工作表的顺序")
    items: list[WorksheetAsmOrderItem] = Field(..., description="构成关系顺序列表")

    class Config:
        from_attributes = True


class WorksheetAsmBatchOrderResponse(BaseModel):
    """批量调整作业单构成关系顺序响应"""
    
    success_count: int = Field(..., description="成功更新的构成关系数量")
    total_count: int = Field(..., description="总构成关系数量")
    updated_questions: list[WorksheetUnitQuestionResponse] = Field(..., description="更新后的问题列表")

    class Config:
        from_attributes = True


# 批量删除相关Schema
class UnitBatchDeleteRequest(BaseModel):
    """批量删除单元请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    worksheet_id: int = Field(..., description="工作表ID")
    unit_ids: list[int] = Field(..., description="单元ID列表")

    class Config:
        from_attributes = True


class UnitBatchDeleteResponse(BaseModel):
    """批量删除单元响应"""
    
    success_count: int = Field(..., description="成功删除的单元数量")
    total_count: int = Field(..., description="总单元数量")
    deleted_unit_ids: list[int] = Field(..., description="成功删除的单元ID列表")
    deleted_asm_count: int = Field(..., description="同时删除的关联构成关系数量")

    class Config:
        from_attributes = True


class WorksheetAsmBatchDeleteRequest(BaseModel):
    """批量删除作业单构成关系请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    worksheet_id: int = Field(..., description="工作表ID")
    unit_id: Optional[int] = Field(None, description="单元ID，可选，不传则删除整个工作表的指定构成关系")
    asm_ids: list[int] = Field(..., description="构成关系ID列表")

    class Config:
        from_attributes = True


class WorksheetAsmBatchDeleteResponse(BaseModel):
    """批量删除作业单构成关系响应"""
    
    success_count: int = Field(..., description="成功删除的构成关系数量")
    total_count: int = Field(..., description="总构成关系数量")
    deleted_asm_ids: list[int] = Field(..., description="成功删除的构成关系ID列表")

    class Config:
        from_attributes = True


# ASM关系创建和移动相关Schema
class WorksheetAsmCreate(BaseModel):
    """创建作业单构成关系"""
    
    tenant_id: int = Field(..., description="租户ID")
    wid: int = Field(..., description="工作表ID")
    uid: int = Field(..., description="单元ID")
    qid: int = Field(..., description="问题ID")
    priority: int = Field(1, description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class WorksheetAsmResponse(BaseModel):
    """作业单构成关系响应"""
    
    id: int = Field(..., description="关系ID")
    tenant_id: int = Field(..., description="租户ID")
    wid: int = Field(..., description="工作表ID")
    uid: int = Field(..., description="单元ID")
    qid: int = Field(..., description="问题ID")
    priority: int = Field(..., description="展示顺序")

    class Config:
        from_attributes = True


class WorksheetAsmBatchMoveRequest(BaseModel):
    """批量移动作业单构成关系请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    worksheet_id: int = Field(..., description="工作表ID")
    target_unit_id: int = Field(..., description="目标单元ID")
    asm_ids: list[int] = Field(..., description="要移动的构成关系ID列表")

    class Config:
        from_attributes = True


class WorksheetAsmBatchMoveResponse(BaseModel):
    """批量移动作业单构成关系响应"""
    
    success_count: int = Field(..., description="成功移动的构成关系数量")
    total_count: int = Field(..., description="总构成关系数量")
    moved_asm_ids: list[int] = Field(..., description="成功移动的构成关系ID列表")
    target_unit_id: int = Field(..., description="目标单元ID")

    class Config:
        from_attributes = True


# 单元问题批量管理相关Schema
class UnitQuestionItem(BaseModel):
    """单元问题项"""
    
    id: int = Field(..., description="问题ID")
    priority: int = Field(..., description="展示顺序（从小到大）")

    class Config:
        from_attributes = True


class UnitQuestionsUpdateRequest(BaseModel):
    """单元问题批量更新请求"""
    
    tenant_id: int = Field(..., description="租户ID")
    worksheet_id: int = Field(..., description="工作表ID")
    unit_id: int = Field(..., description="单元ID")
    questions: list[UnitQuestionItem] = Field(..., description="问题列表")

    class Config:
        from_attributes = True


class UnitQuestionsUpdateResponse(BaseModel):
    """单元问题批量更新响应"""
    
    success: bool = Field(..., description="操作是否成功")
    added_count: int = Field(..., description="新增的问题数量")
    updated_count: int = Field(..., description="更新的问题数量") 
    deleted_count: int = Field(..., description="删除的问题数量")
    total_count: int = Field(..., description="最终问题总数量")
    questions: list[WorksheetUnitQuestionResponse] = Field(..., description="最终的问题列表")

    class Config:
        from_attributes = True
