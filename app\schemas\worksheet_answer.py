from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class WorksheetAnswerCreate(BaseModel):
    """创建作业单作答"""
    
    tenant_id: int = Field(..., description="租户ID")
    elid: int = Field(..., description="练习情况ID")
    qid: int = Field(..., description="问题ID")
    draft: str = Field(..., description="草稿")
    answer: str = Field(..., description="已提交作答")
    comment: str = Field(..., description="AI点评内容")

    class Config:
        from_attributes = True


class WorksheetAnswerUpdate(BaseModel):
    """更新作业单作答"""
    
    draft: Optional[str] = Field(None, description="草稿")
    answer: Optional[str] = Field(None, description="已提交作答")
    comment: Optional[str] = Field(None, description="AI点评内容")

    class Config:
        from_attributes = True


class WorksheetAnswerResponse(BaseModel):
    """作业单作答响应"""
    
    id: int = Field(..., description="作业单作答ID")
    tenant_id: int = Field(..., description="租户ID")
    elid: int = Field(..., description="练习情况ID")
    qid: int = Field(..., description="问题ID")
    draft: str = Field(..., description="草稿")
    answer: str = Field(..., description="已提交作答")
    comment: str = Field(..., description="AI点评内容")
    stime: datetime = Field(..., description="提交时间")
    utime: datetime = Field(..., description="草稿更新时间")

    class Config:
        from_attributes = True


class WorksheetAnswerListResponse(BaseModel):
    """作业单作答列表响应"""
    
    total: int
    items: list[WorksheetAnswerResponse]

    class Config:
        from_attributes = True
