from typing import Optional

from sqlalchemy.orm import Session

from app.core.security import get_password_hash, verify_password
from app.models.models import Sys<PERSON>ser


def get_user_by_username(db: Session, username: str) -> Optional[SysUser]:
    """通过用户名获取用户"""
    return db.query(SysUser).filter(SysUser.username == username).first()


def authenticate_user(db: Session, username: str, password: str) -> Optional[SysUser]:
    """认证用户"""
    user = get_user_by_username(db, username)
    if not user:
        return None
    if not verify_password(password, user.passwd):
        return None
    return user


def update_user_password(
    db: Session, user_id: int, old_password: str, new_password: str
) -> bool:
    """更新用户密码的通用函数"""
    user = db.query(SysUser).filter(SysUser.id == user_id).first()
    if not user:
        return False

    if not verify_password(old_password, user.passwd):
        return False

    user.passwd = get_password_hash(new_password)
    user.token_version += 1  # 使当前token失效
    db.commit()
    return True
