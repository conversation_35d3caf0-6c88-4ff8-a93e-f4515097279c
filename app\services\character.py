from datetime import datetime
from typing import List, Optional, Tu<PERSON>

from sqlalchemy.orm import Session

from app.models.models import TntCharacter
from app.schemas.character import CharacterCreate, CharacterUpdate


def get_character(db: Session, character_id: int) -> Optional[TntCharacter]:
    """获取人物信息"""
    return db.query(TntCharacter).filter(TntCharacter.id == character_id).first()


def get_characters(
    db: Session,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    published: Optional[int] = None,
    name: Optional[str] = None,
    profile: Optional[str] = None,
    notes: Optional[str] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
) -> Tuple[List[TntCharacter], int]:
    """获取人物列表

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        skip: 跳过的记录数，用于分页
        limit: 返回的最大记录数，用于分页
        active: 人物状态筛选
        published: 发布状态筛选
        name: 按姓名搜索，支持模糊匹配
        profile: 按人物资料搜索，支持模糊匹配
        notes: 按备注搜索，支持模糊匹配
        sort_by: 可选，排序字段，可选值：id, ctime
        sort_order: 可选，排序方式，可选值：asc, desc
        start_time: 可选，创建时间的开始时间
        end_time: 可选，创建时间的结束时间

    Returns:
        (人物列表, 总数)
    """
    query = db.query(TntCharacter).filter(TntCharacter.tenant_id == tenant_id)
    if active is not None:
        query = query.filter(TntCharacter.active == active)
    if published is not None:
        query = query.filter(TntCharacter.published == published)

    # 搜索功能
    if name:
        query = query.filter(TntCharacter.name.ilike(f"%{name}%"))
    if profile:
        query = query.filter(TntCharacter.profile.ilike(f"%{profile}%"))
    if notes:
        query = query.filter(TntCharacter.notes.ilike(f"%{notes}%"))

    # 按创建时间区间筛选
    if start_time:
        query = query.filter(TntCharacter.ctime >= start_time)
    if end_time:
        query = query.filter(TntCharacter.ctime <= end_time)

    # 获取总数
    total = query.count()

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntCharacter.id.asc())
            else:
                query = query.order_by(TntCharacter.id.desc())
        elif sort_by == "ctime":
            if sort_order == "asc":
                query = query.order_by(TntCharacter.ctime.asc())
            else:
                query = query.order_by(TntCharacter.ctime.desc())

    characters = query.offset(skip).limit(limit).all()
    return characters, total


def create_character(db: Session, character_in: CharacterCreate) -> TntCharacter:
    """创建人物"""
    character_data = character_in.model_dump()
    # 设置默认值
    character_data.update({
        "avatar": None,  # 头像默认为空
        "pv_profile": character_data.get("profile"),  # 人物资料（提示词变量）默认为profile
        "pv_ability": None,  # 人物能力（提示词变量）默认为空
        "pv_restriction": None,  # 人物限制（提示词变量）默认为空
        "published": 0,  # 发布状态默认为未发布
    })
    
    db_character = TntCharacter(**character_data)
    db.add(db_character)
    db.commit()
    db.refresh(db_character)
    return db_character


def update_character(
    db: Session, character_id: int, character_in: CharacterUpdate
) -> Optional[TntCharacter]:
    """更新人物信息"""
    db_character = get_character(db, character_id)
    if not db_character:
        return None

    update_data = character_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_character, field, value)

    db.commit()
    db.refresh(db_character)
    return db_character
