from datetime import datetime
from typing import List, Optional, Tuple, Any

from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models.models import TntAdminClass, TntClass, TntClassExercise, TntClassStudent, TntExercise, TntStudent, TntTeacher
from app.schemas.clazz import (
    AdminClassCreate,
    ClassCreate,
    ClassExerciseCreate,
    ClassExerciseUpdate,
    ClassExerciseOrderItem,
    ClassStudentCreate,
    ClassStudentUpdate,
    ClassStudentOrderItem,
    ClassUpdate,
    ClassImportPlanRequest,
    ClassExerciseBatchSetTeacherRequest,
    ClassStudentBatchCreateRequest,
)
from app.services import plan as plan_service


def get_class(db: Session, class_id: int) -> Optional[TntClass]:
    """获取班级信息"""
    return db.query(TntClass).filter(TntClass.id == class_id).first()


def get_classes(
    db: Session,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
    name: Optional[str] = None,
    notes: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
) -> tuple[List[TntClass], int]:
    """获取班级列表

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        skip: 跳过的记录数，用于分页
        limit: 返回的最大记录数，用于分页
        active: 班级状态筛选
        sort_by: 可选，排序字段，可选值：id, ctime
        sort_order: 可选，排序方式，可选值：asc, desc
        name: 可选，按班级名称搜索
        notes: 可选，按备注搜索
        start_time: 可选，时间窗口开始时间（查找班级时间段被包含在此窗口中的班级）
        end_time: 可选，时间窗口结束时间（查找班级时间段被包含在此窗口中的班级）

    Returns:
        班级列表和总数的元组
    """
    query = db.query(TntClass).filter(TntClass.tenant_id == tenant_id)
    
    if active is not None:
        query = query.filter(TntClass.active == active)

    # 按名称搜索
    if name:
        query = query.filter(TntClass.name.ilike(f"%{name}%"))
    
    # 按备注搜索
    if notes:
        query = query.filter(TntClass.notes.ilike(f"%{notes}%"))

    # 按时间窗口筛选（班级的开始结束时间段被包含在时间窗口中）
    if start_time:
        query = query.filter(TntClass.btime >= start_time)
    if end_time:
        query = query.filter(TntClass.etime <= end_time)

    # 获取总数
    total = query.count()

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntClass.id.asc())
            else:
                query = query.order_by(TntClass.id.desc())
        elif sort_by == "ctime":
            if sort_order == "asc":
                query = query.order_by(TntClass.ctime.asc())
            else:
                query = query.order_by(TntClass.ctime.desc())

    return query.offset(skip).limit(limit).all(), total


def create_class(db: Session, class_in: ClassCreate) -> TntClass:
    """创建班级"""
    db_class = TntClass(**class_in.model_dump())
    db.add(db_class)
    db.commit()
    db.refresh(db_class)
    return db_class


def update_class(
    db: Session, class_id: int, class_in: ClassUpdate
) -> Optional[TntClass]:
    """更新班级信息"""
    db_class = get_class(db, class_id)
    if not db_class:
        return None

    update_data = class_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_class, field, value)

    db.commit()
    db.refresh(db_class)
    return db_class


def delete_class(db: Session, class_id: int) -> bool:
    """删除班级"""
    db_class = get_class(db, class_id)
    if not db_class:
        return False

    db.delete(db_class)
    db.commit()
    return True


def get_class_student(db: Session, class_student_id: int) -> Optional[TntClassStudent]:
    """获取班级学员关系"""
    return (
        db.query(TntClassStudent).filter(TntClassStudent.id == class_student_id).first()
    )


def get_class_students(
    db: Session,
    class_id: int,
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
) -> List[TntClassStudent]:
    """获取班级学员列表"""
    query = db.query(TntClassStudent).filter(TntClassStudent.cid == class_id)
    if active is not None:
        query = query.filter(TntClassStudent.active == active)
    return query.offset(skip).limit(limit).all()


def create_class_student(
    db: Session, class_student_in: ClassStudentCreate
) -> TntClassStudent:
    """创建班级学员关系"""
    db_class_student = TntClassStudent(**class_student_in.model_dump())
    db.add(db_class_student)
    db.commit()
    db.refresh(db_class_student)
    return db_class_student


def update_class_student(
    db: Session, class_student_id: int, class_student_in: ClassStudentUpdate
) -> Optional[TntClassStudent]:
    """更新班级学员关系"""
    db_class_student = get_class_student(db, class_student_id)
    if not db_class_student:
        return None

    update_data = class_student_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_class_student, field, value)

    db.commit()
    db.refresh(db_class_student)
    return db_class_student


def get_class_exercise(
    db: Session, class_exercise_id: int
) -> Optional[TntClassExercise]:
    """获取班级练习关系"""
    return (
        db.query(TntClassExercise)
        .filter(TntClassExercise.id == class_exercise_id)
        .first()
    )


def get_class_exercises(
    db: Session,
    class_id: int,
    tenant_id: int,
) -> Tuple[List[Any], int]:
    """获取班级练习关系列表，包含练习信息和老师信息
    
    Args:
        db: 数据库会话
        class_id: 班级ID
        tenant_id: 租户ID

    Returns:
        班级练习关系列表和总数的元组，返回的是 (TntClassExercise, TntExercise, TntTeacher) 的元组列表
        TntTeacher 可能为 None（当 tid 为 None 时）
    """
    # 联表查询，只返回 published=1 且 active=1 的练习，LEFT JOIN 老师信息
    query = db.query(TntClassExercise, TntExercise, TntTeacher).join(
        TntExercise, TntClassExercise.eid == TntExercise.id
    ).outerjoin(
        TntTeacher, TntClassExercise.tid == TntTeacher.id
    ).filter(
        and_(
            TntClassExercise.tenant_id == tenant_id,
            TntClassExercise.cid == class_id,
            TntClassExercise.active == 1,
            TntExercise.published == 1,
            TntExercise.active == 1
        )
    )

    # 默认按 priority 升序排列
    query = query.order_by(TntClassExercise.priority.asc())

    class_exercises = query.all()
    total = len(class_exercises)
    return class_exercises, total


def create_class_exercise(
    db: Session, class_exercise_in: ClassExerciseCreate
) -> TntClassExercise:
    """创建班级练习关系"""
    db_class_exercise = TntClassExercise(**class_exercise_in.model_dump())
    db.add(db_class_exercise)
    db.commit()
    db.refresh(db_class_exercise)
    return db_class_exercise


def update_class_exercise(
    db: Session, class_exercise_id: int, class_exercise_in: ClassExerciseUpdate
) -> Optional[TntClassExercise]:
    """更新班级练习关系"""
    db_class_exercise = get_class_exercise(db, class_exercise_id)
    if not db_class_exercise:
        return None

    update_data = class_exercise_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_class_exercise, field, value)

    db.commit()
    db.refresh(db_class_exercise)
    return db_class_exercise


def delete_class_exercise(db: Session, class_exercise_id: int, tenant_id: Optional[int] = None) -> bool:
    """删除班级练习关系"""
    db_class_exercise = get_class_exercise(db, class_exercise_id)
    if not db_class_exercise:
        return False
    
    # 如果指定了租户ID，验证班级练习关系是否属于该租户
    if tenant_id is not None and db_class_exercise.tenant_id != tenant_id:
        return False

    db.delete(db_class_exercise)
    db.commit()
    return True


def batch_update_class_exercise_order(
    db: Session,
    tenant_id: int,
    class_id: int,
    class_exercise_orders: List[ClassExerciseOrderItem]
) -> Tuple[List[TntClassExercise], int, int]:
    """批量调整班级练习关系顺序
    
    Args:
        db: 数据库会话
        tenant_id: 租户ID
        class_id: 班级ID
        class_exercise_orders: 班级练习关系顺序列表

    Returns:
        更新后的班级练习关系列表、成功数量、总数量的元组
    """
    updated_class_exercises = []
    success_count = 0
    total_count = len(class_exercise_orders)

    try:
        # 在一个事务中执行所有更新
        for order_item in class_exercise_orders:
            # 查找该租户和班级下的班级练习关系
            db_class_exercise = db.query(TntClassExercise).filter(
                and_(
                    TntClassExercise.id == order_item.id,
                    TntClassExercise.tenant_id == tenant_id,
                    TntClassExercise.cid == class_id
                )
            ).first()
            
            if db_class_exercise:
                # 更新 priority
                db_class_exercise.priority = order_item.priority
                updated_class_exercises.append(db_class_exercise)
                success_count += 1
        
        # 所有更新完成后一次性提交
        db.commit()
        
        # 刷新所有更新的对象
        for class_exercise in updated_class_exercises:
            db.refresh(class_exercise)
            
    except Exception as e:
        # 如果有任何错误，回滚事务
        db.rollback()
        raise e

    return updated_class_exercises, success_count, total_count


def batch_set_class_exercise_teacher(
    db: Session,
    tenant_id: int,
    class_id: int,
    teacher_id: Optional[int]
) -> Tuple[int, int, str]:
    """批量设置班级练习关系的老师
    
    Args:
        db: 数据库会话
        tenant_id: 租户ID
        class_id: 班级ID
        teacher_id: 老师ID，为None时清空所有练习的老师

    Returns:
        成功数量、总数量、操作消息的元组
    """
    try:
        # 查找该租户和班级下的所有班级练习关系
        query = db.query(TntClassExercise).filter(
            and_(
                TntClassExercise.tenant_id == tenant_id,
                TntClassExercise.cid == class_id
            )
        )
        
        # 获取所有符合条件的记录
        class_exercises = query.all()
        total_count = len(class_exercises)
        
        if total_count == 0:
            return 0, 0, "该班级下没有找到有效的练习关系"
        
        # 如果传入了teacher_id，需要验证老师是否存在且属于该租户
        if teacher_id is not None:
            teacher = db.query(TntTeacher).filter(
                and_(
                    TntTeacher.id == teacher_id,
                    TntTeacher.tenant_id == tenant_id
                )
            ).first()
            
            if not teacher:
                return 0, total_count, f"老师ID {teacher_id} 不存在或不属于该租户"
        
        # 批量更新所有记录的老师ID
        success_count = query.update({"tid": teacher_id})
        
        # 提交事务
        db.commit()
        
        # 生成操作消息
        if teacher_id is None:
            message = f"成功清空了 {success_count} 个练习的老师设置"
        else:
            message = f"成功为 {success_count} 个练习设置老师"
            
        return success_count, total_count, message
        
    except Exception as e:
        # 如果有任何错误，回滚事务
        db.rollback()
        raise e


def get_admin_class(db: Session, admin_class_id: int) -> Optional[TntAdminClass]:
    """获取跟班关系"""
    return db.query(TntAdminClass).filter(TntAdminClass.id == admin_class_id).first()


def get_admin_classes(
    db: Session, class_id: int, skip: int = 0, limit: int = 100
) -> List[TntAdminClass]:
    """获取班级跟班列表"""
    return (
        db.query(TntAdminClass)
        .filter(TntAdminClass.cid == class_id)
        .offset(skip)
        .limit(limit)
        .all()
    )


def create_admin_class(db: Session, admin_class_in: AdminClassCreate) -> TntAdminClass:
    """创建跟班关系"""
    db_admin_class = TntAdminClass(**admin_class_in.model_dump())
    db.add(db_admin_class)
    db.commit()
    db.refresh(db_admin_class)
    return db_admin_class


def delete_admin_class(db: Session, admin_class_id: int) -> bool:
    """删除跟班关系"""
    db_admin_class = get_admin_class(db, admin_class_id)
    if not db_admin_class:
        return False

    db.delete(db_admin_class)
    db.commit()
    return True


def get_class_students_with_student_info(
    db: Session,
    class_id: int,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
) -> Tuple[List[Any], int]:
    """获取班级学员关系列表，包含学员信息
    
    Args:
        db: 数据库会话
        class_id: 班级ID
        tenant_id: 租户ID
        skip: 跳过的记录数
        limit: 返回的最大记录数
        active: 关系状态筛选

    Returns:
        班级学员关系列表和总数的元组，返回的是 (TntClassStudent, TntStudent) 的元组列表
    """
    # 联表查询，获取班级学员关系和学员信息
    query = db.query(TntClassStudent, TntStudent).join(
        TntStudent, TntClassStudent.sid == TntStudent.id
    ).filter(
        and_(
            TntClassStudent.tenant_id == tenant_id,
            TntClassStudent.cid == class_id
        )
    )
    
    if active is not None:
        query = query.filter(TntClassStudent.active == active)

    # 获取总数
    total = query.count()

    # 默认按 priority 升序排列
    query = query.order_by(TntClassStudent.priority.asc())

    class_students = query.offset(skip).limit(limit).all()
    return class_students, total


def batch_delete_class_students(
    db: Session,
    tenant_id: int,
    class_id: int,
    class_student_ids: List[int],
) -> Tuple[List[int], int]:
    """批量删除班级学员关系
    
    Args:
        db: 数据库会话
        tenant_id: 租户ID
        class_id: 班级ID
        class_student_ids: 班级学员关系ID列表

    Returns:
        删除成功的关系ID列表和成功数量的元组
    """
    deleted_ids = []
    success_count = 0

    try:
        # 在一个事务中执行所有删除
        for class_student_id in class_student_ids:
            # 查找该租户和班级下的班级学员关系
            db_class_student = db.query(TntClassStudent).filter(
                and_(
                    TntClassStudent.id == class_student_id,
                    TntClassStudent.tenant_id == tenant_id,
                    TntClassStudent.cid == class_id
                )
            ).first()
            
            if db_class_student:
                db.delete(db_class_student)
                deleted_ids.append(class_student_id)
                success_count += 1
        
        # 所有删除完成后一次性提交
        db.commit()
            
    except Exception as e:
        # 如果有任何错误，回滚事务
        db.rollback()
        raise e

    return deleted_ids, success_count


def batch_update_class_student_order(
    db: Session,
    tenant_id: int,
    class_id: int,
    class_student_orders: List[ClassStudentOrderItem]
) -> Tuple[List[TntClassStudent], int, int]:
    """批量调整班级学员关系顺序
    
    Args:
        db: 数据库会话
        tenant_id: 租户ID
        class_id: 班级ID
        class_student_orders: 班级学员关系顺序列表

    Returns:
        更新后的班级学员关系列表、成功数量、总数量的元组
    """
    updated_class_students = []
    success_count = 0
    total_count = len(class_student_orders)

    try:
        # 在一个事务中执行所有更新
        for order_item in class_student_orders:
            # 查找该租户和班级下的班级学员关系
            db_class_student = db.query(TntClassStudent).filter(
                and_(
                    TntClassStudent.id == order_item.id,
                    TntClassStudent.tenant_id == tenant_id,
                    TntClassStudent.cid == class_id
                )
            ).first()
            
            if db_class_student:
                # 更新 priority
                db_class_student.priority = order_item.priority
                updated_class_students.append(db_class_student)
                success_count += 1
        
        # 所有更新完成后一次性提交
        db.commit()
        
        # 刷新所有更新的对象
        for class_student in updated_class_students:
            db.refresh(class_student)
            
    except Exception as e:
        # 如果有任何错误，回滚事务
        db.rollback()
        raise e

    return updated_class_students, success_count, total_count


def batch_create_class_students(
    db: Session,
    batch_request: ClassStudentBatchCreateRequest,
) -> Tuple[int, int, str]:
    """批量创建班级学员关系
    
    Args:
        db: 数据库会话
        batch_request: 批量创建请求数据

    Returns:
        成功数量、总数量、消息的元组
    """
    # 验证班级是否存在
    class_obj = get_class(db, batch_request.class_id)
    if not class_obj or class_obj.tenant_id != batch_request.tenant_id:
        return 0, len(batch_request.student_ids), "班级不存在或不属于指定租户"
    
    total_count = len(batch_request.student_ids)
    
    # 1. 批量查询该班级下已有的所有学员ID
    existing_student_ids = {
        row.sid for row in db.query(TntClassStudent).filter(
            and_(
                TntClassStudent.tenant_id == batch_request.tenant_id,
                TntClassStudent.cid == batch_request.class_id
            )
        ).all()
    }
    
    # 2. 批量查询请求的学员ID对应的有效学员信息
    valid_students = db.query(TntStudent).filter(
        and_(
            TntStudent.id.in_(batch_request.student_ids),
            TntStudent.tenant_id == batch_request.tenant_id
        )
    ).all()
    
    # 构建有效学员ID映射（ID -> 学员对象）
    valid_student_map = {student.id: student for student in valid_students}
    valid_student_ids = set(valid_student_map.keys())
    
    # 3. 计算需要新增的增量学员ID
    requested_student_ids = set(batch_request.student_ids)
    invalid_student_ids = requested_student_ids - valid_student_ids
    already_exists_student_ids = requested_student_ids & existing_student_ids
    new_student_ids = requested_student_ids - existing_student_ids - invalid_student_ids
    
    # 4. 获取班级当前最大的priority值，用于后续排序
    max_priority_query = db.query(TntClassStudent).filter(
        and_(
            TntClassStudent.tenant_id == batch_request.tenant_id,
            TntClassStudent.cid == batch_request.class_id,
        )
    ).order_by(TntClassStudent.priority.desc())
    
    max_priority_result = max_priority_query.first()
    next_priority = max_priority_result.priority + 1 if max_priority_result else 1
    
    success_count = 0
    
    try:
        # 5. 批量创建新的班级学员关系
        for student_id in new_student_ids:
            class_student_create = ClassStudentCreate(
                tenant_id=batch_request.tenant_id,
                cid=batch_request.class_id,
                sid=student_id,
                priority=next_priority,
            )
            
            db_class_student = TntClassStudent(**class_student_create.model_dump())
            db.add(db_class_student)
            success_count += 1
            next_priority += 1
        
        # 所有操作完成后一次性提交
        db.commit()
            
    except Exception as e:
        # 如果有任何错误，回滚事务
        db.rollback()
        raise e
    
    # 6. 构建结果消息
    message_parts = [f"成功创建 {success_count} 个学员关系"]
    
    if already_exists_student_ids:
        message_parts.append(f"跳过已存在 {len(already_exists_student_ids)} 个")
    
    if invalid_student_ids:
        message_parts.append(f"跳过无效 {len(invalid_student_ids)} 个")
    
    message = "，".join(message_parts)

    return success_count, total_count, message


def import_plan_to_class(
    db: Session,
    import_request: ClassImportPlanRequest,
) -> Tuple[List[TntClassExercise], int, int, str]:
    """导入计划的练习到班级
    
    Args:
        db: 数据库会话
        import_request: 导入计划请求数据

    Returns:
        导入的班级练习关系列表、成功数量、总数量、消息的元组
    """
    # 验证计划是否存在
    plan = plan_service.get_plan(db, import_request.plan_id, import_request.tenant_id)
    if not plan:
        return [], 0, 0, "计划不存在或不属于指定租户"
    
    # 验证班级是否存在
    class_obj = get_class(db, import_request.class_id)
    if not class_obj or class_obj.tenant_id != import_request.tenant_id:
        return [], 0, 0, "班级不存在或不属于指定租户"
    
    # 获取计划的所有练习
    plan_exercises, total_count = plan_service.get_plan_exercises(
        db=db, 
        plan_id=import_request.plan_id, 
        tenant_id=import_request.tenant_id
    )
    
    if not plan_exercises:
        return [], 0, total_count, "计划中没有可导入的练习"
    
    # 获取班级当前最大的priority值，用于后续排序
    max_priority_query = db.query(TntClassExercise).filter(
        and_(
            TntClassExercise.tenant_id == import_request.tenant_id,
            TntClassExercise.cid == import_request.class_id,
        )
    ).order_by(TntClassExercise.priority.desc())
    
    max_priority_result = max_priority_query.first()
    next_priority = max_priority_result.priority + 1 if max_priority_result else 1
    
    imported_class_exercises = []
    success_count = 0
    skipped_exercises = []
    
    try:
        # 在一个事务中执行所有导入操作
        for plan_exercise_row in plan_exercises:
            plan_exercise, exercise = plan_exercise_row
            
            # 检查该练习是否已经在班级中存在
            existing_class_exercise = db.query(TntClassExercise).filter(
                and_(
                    TntClassExercise.tenant_id == import_request.tenant_id,
                    TntClassExercise.cid == import_request.class_id,
                    TntClassExercise.eid == exercise.id
                )
            ).first()
            
            if existing_class_exercise:
                skipped_exercises.append(exercise.title)
                continue
            
            # 创建班级练习关系
            class_exercise_create = ClassExerciseCreate(
                tenant_id=import_request.tenant_id,
                cid=import_request.class_id,
                eid=exercise.id,
                tid=import_request.teacher_id,
                depend=plan_exercise.depend,  # 保持计划中的依赖关系设置
                priority=next_priority,
            )
            
            db_class_exercise = TntClassExercise(**class_exercise_create.model_dump())
            db.add(db_class_exercise)
            imported_class_exercises.append(db_class_exercise)
            success_count += 1
            next_priority += 1
        
        # 所有操作完成后一次性提交
        db.commit()
        
        # 刷新所有新创建的对象
        for class_exercise in imported_class_exercises:
            db.refresh(class_exercise)
            
    except Exception as e:
        # 如果有任何错误，回滚事务
        db.rollback()
        raise e
    
    # 构建结果消息
    message_parts = [f"成功导入 {success_count} 个练习"]
    if skipped_exercises:
        message_parts.append(f"跳过已存在的练习: {', '.join(skipped_exercises[:5])}")
        if len(skipped_exercises) > 5:
            message_parts.append("等")
    
    message = "，".join(message_parts)

    return imported_class_exercises, success_count, total_count, message
