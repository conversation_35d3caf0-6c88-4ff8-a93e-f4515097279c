from typing import List, Optional, Tuple

from sqlalchemy.orm import Session

from app.models.models import TntCue, TntLine
from app.schemas.cue import CueCreate, CueUpdate


def get_cue(db: Session, cue_id: int, tenant_id: Optional[int] = None) -> Optional[TntCue]:
    """获取剧本提示信息"""
    query = db.query(TntCue).filter(TntCue.id == cue_id)
    if tenant_id is not None:
        query = query.filter(TntCue.tenant_id == tenant_id)
    return query.first()


def get_cues(
    db: Session,
    tenant_id: int,
    sid: int,
    active: Optional[int] = None,
) -> List[TntCue]:
    """获取剧本提示列表，按priority从小到大排序"""
    query = db.query(TntCue).filter(
        TntCue.tenant_id == tenant_id,
        TntCue.sid == sid
    )
    if active is not None:
        query = query.filter(TntCue.active == active)
    
    # 按priority从小到大排序
    query = query.order_by(TntCue.priority.asc())
    
    return query.all()


def create_cue(db: Session, cue_in: CueCreate) -> TntCue:
    """创建剧本提示"""
    # 获取当前场景下 priority 的最大值
    max_priority = db.query(TntCue).filter(
        TntCue.tenant_id == cue_in.tenant_id,
        TntCue.sid == cue_in.sid
    ).order_by(TntCue.priority.desc()).first()
    
    # 设置新的 priority 值
    new_priority = (max_priority.priority + 1) if max_priority else 1
    
    # 创建剧本提示数据，覆盖 priority 值
    cue_data = cue_in.model_dump()
    cue_data['priority'] = new_priority
    
    db_cue = TntCue(**cue_data)
    db.add(db_cue)
    db.commit()
    db.refresh(db_cue)
    return db_cue


def update_cue(db: Session, cue_id: int, cue_in: CueUpdate, tenant_id: Optional[int] = None) -> Optional[TntCue]:
    """更新剧本提示信息"""
    db_cue = get_cue(db, cue_id, tenant_id)
    if not db_cue:
        return None

    update_data = cue_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_cue, field, value)

    db.commit()
    db.refresh(db_cue)
    return db_cue


def delete_cue(db: Session, cue_id: int, tenant_id: Optional[int] = None) -> bool:
    """删除剧本提示（先删除关联的台词，再删除提示）"""
    try:
        # 开始事务
        db_cue = get_cue(db, cue_id, tenant_id)
        if not db_cue:
            return False

        # 先删除关联的台词
        db.query(TntLine).filter(TntLine.cueid == cue_id).delete()
        
        # 再删除剧本提示
        db.delete(db_cue)
        
        db.commit()
        return True
        
    except Exception as e:
        db.rollback()
        raise e


def batch_update_cue_order(
    db: Session, 
    tenant_id: int, 
    scene_id: int, 
    cue_orders: List[dict]
) -> Tuple[List[TntCue], int, int]:
    """批量调整剧本提示顺序
    
    Args:
        db: 数据库会话
        tenant_id: 租户ID
        scene_id: 场景ID
        cue_orders: 顺序列表，格式：[{"id": cue_id, "priority": priority}, ...]
    
    Returns:
        (更新后的剧本提示列表, 成功数量, 总数量)
    """
    try:
        updated_cues = []
        success_count = 0
        total_count = len(cue_orders)
        
        for order_item in cue_orders:
            cue_id = order_item["id"]
            priority = order_item["priority"]
            
            db_cue = db.query(TntCue).filter(
                TntCue.id == cue_id,
                TntCue.tenant_id == tenant_id,
                TntCue.sid == scene_id
            ).first()
            
            if db_cue:
                db_cue.priority = priority
                updated_cues.append(db_cue)
                success_count += 1
        
        db.commit()
        
        # 重新按priority排序返回
        updated_cues.sort(key=lambda x: x.priority)
        
        return updated_cues, success_count, total_count
        
    except Exception as e:
        db.rollback()
        raise e
