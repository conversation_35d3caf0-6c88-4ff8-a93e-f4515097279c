from datetime import datetime
from typing import List, Optional

from sqlalchemy.orm import Session

from app.models.models import TntExercise
from app.schemas.exercise import ExerciseCreate, ExerciseUpdate


def get_exercise(db: Session, exercise_id: int) -> Optional[TntExercise]:
    """获取练习信息"""
    return db.query(TntExercise).filter(TntExercise.id == exercise_id).first()


def get_exercises(
    db: Session,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    published: Optional[int] = None,
    type: Optional[int] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    title: Optional[str] = None,
    notes: Optional[str] = None,
) -> List[TntExercise]:
    """获取练习列表

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        skip: 跳过的记录数，用于分页
        limit: 返回的最大记录数，用于分页
        active: 练习状态筛选
        published: 发布状态筛选
        type: 练习类型筛选
        sort_by: 可选，排序字段，可选值：id, ctime
        sort_order: 可选，排序方式，可选值：asc, desc
        start_time: 可选，创建时间的开始时间
        end_time: 可选，创建时间的结束时间
        title: 可选，按标题搜索，支持模糊匹配
        notes: 可选，按备注搜索，支持模糊匹配

    Returns:
        练习列表
    """
    query = db.query(TntExercise).filter(TntExercise.tenant_id == tenant_id)
    if active is not None:
        query = query.filter(TntExercise.active == active)
    if published is not None:
        query = query.filter(TntExercise.published == published)
    if type is not None:
        query = query.filter(TntExercise.type == type)

    # 按创建时间区间筛选
    if start_time:
        query = query.filter(TntExercise.ctime >= start_time)
    if end_time:
        query = query.filter(TntExercise.ctime <= end_time)

    # 按标题搜索
    if title:
        query = query.filter(TntExercise.title.like(f"%{title}%"))
    
    # 按备注搜索
    if notes:
        query = query.filter(TntExercise.notes.like(f"%{notes}%"))

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntExercise.id.asc())
            else:
                query = query.order_by(TntExercise.id.desc())
        elif sort_by == "ctime":
            if sort_order == "asc":
                query = query.order_by(TntExercise.ctime.asc())
            else:
                query = query.order_by(TntExercise.ctime.desc())

    return query.offset(skip).limit(limit).all()


def create_exercise(db: Session, exercise_in: ExerciseCreate) -> TntExercise:
    """创建练习"""
    db_exercise = TntExercise(**exercise_in.model_dump())
    db.add(db_exercise)
    db.commit()
    db.refresh(db_exercise)
    return db_exercise


def update_exercise(
    db: Session, exercise_id: int, exercise_in: ExerciseUpdate
) -> Optional[TntExercise]:
    """更新练习信息"""
    db_exercise = get_exercise(db, exercise_id)
    if not db_exercise:
        return None

    update_data = exercise_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_exercise, field, value)

    db.commit()
    db.refresh(db_exercise)
    return db_exercise
