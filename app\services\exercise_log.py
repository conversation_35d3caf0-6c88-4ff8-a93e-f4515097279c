from typing import List, Optional

from sqlalchemy.orm import Session

from app.models.models import TntExerciseLog
from app.schemas.exercise_log import ExerciseLogCreate, ExerciseLogUpdate


def get_exercise_log(db: Session, exercise_log_id: int) -> Optional[TntExerciseLog]:
    """获取练习情况信息"""
    return db.query(TntExerciseLog).filter(TntExerciseLog.id == exercise_log_id).first()


def get_exercise_logs(
    db: Session,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    cid: Optional[int] = None,
    sid: Optional[int] = None,
    eid: Optional[int] = None,
) -> List[TntExerciseLog]:
    """获取练习情况列表"""
    query = db.query(TntExerciseLog).filter(TntExerciseLog.tenant_id == tenant_id)
    if active is not None:
        query = query.filter(TntExerciseLog.active == active)
    if cid is not None:
        query = query.filter(TntExerciseLog.cid == cid)
    if sid is not None:
        query = query.filter(TntExerciseLog.sid == sid)
    if eid is not None:
        query = query.filter(TntExerciseLog.eid == eid)
    return query.offset(skip).limit(limit).all()


def create_exercise_log(
    db: Session, exercise_log_in: ExerciseLogCreate
) -> TntExerciseLog:
    """创建练习情况"""
    db_exercise_log = TntExerciseLog(**exercise_log_in.model_dump())
    db.add(db_exercise_log)
    db.commit()
    db.refresh(db_exercise_log)
    return db_exercise_log


def update_exercise_log(
    db: Session, exercise_log_id: int, exercise_log_in: ExerciseLogUpdate
) -> Optional[TntExerciseLog]:
    """更新练习情况信息"""
    db_exercise_log = get_exercise_log(db, exercise_log_id)
    if not db_exercise_log:
        return None

    update_data = exercise_log_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_exercise_log, field, value)

    db.commit()
    db.refresh(db_exercise_log)
    return db_exercise_log
