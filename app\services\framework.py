from typing import List, Optional

from sqlalchemy.orm import Session

from app.models.models import TntFramework
from app.schemas.framework import FrameworkCreate, FrameworkUpdate, FrameworkOrderItem


def get_framework(db: Session, framework_id: int) -> Optional[TntFramework]:
    """获取理论框架信息"""
    return db.query(TntFramework).filter(TntFramework.id == framework_id).first()


def get_frameworks(
    db: Session,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
) -> tuple[List[TntFramework], int]:
    """获取理论框架列表"""
    query = db.query(TntFramework).filter(TntFramework.tenant_id == tenant_id)
    if active is not None:
        query = query.filter(TntFramework.active == active)

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntFramework.id.asc())
            else:
                query = query.order_by(TntFramework.id.desc())
        elif sort_by == "priority":
            if sort_order == "asc":
                query = query.order_by(TntFramework.priority.asc())
            else:
                query = query.order_by(TntFramework.priority.desc())
        pass

    total = query.count()
    frameworks = query.offset(skip).limit(limit).all()
    return frameworks, total


def create_framework(db: Session, framework_in: FrameworkCreate) -> TntFramework:
    """创建理论框架"""
    # 获取当前租户下 priority 的最大值
    max_priority = db.query(TntFramework).filter(
        TntFramework.tenant_id == framework_in.tenant_id
    ).order_by(TntFramework.priority.desc()).first()
    
    # 设置新的 priority 值
    new_priority = (max_priority.priority + 1) if max_priority else 1
    
    # 创建框架数据，覆盖 priority 值
    framework_data = framework_in.model_dump()
    framework_data['priority'] = new_priority
    
    db_framework = TntFramework(**framework_data)
    db.add(db_framework)
    db.commit()
    db.refresh(db_framework)
    return db_framework


def update_framework(
    db: Session, framework_id: int, framework_in: FrameworkUpdate
) -> Optional[TntFramework]:
    """更新理论框架信息"""
    db_framework = get_framework(db, framework_id)
    if not db_framework:
        return None

    update_data = framework_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_framework, field, value)

    db.commit()
    db.refresh(db_framework)
    return db_framework


def batch_update_framework_order(
    db: Session, 
    tenant_id: int, 
    framework_orders: List[FrameworkOrderItem]
) -> tuple[List[TntFramework], int, int]:
    """批量更新框架顺序"""
    success_count = 0
    updated_frameworks = []
    
    try:
        # 在一个事务中执行所有更新
        for order_item in framework_orders:
            # 验证框架是否属于该租户
            db_framework = db.query(TntFramework).filter(
                TntFramework.id == order_item.id,
                TntFramework.tenant_id == tenant_id
            ).first()
            
            if db_framework:
                db_framework.priority = order_item.priority
                updated_frameworks.append(db_framework)
                success_count += 1
        
        # 所有更新完成后一次性提交
        if updated_frameworks:
            db.commit()
            # 刷新所有更新的框架数据
            for framework in updated_frameworks:
                db.refresh(framework)
                
    except Exception as e:
        # 如果有任何错误，回滚事务
        db.rollback()
        raise e
    
    return updated_frameworks, success_count, len(framework_orders)
