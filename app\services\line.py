from typing import List, Optional, Tuple

from sqlalchemy.orm import Session

from app.models.models import TntLine
from app.schemas.line import LineCreate, LineUpdate


def get_line(db: Session, line_id: int, tenant_id: Optional[int] = None) -> Optional[TntLine]:
    """获取台词信息"""
    query = db.query(TntLine).filter(TntLine.id == line_id)
    if tenant_id is not None:
        query = query.filter(TntLine.tenant_id == tenant_id)
    return query.first()


def get_lines(
    db: Session,
    tenant_id: int,
    cueid: int,
    active: Optional[int] = None,
) -> List[TntLine]:
    """获取台词列表，按priority从小到大排序"""
    query = db.query(TntLine).filter(
        TntLine.tenant_id == tenant_id,
        TntLine.cueid == cueid
    )
    if active is not None:
        query = query.filter(TntLine.active == active)

    # 按priority从小到大排序
    query = query.order_by(TntLine.priority.asc())

    return query.all()


def create_line(db: Session, line_in: LineCreate) -> TntLine:
    """创建台词"""
    # 获取当前剧本提示下 priority 的最大值
    max_priority = db.query(TntLine).filter(
        TntLine.tenant_id == line_in.tenant_id,
        TntLine.cueid == line_in.cueid
    ).order_by(TntLine.priority.desc()).first()
    
    # 设置新的 priority 值
    new_priority = (max_priority.priority + 1) if max_priority else 1
    
    # 创建台词数据，覆盖 priority 值
    line_data = line_in.model_dump()
    line_data['priority'] = new_priority
    
    db_line = TntLine(**line_data)
    db.add(db_line)
    db.commit()
    db.refresh(db_line)
    return db_line


def update_line(db: Session, line_id: int, line_in: LineUpdate, tenant_id: Optional[int] = None) -> Optional[TntLine]:
    """更新台词信息"""
    db_line = get_line(db, line_id, tenant_id)
    if not db_line:
        return None

    update_data = line_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_line, field, value)

    db.commit()
    db.refresh(db_line)
    return db_line


def delete_line(db: Session, line_id: int, tenant_id: Optional[int] = None) -> bool:
    """删除台词"""
    db_line = get_line(db, line_id, tenant_id)
    if not db_line:
        return False

    try:
        db.delete(db_line)
        db.commit()
        return True
        
    except Exception as e:
        db.rollback()
        raise e


def batch_update_line_order(
    db: Session, 
    tenant_id: int, 
    line_orders: List[dict]
) -> Tuple[List[TntLine], int, int]:
    """批量调整台词顺序
    
    Args:
        db: 数据库会话
        tenant_id: 租户ID
        line_orders: 顺序列表，格式：[{"id": line_id, "priority": priority}, ...]
    
    Returns:
        (更新后的台词列表, 成功数量, 总数量)
    """
    try:
        updated_lines = []
        success_count = 0
        total_count = len(line_orders)
        
        for order_item in line_orders:
            line_id = order_item["id"]
            priority = order_item["priority"]
            
            db_line = db.query(TntLine).filter(
                TntLine.id == line_id,
                TntLine.tenant_id == tenant_id
            ).first()
            
            if db_line:
                db_line.priority = priority
                updated_lines.append(db_line)
                success_count += 1
        
        db.commit()
        
        # 重新按priority排序返回
        updated_lines.sort(key=lambda x: x.priority)
        
        return updated_lines, success_count, total_count
        
    except Exception as e:
        db.rollback()
        raise e


def batch_move_lines(
    db: Session, 
    tenant_id: int, 
    target_cueid: int,
    line_ids: List[int]
) -> Tuple[List[int], int]:
    """批量移动台词到指定剧本提示
    
    Args:
        db: 数据库会话
        tenant_id: 租户ID
        target_cueid: 目标剧本提示ID
        line_ids: 要移动的台词ID列表
    
    Returns:
        (成功移动的台词ID列表, 成功数量)
    """
    try:
        # 验证目标剧本提示是否存在且属于指定租户
        from app.models.models import TntCue
        target_cue = db.query(TntCue).filter(
            TntCue.id == target_cueid,
            TntCue.tenant_id == tenant_id
        ).first()
        
        if not target_cue:
            raise ValueError("目标剧本提示不存在或不属于指定租户")
        
        moved_line_ids = []
        success_count = 0
        
        for line_id in line_ids:
            # 查找台词，确保它属于指定租户
            db_line = db.query(TntLine).filter(
                TntLine.id == line_id,
                TntLine.tenant_id == tenant_id
            ).first()
            
            if db_line:
                # 移动到目标剧本提示
                db_line.cueid = target_cueid
                moved_line_ids.append(line_id)
                success_count += 1
        
        db.commit()
        
        return moved_line_ids, success_count
        
    except Exception as e:
        db.rollback()
        raise e
