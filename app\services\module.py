from typing import List, Optional

from sqlalchemy.orm import Session

from app.models.models import TntModule
from app.schemas.module import Module<PERSON><PERSON>, ModuleUpdate, ModuleOrderItem


def get_module(db: Session, module_id: int, tenant_id: Optional[int] = None) -> Optional[TntModule]:
    """获取模块信息"""
    query = db.query(TntModule).filter(TntModule.id == module_id)
    if tenant_id is not None:
        query = query.filter(TntModule.tenant_id == tenant_id)
    return query.first()


def get_modules(
    db: Session,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    fid: Optional[int] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
) -> tuple[List[TntModule], int]:
    """获取模块列表"""
    query = db.query(TntModule).filter(TntModule.tenant_id == tenant_id)
    if active is not None:
        query = query.filter(TntModule.active == active)
    if fid is not None:
        query = query.filter(TntModule.fid == fid)

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntModule.id.asc())
            else:
                query = query.order_by(TntModule.id.desc())
        elif sort_by == "priority":
            if sort_order == "asc":
                query = query.order_by(TntModule.priority.asc())
            else:
                query = query.order_by(TntModule.priority.desc())
        pass

    total = query.count()
    modules = query.offset(skip).limit(limit).all()
    return modules, total


def create_module(db: Session, module_in: ModuleCreate) -> TntModule:
    """创建模块"""
    # 获取当前租户下同一框架的 priority 的最大值
    max_priority = db.query(TntModule).filter(
        TntModule.tenant_id == module_in.tenant_id,
        TntModule.fid == module_in.fid
    ).order_by(TntModule.priority.desc()).first()
    
    # 设置新的 priority 值
    new_priority = (max_priority.priority + 1) if max_priority else 1
    
    # 创建模块数据，覆盖 priority 值
    module_data = module_in.model_dump()
    module_data['priority'] = new_priority
    
    db_module = TntModule(**module_data)
    db.add(db_module)
    db.commit()
    db.refresh(db_module)
    return db_module


def update_module(
    db: Session, module_id: int, module_in: ModuleUpdate, tenant_id: Optional[int] = None
) -> Optional[TntModule]:
    """更新模块信息"""
    db_module = get_module(db, module_id, tenant_id)
    if not db_module:
        return None

    update_data = module_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_module, field, value)

    db.commit()
    db.refresh(db_module)
    return db_module


def batch_update_module_order(
    db: Session, 
    tenant_id: int, 
    module_orders: List[ModuleOrderItem]
) -> tuple[List[TntModule], int, int]:
    """批量更新模块顺序"""
    success_count = 0
    updated_modules = []
    
    try:
        # 在一个事务中执行所有更新
        for order_item in module_orders:
            # 验证模块是否属于该租户
            db_module = db.query(TntModule).filter(
                TntModule.id == order_item.id,
                TntModule.tenant_id == tenant_id
            ).first()
            
            if db_module:
                db_module.priority = order_item.priority
                updated_modules.append(db_module)
                success_count += 1
        
        # 所有更新完成后一次性提交
        if updated_modules:
            db.commit()
            # 刷新所有更新的模块数据
            for module in updated_modules:
                db.refresh(module)
                
    except Exception as e:
        # 如果有任何错误，回滚事务
        db.rollback()
        raise e
    
    return updated_modules, success_count, len(module_orders)
