from typing import List, Optional, Tuple, Any

from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models.models import TntPlan, TntPlanExercise, TntExercise
from app.schemas.plan import PlanCreate, PlanUpdate, PlanExerciseCreate, PlanExerciseUpdate, PlanExerciseOrderItem


def get_plan(db: Session, plan_id: int, tenant_id: Optional[int] = None) -> Optional[TntPlan]:
    """获取计划信息"""
    query = db.query(TntPlan).filter(TntPlan.id == plan_id)
    if tenant_id is not None:
        query = query.filter(TntPlan.tenant_id == tenant_id)
    return query.first()


def get_plans(
    db: Session,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    name: Optional[str] = None,
    description: Optional[str] = None,
    notes: Optional[str] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
) -> Tuple[List[TntPlan], int]:
    """获取计划列表

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        skip: 跳过的记录数，用于分页
        limit: 返回的最大记录数，用于分页
        active: 可选，按状态筛选（0：失效；1：有效）
        name: 按计划名称搜索，支持模糊匹配
        description: 按描述搜索，支持模糊匹配
        notes: 按备注搜索，支持模糊匹配
        sort_by: 可选，排序字段，可选值：id
        sort_order: 可选，排序方式，可选值：asc, desc

    Returns:
        计划列表和总数的元组
    """
    query = db.query(TntPlan).filter(TntPlan.tenant_id == tenant_id)
    if active is not None:
        query = query.filter(TntPlan.active == active)

    # 搜索功能
    if name:
        query = query.filter(TntPlan.name.ilike(f"%{name}%"))
    if description:
        query = query.filter(TntPlan.description.ilike(f"%{description}%"))
    if notes:
        query = query.filter(TntPlan.notes.ilike(f"%{notes}%"))

    # 获取总数
    total = query.count()

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntPlan.id.asc())
            else:
                query = query.order_by(TntPlan.id.desc())

    plans = query.offset(skip).limit(limit).all()
    return plans, total


def create_plan(db: Session, plan_in: PlanCreate) -> TntPlan:
    """创建计划"""
    db_plan = TntPlan(**plan_in.model_dump())
    db.add(db_plan)
    db.commit()
    db.refresh(db_plan)
    return db_plan


def update_plan(db: Session, plan_id: int, plan_in: PlanUpdate, tenant_id: Optional[int] = None) -> Optional[TntPlan]:
    """更新计划信息"""
    db_plan = get_plan(db, plan_id, tenant_id)
    if not db_plan:
        return None

    update_data = plan_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_plan, field, value)

    db.commit()
    db.refresh(db_plan)
    return db_plan


def delete_plan(db: Session, plan_id: int, tenant_id: Optional[int] = None) -> bool:
    """删除计划"""
    db_plan = get_plan(db, plan_id, tenant_id)
    if not db_plan:
        return False

    db.delete(db_plan)
    db.commit()
    return True


# ===== PlanExercise 相关函数 =====

def get_plan_exercises(
    db: Session,
    plan_id: int,
    tenant_id: int,
) -> Tuple[List[Any], int]:
    """获取计划练习关系列表
    
    Args:
        db: 数据库会话
        plan_id: 计划ID
        tenant_id: 租户ID

    Returns:
        计划练习关系列表和总数的元组，返回的是 (TntPlanExercise, TntExercise) 的元组列表
    """
    # 联表查询，只返回 published=1 且 active=1 的练习
    query = db.query(TntPlanExercise, TntExercise).join(
        TntExercise, TntPlanExercise.eid == TntExercise.id
    ).filter(
        and_(
            TntPlanExercise.tenant_id == tenant_id,
            TntPlanExercise.pid == plan_id,
            TntExercise.published == 1,
            TntExercise.active == 1
        )
    )

    # 默认按 priority 升序排列
    query = query.order_by(TntPlanExercise.priority.asc())

    plan_exercises = query.all()
    total = len(plan_exercises)
    return plan_exercises, total


def get_plan_exercise(db: Session, plan_exercise_id: int, tenant_id: Optional[int] = None) -> Optional[TntPlanExercise]:
    """获取计划练习关系信息"""
    query = db.query(TntPlanExercise).filter(TntPlanExercise.id == plan_exercise_id)
    if tenant_id is not None:
        query = query.filter(TntPlanExercise.tenant_id == tenant_id)
    return query.first()


def create_plan_exercise(db: Session, plan_exercise_in: PlanExerciseCreate) -> TntPlanExercise:
    """创建计划练习关系"""
    db_plan_exercise = TntPlanExercise(**plan_exercise_in.model_dump())
    db.add(db_plan_exercise)
    db.commit()
    db.refresh(db_plan_exercise)
    return db_plan_exercise


def update_plan_exercise(
    db: Session, 
    plan_exercise_id: int, 
    plan_exercise_in: PlanExerciseUpdate,
    tenant_id: Optional[int] = None
) -> Optional[TntPlanExercise]:
    """更新计划练习关系信息"""
    db_plan_exercise = get_plan_exercise(db, plan_exercise_id, tenant_id)
    if not db_plan_exercise:
        return None

    update_data = plan_exercise_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_plan_exercise, field, value)

    db.commit()
    db.refresh(db_plan_exercise)
    return db_plan_exercise


def delete_plan_exercise(db: Session, plan_exercise_id: int, tenant_id: Optional[int] = None) -> bool:
    """删除计划练习关系"""
    db_plan_exercise = get_plan_exercise(db, plan_exercise_id, tenant_id)
    if not db_plan_exercise:
        return False

    db.delete(db_plan_exercise)
    db.commit()
    return True


def batch_update_plan_exercise_order(
    db: Session,
    tenant_id: int,
    plan_id: int,
    plan_exercise_orders: List[PlanExerciseOrderItem]
) -> Tuple[List[TntPlanExercise], int, int]:
    """批量调整计划练习关系顺序
    
    Args:
        db: 数据库会话
        tenant_id: 租户ID
        plan_id: 计划ID
        plan_exercise_orders: 计划练习关系顺序列表

    Returns:
        更新后的计划练习关系列表、成功数量、总数量的元组
    """
    updated_plan_exercises = []
    success_count = 0
    total_count = len(plan_exercise_orders)

    try:
        # 在一个事务中执行所有更新
        for order_item in plan_exercise_orders:
            # 查找该租户和计划下的计划练习关系
            db_plan_exercise = db.query(TntPlanExercise).filter(
                and_(
                    TntPlanExercise.id == order_item.id,
                    TntPlanExercise.tenant_id == tenant_id,
                    TntPlanExercise.pid == plan_id
                )
            ).first()
            
            if db_plan_exercise:
                # 更新 priority
                db_plan_exercise.priority = order_item.priority
                updated_plan_exercises.append(db_plan_exercise)
                success_count += 1
        
        # 所有更新完成后一次性提交
        db.commit()
        
        # 刷新所有更新的对象
        for plan_exercise in updated_plan_exercises:
            db.refresh(plan_exercise)
            
    except Exception as e:
        # 如果有任何错误，回滚事务
        db.rollback()
        raise e

    return updated_plan_exercises, success_count, total_count
