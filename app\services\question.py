import logging
from datetime import datetime
from typing import List, Optional, Tuple

from sqlalchemy.orm import Session

from app.models.models import (
    TntModule,
    TntQuestion,
    TntQuestionGuide,
    TntQuestionModule,
    TntQuestionSubject,
    TntSubject,
    TntWorksheetAsm,
)
from app.schemas.question import (
    Question<PERSON><PERSON>,
    QuestionGuideCreate,
    QuestionGuideOrderItem,
    QuestionGuideUpdate,
    QuestionModuleBatchUpdate,
    QuestionSubjectBatchUpdate,
    QuestionUpdate,
)

logger = logging.getLogger(__name__)


def get_questions_by_unit(
    db: Session,
    tenant_id: int,
    worksheet_id: int,
    unit_id: int,
) -> List[TntQuestion]:
    """获取指定单元下的问题列表（按priority排序）"""
    return (
        db.query(TntQuestion)
        .join(TntWorksheetAsm, TntQuestion.id == TntWorksheetAsm.qid)
        .filter(
            TntWorksheetAsm.tenant_id == tenant_id,
            TntWorksheetAsm.wid == worksheet_id,
            TntWorksheetAsm.uid == unit_id,
            TntQuestion.tenant_id == tenant_id,
        )
        .order_by(TntWorksheetAsm.priority.asc())
        .all()
    )


def get_question(
    db: Session, question_id: int, tenant_id: Optional[int] = None
) -> Optional[TntQuestion]:
    """获取问题信息"""
    query = db.query(TntQuestion).filter(TntQuestion.id == question_id)
    if tenant_id is not None:
        query = query.filter(TntQuestion.tenant_id == tenant_id)
    return query.first()


def get_questions(
    db: Session,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    subject_id: Optional[int] = None,
    title: Optional[str] = None,
    notes: Optional[str] = None,
) -> Tuple[List[TntQuestion], int]:
    """获取问题列表

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        skip: 跳过的记录数，用于分页
        limit: 返回的最大记录数，用于分页
        active: 可选，问题状态筛选
        sort_by: 可选，排序字段，可选值：id, ctime
        sort_order: 可选，排序方式，可选值：asc, desc
        start_time: 可选，创建时间的开始时间
        end_time: 可选，创建时间的结束时间
        subject_id: 可选，按关联的主题ID筛选
        title: 可选，按标题关键字模糊搜索
        notes: 可选，按备注关键字模糊搜索

    Returns:
        问题列表和总数的元组
    """
    query = db.query(TntQuestion).filter(TntQuestion.tenant_id == tenant_id)
    
    # 如果需要按主题ID筛选，添加JOIN条件
    if subject_id is not None:
        query = query.join(TntQuestionSubject, TntQuestion.id == TntQuestionSubject.qid).filter(
            TntQuestionSubject.sid == subject_id,
            TntQuestionSubject.tenant_id == tenant_id
        ).distinct()
    
    if active is not None:
        query = query.filter(TntQuestion.active == active)

    # 按创建时间区间筛选
    if start_time:
        query = query.filter(TntQuestion.ctime >= start_time)
    if end_time:
        query = query.filter(TntQuestion.ctime <= end_time)

    # 按标题关键字模糊搜索
    if title:
        query = query.filter(TntQuestion.title.contains(title))
    
    # 按备注关键字模糊搜索
    if notes:
        query = query.filter(TntQuestion.notes.contains(notes))

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntQuestion.id.asc())
            else:
                query = query.order_by(TntQuestion.id.desc())
        elif sort_by == "ctime":
            if sort_order == "asc":
                query = query.order_by(TntQuestion.ctime.asc())
            else:
                query = query.order_by(TntQuestion.ctime.desc())

    total = query.count()
    questions = query.offset(skip).limit(limit).all()
    return questions, total


def create_question(db: Session, question_in: QuestionCreate) -> TntQuestion:
    """创建问题"""
    db_question = TntQuestion(**question_in.model_dump())
    db.add(db_question)
    db.commit()
    db.refresh(db_question)
    return db_question


def update_question(
    db: Session,
    question_id: int,
    question_in: QuestionUpdate,
    tenant_id: Optional[int] = None,
) -> Optional[TntQuestion]:
    """更新问题信息"""
    db_question = get_question(db, question_id, tenant_id)
    if not db_question:
        return None

    update_data = question_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_question, field, value)

    db.commit()
    db.refresh(db_question)
    return db_question


def delete_question(
    db: Session, question_id: int, tenant_id: Optional[int] = None
) -> bool:
    """删除问题"""
    db_question = get_question(db, question_id, tenant_id)
    if not db_question:
        return False

    db.delete(db_question)
    db.commit()
    return True


# 问题作答指南相关函数
def get_question_guides(
    db: Session,
    tenant_id: int,
    question_id: int,
    skip: int = 0,
    limit: int = 100,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
) -> Tuple[List[TntQuestionGuide], int]:
    """获取问题作答指南列表"""
    query = db.query(TntQuestionGuide).filter(
        TntQuestionGuide.tenant_id == tenant_id, TntQuestionGuide.qid == question_id
    )

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntQuestionGuide.id.asc())
            else:
                query = query.order_by(TntQuestionGuide.id.desc())
        elif sort_by == "priority":
            if sort_order == "asc":
                query = query.order_by(TntQuestionGuide.priority.asc())
            else:
                query = query.order_by(TntQuestionGuide.priority.desc())
    else:
        # 默认按优先级排序
        query = query.order_by(TntQuestionGuide.priority.asc())

    total = query.count()
    guides = query.offset(skip).limit(limit).all()
    return guides, total


def get_question_guide(
    db: Session, guide_id: int, tenant_id: Optional[int] = None
) -> Optional[TntQuestionGuide]:
    """获取单个问题作答指南"""
    query = db.query(TntQuestionGuide).filter(TntQuestionGuide.id == guide_id)
    if tenant_id is not None:
        query = query.filter(TntQuestionGuide.tenant_id == tenant_id)
    return query.first()


def create_question_guide(
    db: Session, guide_in: QuestionGuideCreate
) -> TntQuestionGuide:
    """创建问题作答指南"""
    # 获取当前租户下该问题的作答指南中 priority 的最大值
    max_priority = (
        db.query(TntQuestionGuide)
        .filter(
            TntQuestionGuide.tenant_id == guide_in.tenant_id,
            TntQuestionGuide.qid == guide_in.qid,
        )
        .order_by(TntQuestionGuide.priority.desc())
        .first()
    )

    # 设置新的 priority 值
    new_priority = (max_priority.priority + 1) if max_priority else 1

    # 创建作答指南数据，覆盖 priority 值
    guide_data = guide_in.model_dump()
    guide_data["priority"] = new_priority

    db_guide = TntQuestionGuide(**guide_data)
    db.add(db_guide)
    db.commit()
    db.refresh(db_guide)
    return db_guide


def update_question_guide(
    db: Session,
    guide_id: int,
    guide_in: QuestionGuideUpdate,
    tenant_id: Optional[int] = None,
) -> Optional[TntQuestionGuide]:
    """更新问题作答指南"""
    db_guide = get_question_guide(db, guide_id, tenant_id)
    if not db_guide:
        return None

    update_data = guide_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_guide, field, value)

    db.commit()
    db.refresh(db_guide)
    return db_guide


def delete_question_guide(
    db: Session, guide_id: int, tenant_id: Optional[int] = None
) -> bool:
    """删除问题作答指南"""
    db_guide = get_question_guide(db, guide_id, tenant_id)
    if not db_guide:
        return False

    db.delete(db_guide)
    db.commit()
    return True


def batch_update_question_guide_order(
    db: Session, tenant_id: int, guide_orders: List[QuestionGuideOrderItem]
) -> Tuple[List[TntQuestionGuide], int, int]:
    """批量更新问题作答指南顺序

    Returns:
        Tuple[更新后的作答指南列表, 成功更新数量, 总请求数量]
    """
    success_count = 0
    updated_guides = []

    try:
        # 在一个事务中执行所有更新
        for order_item in guide_orders:
            # 验证作答指南是否属于该租户
            db_guide = (
                db.query(TntQuestionGuide)
                .filter(
                    TntQuestionGuide.id == order_item.id,
                    TntQuestionGuide.tenant_id == tenant_id,
                )
                .first()
            )

            if db_guide:
                db_guide.priority = order_item.priority
                updated_guides.append(db_guide)
                success_count += 1

        # 所有更新完成后一次性提交
        if updated_guides:
            db.commit()
            # 刷新所有更新的作答指南数据
            for guide in updated_guides:
                db.refresh(guide)
                
    except Exception as e:
        # 如果有任何错误，回滚事务
        db.rollback()
        raise e

    return updated_guides, success_count, len(guide_orders)


# 问题-理论模块关系相关函数
def get_question_modules(
    db: Session,
    tenant_id: int,
    question_id: int,
    skip: int = 0,
    limit: int = 100,
) -> Tuple[List[TntQuestionModule], int]:
    """获取问题-理论模块关系列表"""
    query = (
        db.query(TntQuestionModule)
        .join(TntModule, TntQuestionModule.mid == TntModule.id)
        .filter(
            TntQuestionModule.tenant_id == tenant_id,
            TntQuestionModule.qid == question_id,
        )
    )

    total = query.count()
    modules = query.offset(skip).limit(limit).all()
    return modules, total


def batch_update_question_modules(
    db: Session, batch_in: QuestionModuleBatchUpdate
) -> bool:
    """批量更新问题-理论模块关系

    计算增量更新：只删除不需要的关系，只添加新的关系

    Returns:
        bool: 操作是否成功
    """
    try:
        # 1. 获取当前已存在的理论模块关系（查询操作，不需要在事务中）
        existing_modules = (
            db.query(TntQuestionModule)
            .filter(
                TntQuestionModule.qid == batch_in.question_id,
                TntQuestionModule.tenant_id == batch_in.tenant_id,
            )
            .all()
        )

        # 2. 计算当前存在的模块ID集合
        existing_module_ids = {module.mid for module in existing_modules}

        # 3. 计算新的模块ID集合
        new_module_ids = set(batch_in.module_ids)

        # 4. 计算需要删除的模块ID（存在于数据库但不在新列表中）
        modules_to_delete = existing_module_ids - new_module_ids

        # 5. 计算需要添加的模块ID（在新列表中但不存在于数据库）
        modules_to_add = new_module_ids - existing_module_ids

        # 6. 如果没有变化，直接返回成功
        if not modules_to_delete and not modules_to_add:
            return True



        # 7. 开始事务：删除不需要的关系
        for module in existing_modules:
            if module.mid in modules_to_delete:
                db.delete(module)

        # 8. 添加新的关系
        for module_id in modules_to_add:
            # 检查理论模块是否存在（可选验证）
            # 这里可以添加对TntModule表的验证，确保module_id存在

            db_module = TntQuestionModule(
                tenant_id=batch_in.tenant_id, qid=batch_in.question_id, mid=module_id
            )
            db.add(db_module)

        db.commit()
        return True
    except Exception as e:
        db.rollback()
        logger.error(
            f"问题ID {batch_in.question_id} 的理论模块关系更新失败: {str(e)}",
            exc_info=True,
        )
        return False


# 问题-主题关系相关函数
def get_question_subjects(
    db: Session,
    tenant_id: int,
    question_id: int,
    skip: int = 0,
    limit: int = 100,
) -> Tuple[List[TntQuestionSubject], int]:
    """获取问题-主题关系列表"""
    query = (
        db.query(TntQuestionSubject)
        .join(TntSubject, TntQuestionSubject.sid == TntSubject.id)
        .filter(
            TntQuestionSubject.tenant_id == tenant_id,
            TntQuestionSubject.qid == question_id,
        )
    )

    total = query.count()
    subjects = query.offset(skip).limit(limit).all()
    return subjects, total


def batch_update_question_subjects(
    db: Session, batch_in: QuestionSubjectBatchUpdate
) -> bool:
    """批量更新问题-主题关系

    计算增量更新：只删除不需要的关系，只添加新的关系

    Returns:
        bool: 操作是否成功
    """
    try:
        # 1. 获取当前已存在的主题关系（查询操作，不需要在事务中）
        existing_subjects = (
            db.query(TntQuestionSubject)
            .filter(
                TntQuestionSubject.qid == batch_in.question_id,
                TntQuestionSubject.tenant_id == batch_in.tenant_id,
            )
            .all()
        )

        # 2. 计算当前存在的主题ID集合
        existing_subject_ids = {subject.sid for subject in existing_subjects}

        # 3. 计算新的主题ID集合
        new_subject_ids = set(batch_in.subject_ids)

        # 4. 计算需要删除的主题ID（存在于数据库但不在新列表中）
        subjects_to_delete = existing_subject_ids - new_subject_ids

        # 5. 计算需要添加的主题ID（在新列表中但不存在于数据库）
        subjects_to_add = new_subject_ids - existing_subject_ids

        # 6. 如果没有变化，直接返回成功
        if not subjects_to_delete and not subjects_to_add:
            return True



        # 7. 开始事务：删除不需要的关系
        for subject in existing_subjects:
            if subject.sid in subjects_to_delete:
                db.delete(subject)

        # 8. 添加新的关系
        for subject_id in subjects_to_add:
            # 检查主题是否存在（可选验证）
            # 这里可以添加对TntSubject表的验证，确保subject_id存在

            db_subject = TntQuestionSubject(
                tenant_id=batch_in.tenant_id, qid=batch_in.question_id, sid=subject_id
            )
            db.add(db_subject)

        db.commit()
        return True
    except Exception as e:
        db.rollback()
        logger.error(
            f"问题ID {batch_in.question_id} 的主题关系更新失败: {str(e)}",
            exc_info=True,
        )
        return False


def get_question_relations(
    db: Session,
    tenant_id: int,
    question_id: int,
) -> Tuple[List[TntQuestionModule], int, List[TntQuestionSubject], int]:
    """获取问题关联的所有理论模块和主题

    Returns:
        Tuple[理论模块列表, 理论模块总数, 主题列表, 主题总数]
    """
    # 获取所有理论模块关系（不分页）
    modules, module_total = get_question_modules(
        db=db,
        tenant_id=tenant_id,
        question_id=question_id,
        skip=0,
        limit=10000,  # 设置一个很大的数字来获取所有数据
    )

    # 获取所有主题关系（不分页）
    subjects, subject_total = get_question_subjects(
        db=db,
        tenant_id=tenant_id,
        question_id=question_id,
        skip=0,
        limit=10000,  # 设置一个很大的数字来获取所有数据
    )

    return modules, module_total, subjects, subject_total
