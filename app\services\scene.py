from datetime import datetime
from typing import List, Optional, Tu<PERSON>

from sqlalchemy.orm import Session, joinedload

from app.models.models import TntExercise, TntScene, TntCharacter, TntSceneCharacter, TntSceneGuide, TntCue, TntLine
from app.schemas.scene import SceneCreate, SceneUpdate, SceneCharacterCreate, SceneGuideCreate, SceneGuideUpdate


def get_scene(db: Session, scene_id: int, tenant_id: Optional[int] = None) -> Optional[TntScene]:
    """获取场景信息"""
    query = db.query(TntScene).join(TntExercise).filter(
        TntScene.id == scene_id
    ).options(joinedload(TntScene.exercise))
    
    if tenant_id is not None:
        query = query.filter(TntScene.tenant_id == tenant_id)
    return query.first()


def get_scenes(
    db: Session,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    title: Optional[str] = None,
    published: Optional[bool] = None,
    active: Optional[bool] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
) -> Tuple[List[TntScene], int]:
    """获取场景列表

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        skip: 跳过的记录数，用于分页
        limit: 返回的最大记录数，用于分页
        title: 可选，按场景标题搜索，支持模糊匹配
        published: 可选，按发布状态搜索
        active: 可选，按激活状态搜索
        sort_by: 可选，排序字段，可选值：id, ctime
        sort_order: 可选，排序方式，可选值：asc, desc
        start_time: 可选，创建时间的开始时间
        end_time: 可选，创建时间的结束时间

    Returns:
        (场景列表, 总数)
    """
    # 基础查询，联合查询exercise表
    query = db.query(TntScene).join(TntExercise).filter(
        TntScene.tenant_id == tenant_id
    ).options(joinedload(TntScene.exercise))

    # 按标题搜索
    if title:
        query = query.filter(TntExercise.title.like(f"%{title}%"))

    # 按发布状态搜索
    if published is not None:
        query = query.filter(TntExercise.published == published)

    # 按激活状态搜索
    if active is not None:
        # 将bool转换为int：True -> 1, False -> 0
        active_value = 1 if active else 0
        query = query.filter(TntExercise.active == active_value)

    # 按创建时间区间筛选
    if start_time:
        query = query.filter(TntExercise.ctime >= start_time)
    if end_time:
        query = query.filter(TntExercise.ctime <= end_time)

    # 计算总数
    total = query.count()

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntScene.id.asc())
            else:
                query = query.order_by(TntScene.id.desc())
        elif sort_by == "ctime":
            if sort_order == "asc":
                query = query.order_by(TntExercise.ctime.asc())
            else:
                query = query.order_by(TntExercise.ctime.desc())

    scenes = query.offset(skip).limit(limit).all()
    return scenes, total


def create_scene(db: Session, scene_in: SceneCreate) -> TntScene:
    """创建场景"""
    try:
        # 开始事务
        # 首先创建exercise，type=2（角色扮演）
        exercise_data = {
            "tenant_id": scene_in.tenant_id,
            "title": scene_in.title,
            "type": 2,  # 角色扮演类型
            "intro": scene_in.intro,
            "duration": scene_in.duration,
            "version": scene_in.version,
            "bgtext": None,  # 默认为 None
            "bgvideo": None,  # 默认为 None
            "notes": scene_in.notes,
            "published": 0,  # 默认为 0（未发布）
        }
        
        db_exercise = TntExercise(**exercise_data)
        db.add(db_exercise)
        db.flush()  # 刷新以获取exercise的ID，但不提交
        
        # 创建scene
        db_scene = TntScene(
            tenant_id=scene_in.tenant_id,
            eid=db_exercise.id,
            pv_scripts=""  # 默认为空字符串
        )
        db.add(db_scene)
        
        # 提交事务
        db.commit()
        
        # 刷新对象以获取最新数据
        db.refresh(db_scene)
        db.refresh(db_exercise)
        
        # 加载关联数据
        db_scene_with_exercise = db.query(TntScene).filter(
            TntScene.id == db_scene.id
        ).options(joinedload(TntScene.exercise)).first()
        
        if not db_scene_with_exercise:
            raise Exception("创建场景后无法重新加载数据")
        
        return db_scene_with_exercise
        
    except Exception as e:
        db.rollback()
        raise e


def update_scene(
    db: Session, scene_id: int, scene_in: SceneUpdate, tenant_id: Optional[int] = None
) -> Optional[TntScene]:
    """更新场景信息"""
    query = db.query(TntScene).join(TntExercise).filter(
        TntScene.id == scene_id
    ).options(joinedload(TntScene.exercise))
    
    if tenant_id is not None:
        query = query.filter(TntScene.tenant_id == tenant_id)
    
    db_scene = query.first()
    if not db_scene:
        return None

    try:
        # 更新scene特有字段
        scene_update_data = scene_in.model_dump(
            exclude_unset=True,
            include={'pv_scripts'}
        )
        
        if scene_update_data:
            for field, value in scene_update_data.items():
                setattr(db_scene, field, value)

        # 更新exercise相关字段
        exercise_update_data = scene_in.model_dump(
            exclude_unset=True,
            include={'title', 'intro', 'duration', 'version', 'bgtext', 'bgvideo', 'notes', 'published', 'active'}
        )
        
        if exercise_update_data:
            for field, value in exercise_update_data.items():
                setattr(db_scene.exercise, field, value)

        db.commit()
        
        # 重新加载数据
        db.refresh(db_scene)
        return db_scene
        
    except Exception as e:
        db.rollback()
        raise e


def delete_scene(db: Session, scene_id: int, tenant_id: Optional[int] = None) -> bool:
    """删除场景"""
    query = db.query(TntScene).filter(TntScene.id == scene_id)
    if tenant_id is not None:
        query = query.filter(TntScene.tenant_id == tenant_id)
    
    db_scene = query.first()
    if not db_scene:
        return False

    try:
        # 在事务中删除scene和对应的exercise
        exercise_id = db_scene.eid
        
        # 删除scene
        db.delete(db_scene)
        
        # 删除对应的exercise
        db_exercise = db.query(TntExercise).filter(TntExercise.id == exercise_id).first()
        if db_exercise:
            db.delete(db_exercise)
        
        db.commit()
        return True
        
    except Exception as e:
        db.rollback()
        raise e


# 场景-人物关系相关方法

def get_scene_characters(
    db: Session,
    tenant_id: int,
    scene_id: int,
) -> List[TntSceneCharacter]:
    """获取场景的人物列表"""
    return db.query(TntSceneCharacter).join(TntCharacter).filter(
        TntSceneCharacter.tenant_id == tenant_id,
        TntSceneCharacter.sid == scene_id,
        TntCharacter.published == 1,
        TntCharacter.active == 1
    ).options(joinedload(TntSceneCharacter.character)).order_by(TntSceneCharacter.priority.asc()).all()


def create_scene_character(db: Session, scene_character_in: SceneCharacterCreate) -> TntSceneCharacter:
    """创建场景-人物关系"""
    # 检查关系是否已存在
    existing = db.query(TntSceneCharacter).filter(
        TntSceneCharacter.tenant_id == scene_character_in.tenant_id,
        TntSceneCharacter.sid == scene_character_in.sid,
        TntSceneCharacter.cid == scene_character_in.cid
    ).first()
    if existing:
        raise ValueError("场景-人物关系已存在")
    
    try:
        # 获取当前租户下该场景的场景-人物关系中 priority 的最大值
        max_priority = (
            db.query(TntSceneCharacter)
            .filter(
                TntSceneCharacter.tenant_id == scene_character_in.tenant_id,
                TntSceneCharacter.sid == scene_character_in.sid,
            )
            .order_by(TntSceneCharacter.priority.desc())
            .first()
        )

        # 设置新的 priority 值
        new_priority = (max_priority.priority + 1) if max_priority else 1

        # 创建场景-人物关系数据，覆盖 priority 值
        scene_character_data = scene_character_in.model_dump()
        scene_character_data["priority"] = new_priority
        
        db_scene_character = TntSceneCharacter(**scene_character_data)
        db.add(db_scene_character)
        db.commit()
        db.refresh(db_scene_character)
        
        # 加载关联数据
        result = db.query(TntSceneCharacter).filter(
            TntSceneCharacter.id == db_scene_character.id
        ).options(joinedload(TntSceneCharacter.character)).first()
        
        return result
        
    except Exception as e:
        db.rollback()
        raise e


def batch_update_scene_character_order(
    db: Session,
    tenant_id: int,
    scene_id: int,
    character_orders: List[dict],
) -> Tuple[List[TntSceneCharacter], int, int]:
    """批量调整场景人物关系顺序"""
    
    try:
        updated_characters = []
        success_count = 0
        total_count = len(character_orders)
        
        for order_item in character_orders:
            character_id = order_item["id"]
            priority = order_item["priority"]
            
            # 查找并更新
            scene_character = db.query(TntSceneCharacter).filter(
                TntSceneCharacter.id == character_id,
                TntSceneCharacter.tenant_id == tenant_id,
                TntSceneCharacter.sid == scene_id
            ).first()
            
            if scene_character:
                scene_character.priority = priority
                success_count += 1
                updated_characters.append(scene_character)
        
        db.commit()
        
        # 重新加载更新后的数据
        final_characters = db.query(TntSceneCharacter).filter(
            TntSceneCharacter.tenant_id == tenant_id,
            TntSceneCharacter.sid == scene_id
        ).options(joinedload(TntSceneCharacter.character)).order_by(TntSceneCharacter.priority.asc()).all()
        
        return final_characters, success_count, total_count
        
    except Exception as e:
        db.rollback()
        raise e


def batch_delete_scene_characters(
    db: Session,
    tenant_id: int,
    scene_id: int,
    character_ids: List[int],
) -> Tuple[List[int], int]:
    """批量删除场景人物关系"""
    
    try:
        deleted_ids = []
        
        for character_id in character_ids:
            scene_character = db.query(TntSceneCharacter).filter(
                TntSceneCharacter.id == character_id,
                TntSceneCharacter.tenant_id == tenant_id,
                TntSceneCharacter.sid == scene_id
            ).first()
            
            if scene_character:
                deleted_ids.append(character_id)
                db.delete(scene_character)
        
        db.commit()
        success_count = len(deleted_ids)
        
        return deleted_ids, success_count
        
    except Exception as e:
        db.rollback()
        raise e


def update_scene_character(
    db: Session,
    scene_character_id: int,
    tenant_id: int,
    played: int,
) -> Optional[TntSceneCharacter]:
    """更新场景人物关系"""
    
    # 查找场景人物关系
    scene_character = db.query(TntSceneCharacter).filter(
        TntSceneCharacter.id == scene_character_id,
        TntSceneCharacter.tenant_id == tenant_id
    ).first()
    
    if not scene_character:
        return None
    
    try:
        # 更新 played 字段
        scene_character.played = played
        db.commit()
        
        # 重新加载数据并包含关联的人物信息
        updated_character = db.query(TntSceneCharacter).filter(
            TntSceneCharacter.id == scene_character_id
        ).options(joinedload(TntSceneCharacter.character)).first()
        
        return updated_character
        
    except Exception as e:
        db.rollback()
        raise e


# 场景指南相关方法

def get_scene_guides(
    db: Session,
    tenant_id: int,
    scene_id: int,
    skip: int = 0,
    limit: int = 100,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
) -> Tuple[List[TntSceneGuide], int]:
    """获取场景指南列表"""
    
    query = db.query(TntSceneGuide).filter(
        TntSceneGuide.tenant_id == tenant_id,
        TntSceneGuide.sid == scene_id
    )
    
    # 计算总数
    total = query.count()
    
    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntSceneGuide.id.asc())
            else:
                query = query.order_by(TntSceneGuide.id.desc())
        elif sort_by == "priority":
            if sort_order == "asc":
                query = query.order_by(TntSceneGuide.priority.asc())
            else:
                query = query.order_by(TntSceneGuide.priority.desc())
    else:
        # 默认按priority升序排列
        query = query.order_by(TntSceneGuide.priority.asc())
    
    guides = query.offset(skip).limit(limit).all()
    return guides, total


def create_scene_guide(db: Session, guide_in: SceneGuideCreate) -> TntSceneGuide:
    """创建场景指南"""
    # 获取当前租户下该场景的指南中 priority 的最大值
    max_priority = (
        db.query(TntSceneGuide)
        .filter(
            TntSceneGuide.tenant_id == guide_in.tenant_id,
            TntSceneGuide.sid == guide_in.sid,
        )
        .order_by(TntSceneGuide.priority.desc())
        .first()
    )

    # 设置新的 priority 值
    new_priority = (max_priority.priority + 1) if max_priority else 1

    # 创建场景指南数据，覆盖 priority 值
    guide_data = guide_in.model_dump()
    guide_data["priority"] = new_priority

    db_guide = TntSceneGuide(**guide_data)
    db.add(db_guide)
    db.commit()
    db.refresh(db_guide)
    return db_guide


def get_scene_guide(db: Session, guide_id: int, tenant_id: int) -> Optional[TntSceneGuide]:
    """获取场景指南信息"""
    return db.query(TntSceneGuide).filter(
        TntSceneGuide.id == guide_id,
        TntSceneGuide.tenant_id == tenant_id
    ).first()


def update_scene_guide(
    db: Session, guide_id: int, guide_in: SceneGuideUpdate, tenant_id: int
) -> Optional[TntSceneGuide]:
    """更新场景指南"""
    
    db_guide = db.query(TntSceneGuide).filter(
        TntSceneGuide.id == guide_id,
        TntSceneGuide.tenant_id == tenant_id
    ).first()
    
    if not db_guide:
        return None
    
    try:
        update_data = guide_in.model_dump(exclude_unset=True)
        if update_data:
            for field, value in update_data.items():
                setattr(db_guide, field, value)
        
        db.commit()
        db.refresh(db_guide)
        return db_guide
        
    except Exception as e:
        db.rollback()
        raise e


def delete_scene_guide(db: Session, guide_id: int, tenant_id: int) -> bool:
    """删除场景指南"""
    
    db_guide = db.query(TntSceneGuide).filter(
        TntSceneGuide.id == guide_id,
        TntSceneGuide.tenant_id == tenant_id
    ).first()
    
    if not db_guide:
        return False
    
    try:
        db.delete(db_guide)
        db.commit()
        return True
        
    except Exception as e:
        db.rollback()
        raise e


def batch_update_scene_guide_order(
    db: Session, 
    tenant_id: int, 
    guide_orders: List[dict]
) -> Tuple[List[TntSceneGuide], int, int]:
    """批量调整场景指南顺序
    
    Args:
        db: 数据库会话
        tenant_id: 租户ID
        guide_orders: 指南顺序列表，格式为 [{"id": guide_id, "priority": priority}, ...]
    
    Returns:
        Tuple[更新后的指南列表, 成功更新数量, 总请求数量]
    """
    success_count = 0
    updated_guides = []

    try:
        # 在一个事务中执行所有更新
        for order_item in guide_orders:
            guide_id = order_item["id"]
            priority = order_item["priority"]
            
            # 验证指南是否属于该租户
            db_guide = db.query(TntSceneGuide).filter(
                TntSceneGuide.id == guide_id,
                TntSceneGuide.tenant_id == tenant_id,
            ).first()

            if db_guide:
                db_guide.priority = priority
                updated_guides.append(db_guide)
                success_count += 1

        # 所有更新完成后一次性提交
        if updated_guides:
            db.commit()
            # 刷新所有更新的指南数据
            for guide in updated_guides:
                db.refresh(guide)
                
    except Exception as e:
        # 如果有任何错误，回滚事务
        db.rollback()
        raise e

    return updated_guides, success_count, len(guide_orders)


def generate_and_save_scene_script(
    db: Session,
    scene_id: int,
    tenant_id: int,
) -> Optional[TntScene]:
    """生成并保存场景剧情脚本
    
    遍历场景下的所有cue，为每个cue生成一行脚本。
    如果一个cue只对应一个line，格式为：序号.如发言内容是："触发内容"时，返回[{"人物名":"line_id"}]
    如果一个cue对应多个line，格式为：序号.如发言内容是："触发内容"时，返回[{"人物1":"line_id1"},{"人物2":"line_id2"}]
    
    Args:
        db: 数据库会话
        scene_id: 场景ID
        tenant_id: 租户ID
    
    Returns:
        更新后的场景对象，如果场景不存在则返回None
    """
    # 验证场景是否存在且属于指定租户
    db_scene = get_scene(db, scene_id, tenant_id)
    if not db_scene:
        return None
    
    try:
        # 获取场景下的所有cue，按priority排序
        cues = db.query(TntCue).filter(
            TntCue.tenant_id == tenant_id,
            TntCue.sid == scene_id,
            TntCue.active == 1
        ).options(
            joinedload(TntCue.character)
        ).order_by(TntCue.priority.asc()).all()
        
        script_lines = []
        cue_counter = 1
        
        for cue in cues:
            # 获取该cue下的所有line，按priority排序
            lines = db.query(TntLine).filter(
                TntLine.tenant_id == tenant_id,
                TntLine.cueid == cue.id,
                TntLine.active == 1
            ).options(
                joinedload(TntLine.character)
            ).order_by(TntLine.priority.asc()).all()
            
            if lines:  # 只有当cue有对应的line时才生成脚本行
                # 构建返回的人物和line_id映射列表
                line_mappings = []
                for line in lines:
                    line_mappings.append(f'{{"{line.character.name}":"{line.id}"}}')
                
                # 生成脚本行：每个cue生成一行
                script_line = f'{cue_counter}.如发言内容是："{cue.content}"时，返回[{",".join(line_mappings)}]'
                script_lines.append(script_line)
                cue_counter += 1
        
        # 生成完整的脚本内容
        script_content = "\n".join(script_lines)
        
        # 更新场景的pv_scripts字段
        db_scene.pv_scripts = script_content
        db.commit()
        db.refresh(db_scene)
        
        return db_scene
        
    except Exception as e:
        db.rollback()
        raise e
