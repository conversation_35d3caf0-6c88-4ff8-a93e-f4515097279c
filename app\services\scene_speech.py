from typing import List, Optional

from sqlalchemy.orm import Session

from app.models.models import TntSceneSpeech
from app.schemas.scene_speech import SceneSpeechCreate, SceneSpeechUpdate


def get_scene_speech(db: Session, scene_speech_id: int, scene_id: Optional[int] = None, tenant_id: Optional[int] = None) -> Optional[TntSceneSpeech]:
    """获取场景语音信息"""
    query = db.query(TntSceneSpeech).filter(TntSceneSpeech.id == scene_speech_id)
    if tenant_id is not None:
        query = query.filter(TntSceneSpeech.tenant_id == tenant_id)
    if scene_id is not None:
        query = query.filter(TntSceneSpeech.elid == scene_id)
    return query.first()


def get_scene_speeches(
    db: Session,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    scene_id: Optional[int] = None,
    active: Optional[int] = None,
) -> List[TntSceneSpeech]:
    """获取场景语音列表"""
    query = db.query(TntSceneSpeech).filter(TntSceneSpeech.tenant_id == tenant_id)
    if scene_id is not None:
        # Note: mapping scene_id to elid field based on schema
        query = query.filter(TntSceneSpeech.elid == scene_id)
    # Note: active filtering not implemented as schema doesn't have this field
    return query.offset(skip).limit(limit).all()


def create_scene_speech(
    db: Session, scene_speech_in: SceneSpeechCreate
) -> TntSceneSpeech:
    """创建场景语音"""
    db_scene_speech = TntSceneSpeech(**scene_speech_in.model_dump())
    db.add(db_scene_speech)
    db.commit()
    db.refresh(db_scene_speech)
    return db_scene_speech


def update_scene_speech(
    db: Session, scene_speech_id: int, scene_speech_in: SceneSpeechUpdate,
    scene_id: Optional[int] = None, tenant_id: Optional[int] = None
) -> Optional[TntSceneSpeech]:
    """更新场景语音信息"""
    db_scene_speech = get_scene_speech(db, scene_speech_id, scene_id, tenant_id)
    if not db_scene_speech:
        return None

    update_data = scene_speech_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_scene_speech, field, value)

    db.commit()
    db.refresh(db_scene_speech)
    return db_scene_speech


def delete_scene_speech(db: Session, scene_speech_id: int, scene_id: Optional[int] = None, tenant_id: Optional[int] = None) -> bool:
    """删除场景语音"""
    db_scene_speech = get_scene_speech(db, scene_speech_id, scene_id, tenant_id)
    if not db_scene_speech:
        return False

    db.delete(db_scene_speech)
    db.commit()
    return True
