from datetime import datetime
from typing import List, Optional, Tuple

from sqlalchemy.orm import Session, joinedload

from app.core.security import get_password_hash
from app.models.models import TntStudent, SysUser
from app.schemas.student import StudentCreate, StudentUpdate


def get_student(db: Session, student_id: int) -> Optional[TntStudent]:
    """获取学员信息"""
    return (
        db.query(TntStudent)
        .options(joinedload(TntStudent.user))
        .filter(TntStudent.id == student_id)
        .first()
    )


def get_students(
    db: Session,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    active: Optional[int] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    name: Optional[str] = None,
    gender: Optional[int] = None,
    notes: Optional[str] = None,
) -> Tuple[List[TntStudent], int]:
    """获取学员列表

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        skip: 跳过的记录数，用于分页
        limit: 返回的最大记录数，用于分页
        active: 可选，按用户状态筛选（0：失效；1：有效）
        sort_by: 可选，排序字段，可选值：id, ctime
        sort_order: 可选，排序方式，可选值：asc, desc
        start_time: 可选，创建时间的开始时间
        end_time: 可选，创建时间的结束时间
        name: 可选，按姓名搜索，支持模糊匹配
        gender: 可选，按性别筛选（0：未知；1：男；2：女）
        notes: 可选，按备注搜索，支持模糊匹配

    Returns:
        学员列表和总数的元组
    """
    query = db.query(TntStudent).options(joinedload(TntStudent.user)).filter(TntStudent.tenant_id == tenant_id)

    # 按创建时间区间筛选、按active筛选或排序时需要 JOIN user 表
    need_join_user = active is not None or start_time or end_time or (sort_by == "ctime")
    if need_join_user:
        query = query.join(SysUser, TntStudent.uid == SysUser.id)
    
    if active is not None:
        query = query.filter(SysUser.active == active)
    
    # 按创建时间区间筛选
    if start_time:
        query = query.filter(SysUser.ctime >= start_time)
    if end_time:
        query = query.filter(SysUser.ctime <= end_time)
    
    # 按姓名搜索
    if name:
        query = query.filter(TntStudent.name.ilike(f"%{name}%"))
    
    # 按性别筛选
    if gender is not None:
        query = query.filter(TntStudent.gender == gender)
    
    # 按备注搜索
    if notes:
        query = query.filter(TntStudent.notes.ilike(f"%{notes}%"))

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntStudent.id.asc())
            else:
                query = query.order_by(TntStudent.id.desc())
        elif sort_by == "ctime":
            if sort_order == "asc":
                query = query.order_by(SysUser.ctime.asc())
            else:
                query = query.order_by(SysUser.ctime.desc())

    total = query.count()
    students = query.offset(skip).limit(limit).all()
    return students, total


def create_student(db: Session, student_in: StudentCreate) -> TntStudent:
    """创建学员"""
    try:
        # 创建系统用户
        db_user = SysUser(
            username=student_in.username,
            passwd=get_password_hash(student_in.password),
            token_version=0,
            active=True,
        )
        db.add(db_user)
        db.flush()  # 获取用户ID

        # 创建学员
        db_student = TntStudent(
            tenant_id=student_in.tenant_id,
            uid=db_user.id,
            name=student_in.name,
            gender=student_in.gender,
            notes=student_in.notes,
        )
        db.add(db_student)
        db.commit()
        db.refresh(db_student)

        # 重新查询以获取关联的用户信息
        return (
            db.query(TntStudent)
            .options(joinedload(TntStudent.user))
            .filter(TntStudent.id == db_student.id)
            .first()
        )
    except Exception as e:
        # 如果出现任何异常，回滚事务
        db.rollback()
        raise e


def update_student(
    db: Session, student_id: int, student_in: StudentUpdate
) -> Optional[TntStudent]:
    """更新学员信息"""
    db_student = get_student(db, student_id)
    if not db_student:
        return None

    # 更新系统用户
    if student_in.active is not None:
        user = db_student.user  # 使用已经加载的关联对象
        if user:
            user.active = student_in.active

    # 更新学员信息
    update_data = student_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        if field not in ["active"]:  # active字段已经在上面处理过了
            setattr(db_student, field, value)

    db.commit()
    db.refresh(db_student)
    return db_student
