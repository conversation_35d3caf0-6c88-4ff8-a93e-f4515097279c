from typing import List, Optional

from sqlalchemy.orm import Session

from app.models.models import TntSubject
from app.schemas.subject import SubjectCreate, SubjectUpdate


def get_subjects(
    db: Session,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    name: Optional[str] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
) -> tuple[List[TntSubject], int]:
    """获取科目列表"""
    query = db.query(TntSubject).filter(TntSubject.tenant_id == tenant_id)

    # 按名称搜索
    if name:
        query = query.filter(TntSubject.name.like(f"%{name}%"))

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntSubject.id.asc())
            else:
                query = query.order_by(TntSubject.id.desc())
        pass

    total = query.count()
    subjects = query.offset(skip).limit(limit).all()
    return subjects, total


def get_subject(db: Session, subject_id: int, tenant_id: Optional[int] = None) -> Optional[TntSubject]:
    """根据ID获取科目"""
    query = db.query(TntSubject).filter(TntSubject.id == subject_id)
    if tenant_id is not None:
        query = query.filter(TntSubject.tenant_id == tenant_id)
    return query.first()


def create_subject(db: Session, subject_in: SubjectCreate) -> TntSubject:
    """创建科目"""
    db_subject = TntSubject(**subject_in.model_dump())
    db.add(db_subject)
    db.commit()
    db.refresh(db_subject)
    return db_subject


def update_subject(
    db: Session, subject_id: int, subject_in: SubjectUpdate, tenant_id: Optional[int] = None
) -> Optional[TntSubject]:
    """更新科目"""
    query = db.query(TntSubject).filter(TntSubject.id == subject_id)
    if tenant_id is not None:
        query = query.filter(TntSubject.tenant_id == tenant_id)
    
    db_subject = query.first()
    if not db_subject:
        return None

    update_data = subject_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_subject, field, value)

    db.commit()
    db.refresh(db_subject)
    return db_subject


def delete_subject(db: Session, subject_id: int, tenant_id: Optional[int] = None) -> bool:
    """删除科目"""
    query = db.query(TntSubject).filter(TntSubject.id == subject_id)
    if tenant_id is not None:
        query = query.filter(TntSubject.tenant_id == tenant_id)
    
    db_subject = query.first()
    if not db_subject:
        return False

    db.delete(db_subject)
    db.commit()
    return True
