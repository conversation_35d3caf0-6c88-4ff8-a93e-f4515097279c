from datetime import datetime
from typing import List, Optional, Tuple

from sqlalchemy.orm import Session, joinedload

from app.core.security import get_password_hash
from app.models.models import SysAdmin, SysUser
from app.schemas.sys.admin import (
    SysAdminCreate,
    SysAdminUpdate,
)


def create_admin(db: Session, admin: SysAdminCreate) -> SysAdmin:
    """创建管理员"""
    try:
        # 创建系统用户
        db_user = SysUser(
            username=admin.username,
            passwd=get_password_hash(admin.password),
            token_version=0,
            active=True,
        )
        db.add(db_user)
        db.flush()  # 获取用户ID

        # 创建管理员
        db_admin = SysAdmin(
            uid=db_user.id,
            name=admin.name,
            role=admin.role,
        )
        db.add(db_admin)
        db.commit()
        db.refresh(db_admin)

        # 重新查询以获取关联的用户信息
        return (
            db.query(SysAdmin)
            .options(joinedload(SysAdmin.user))
            .filter(SysAdmin.id == db_admin.id)
            .first()
        )
    except Exception as e:
        # 如果出现任何异常，回滚事务
        db.rollback()
        raise e


def get_admin(db: Session, admin_id: int) -> Optional[SysAdmin]:
    """获取管理员"""
    return (
        db.query(SysAdmin)
        .options(joinedload(SysAdmin.user))
        .filter(SysAdmin.id == admin_id)
        .first()
    )


def get_admins(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    username: Optional[str] = None,
    name: Optional[str] = None,
    active: Optional[int] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
) -> Tuple[List[SysAdmin], int]:
    """获取管理员列表

    Args:
        db: 数据库会话
        skip: 跳过的记录数，用于分页
        limit: 返回的最大记录数，用于分页
        username: 可选，按用户名搜索
        name: 可选，按姓名搜索
        active: 可选，按用户状态筛选（0：失效；1：有效）
        sort_by: 可选，排序字段，可选值：id, ctime
        sort_order: 可选，排序方式，可选值：asc, desc
        start_time: 可选，创建时间的开始时间
        end_time: 可选，创建时间的结束时间

    Returns:
        管理员列表和总数的元组
    """
    query = db.query(SysAdmin).options(joinedload(SysAdmin.user))

    # 判断是否需要JOIN SysUser表
    # 当需要搜索用户名、按active筛选、按时间筛选或按ctime排序时，需要JOIN user表
    need_join_user = (
        username or active is not None or start_time or end_time or (sort_by == "ctime")
    )
    
    if need_join_user:
        query = query.join(SysUser, SysAdmin.uid == SysUser.id)

    if username:
        query = query.filter(SysUser.username.like(f"%{username}%"))
    
    if name:
        query = query.filter(SysAdmin.name.like(f"%{name}%"))

    if active is not None:
        query = query.filter(SysUser.active == active)

    # 按创建时间区间筛选
    if start_time:
        query = query.filter(SysUser.ctime >= start_time)
    if end_time:
        query = query.filter(SysUser.ctime <= end_time)

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(SysAdmin.id.asc())
            else:
                query = query.order_by(SysAdmin.id.desc())
        elif sort_by == "ctime":
            if sort_order == "asc":
                query = query.order_by(SysUser.ctime.asc())
            else:
                query = query.order_by(SysUser.ctime.desc())

    total = query.count()
    admins = query.offset(skip).limit(limit).all()
    return admins, total


def update_admin(
    db: Session, admin_id: int, admin: SysAdminUpdate
) -> Optional[SysAdmin]:
    """更新管理员"""
    db_admin = get_admin(db, admin_id)
    if not db_admin:
        return None

    # 更新系统用户
    if admin.username or admin.password or admin.active is not None:
        user = db_admin.user  # 使用已经加载的关联对象
        if user:
            if admin.username:
                user.username = admin.username
            if admin.password:
                user.passwd = get_password_hash(admin.password)
                user.token_version += 1
            if admin.active is not None:
                user.active = admin.active

    # 更新管理员信息
    for field, value in admin.model_dump(exclude_unset=True).items():
        if field not in [
            "password",
            "username",
            "active",
        ]:  # 这些字段已经在上面处理过了
            setattr(db_admin, field, value)

    db.commit()
    db.refresh(db_admin)
    return db_admin


def delete_admin(db: Session, admin_id: int) -> bool:
    """删除管理员"""
    db_admin = get_admin(db, admin_id)
    if not db_admin:
        return False

    # 删除关联的系统用户
    user = db_admin.user  # 使用已经加载的关联对象
    if user:
        db.delete(user)

    # 删除管理员
    db.delete(db_admin)
    db.commit()
    return True
