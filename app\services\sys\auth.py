from typing import Optional, <PERSON><PERSON>

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON>asswordBearer
from jose import JWTError, jwt
from pydantic import ValidationError
from sqlalchemy.orm import Session, joinedload

from app.core.config import settings
from app.core.security import create_access_token
from app.db.session import get_db
from app.models.models import SysAdmin, SysUser
from app.schemas.sys.auth import (
    SysAdminPasswordUpdate,
    SysAdminTokenPayload,
)
from app.services.auth import authenticate_user, update_user_password

reusable_oauth2 = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/sys/auth/login")


def get_current_sys_admin(
    db: Session = Depends(get_db), token: str = Depends(reusable_oauth2)
) -> SysAdmin:
    """获取当前系统管理员"""
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = SysAdminTokenPayload(**payload)
    except (JW<PERSON>rro<PERSON>, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    admin = db.query(SysAdmin).filter(SysAdmin.uid == token_data.uid).first()
    if not admin:
        raise HTTPException(status_code=404, detail="Not found")
    if not admin.user.active:
        raise HTTPException(status_code=400, detail="Inactive")

    return admin


def login(
    db: Session, username: str, password: str
) -> Tuple[Optional[SysAdmin], Optional[str], Optional[str]]:
    """系统管理员登录

    Returns:
        Tuple[Optional[SysAdmin], Optional[str], Optional[str]]: (admin, token, error_message)
    """
    user = authenticate_user(db, username, password)
    if not user:
        return None, None, "用户名或密码错误"

    admin = db.query(SysAdmin).filter(SysAdmin.uid == user.id).first()
    if not admin:
        return None, None, "该用户不是系统管理员"

    if not user.active:
        return None, None, "用户已被禁用"

    token = create_access_token(
        data={
            "uid": user.id,
            "utype": "sys",
        }
    )

    return admin, token, None


def logout(db: Session, user_id: int) -> None:
    """系统管理员登出"""
    user = db.query(SysUser).filter(SysUser.id == user_id).first()
    if user:
        user.token_version += 1
        db.commit()


def get_sys_admin_profile(db: Session, admin_id: int) -> Optional[SysAdmin]:
    """获取系统管理员个人信息"""
    return (
        db.query(SysAdmin)
        .options(joinedload(SysAdmin.user))
        .filter(SysAdmin.id == admin_id)
        .first()
    )


def update_sys_admin_password(
    db: Session, admin_id: int, password_update: SysAdminPasswordUpdate
) -> bool:
    """更新系统管理员密码"""
    admin = get_sys_admin_profile(db, admin_id)
    if not admin:
        return False

    return update_user_password(
        db, admin.uid, password_update.old_password, password_update.new_password
    )
