from typing import List, Optional

from sqlalchemy import or_
from sqlalchemy.orm import Session, selectinload

from app.models.models import SysBconf
from app.schemas.sys.bconf import BconfCreate, BconfUpdate


def create_bconf(db: Session, bconf: BconfCreate) -> SysBconf:
    """创建机器人设置"""
    db_bconf = SysBconf(**bconf.model_dump())
    db.add(db_bconf)
    db.commit()
    db.refresh(db_bconf, ['bot'])  # 刷新时加载关联数据
    return db_bconf


def get_bconf(db: Session, key: str) -> Optional[SysBconf]:
    """获取机器人设置"""
    return db.query(SysBconf).options(selectinload(SysBconf.bot)).filter(SysBconf.key == key).first()


def get_bconfs(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    keyword: Optional[str] = None,
) -> tuple[List[SysBconf], int]:
    """获取机器人设置列表"""
    query = db.query(SysBconf).options(selectinload(SysBconf.bot))
    if keyword:
        query = query.filter(
            or_(SysBconf.key.like(f"%{keyword}%"), SysBconf.notes.like(f"%{keyword}%"))
        )
    total = query.count()
    bconfs = query.offset(skip).limit(limit).all()
    return bconfs, total


def update_bconf(db: Session, key: str, bconf: BconfUpdate) -> Optional[SysBconf]:
    """更新机器人设置"""
    db_bconf = get_bconf(db, key)
    if not db_bconf:
        return None

    update_data = bconf.model_dump(exclude_unset=True)
    
    # 如果要更新key，需要特殊处理（因为key是主键）
    if 'key' in update_data and update_data['key'] != key:
        new_key = update_data.pop('key')
        
        # 检查新key是否已存在
        existing_bconf = get_bconf(db, new_key)
        if existing_bconf:
            raise ValueError(f"配置键名 '{new_key}' 已存在")
        
        # 创建新记录
        new_bconf_data = {
            'key': new_key,
            'bid': update_data.get('bid', db_bconf.bid),
            'notes': update_data.get('notes', db_bconf.notes),
        }
        new_db_bconf = SysBconf(**new_bconf_data)
        
        # 删除旧记录，添加新记录
        db.delete(db_bconf)
        db.add(new_db_bconf)
        db.commit()
        db.refresh(new_db_bconf, ['bot'])
        return new_db_bconf
    else:
        # 常规更新
        for field, value in update_data.items():
            setattr(db_bconf, field, value)
        
        db.commit()
        db.refresh(db_bconf, ['bot'])  # 刷新时加载关联数据
        return db_bconf


def delete_bconf(db: Session, key: str) -> bool:
    """删除机器人设置"""
    db_bconf = get_bconf(db, key)
    if not db_bconf:
        return False

    db.delete(db_bconf)
    db.commit()
    return True
