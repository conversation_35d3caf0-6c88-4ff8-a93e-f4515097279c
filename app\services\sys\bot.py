from typing import List, Optional

from sqlalchemy import or_
from sqlalchemy.orm import Session

from app.models.models import SysBot
from app.schemas.sys.bot import BotCreate, BotUpdate


def create_bot(db: Session, bot: BotCreate) -> SysBot:
    """创建机器人"""
    db_bot = SysBot(**bot.model_dump())
    db.add(db_bot)
    db.commit()
    db.refresh(db_bot)
    return db_bot


def get_bot(db: Session, bot_id: int) -> Optional[SysBot]:
    """获取机器人"""
    return db.query(SysBot).filter(SysBot.id == bot_id).first()


def get_bots(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    keyword: Optional[str] = None,
    active: Optional[int] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
) -> tuple[List[SysBot], int]:
    """获取机器人列表"""
    query = db.query(SysBot)
    if keyword:
        query = query.filter(or_(SysBot.name.like(f"%{keyword}%")))
    
    if active is not None:
        query = query.filter(SysBot.active == active)

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(SysBot.id.asc())
            else:
                query = query.order_by(SysBot.id.desc())

    total = query.count()
    bots = query.offset(skip).limit(limit).all()
    return bots, total


def update_bot(db: Session, bot_id: int, bot: BotUpdate) -> Optional[SysBot]:
    """更新机器人"""
    db_bot = get_bot(db, bot_id)
    if not db_bot:
        return None

    update_data = bot.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_bot, field, value)

    db.commit()
    db.refresh(db_bot)
    return db_bot


def delete_bot(db: Session, bot_id: int) -> bool:
    """删除机器人"""
    db_bot = get_bot(db, bot_id)
    if not db_bot:
        return False

    db.delete(db_bot)
    db.commit()
    return True
