from datetime import datetime
from typing import List, Optional

from sqlalchemy import or_
from sqlalchemy.orm import Session

from app.models.models import SysTenant
from app.schemas.sys.tenant import TenantCreate, TenantUpdate


def create_tenant(db: Session, tenant: TenantCreate) -> SysTenant:
    """创建租户"""
    db_tenant = SysTenant(**tenant.model_dump())
    db.add(db_tenant)
    db.commit()
    db.refresh(db_tenant)
    return db_tenant


def get_tenant(db: Session, tenant_id: int) -> Optional[SysTenant]:
    """获取租户"""
    return db.query(SysTenant).filter(SysTenant.id == tenant_id).first()


def get_tenant_by_code(db: Session, code: str) -> Optional[SysTenant]:
    """通过代号获取租户"""
    return db.query(SysTenant).filter(SysTenant.code == code).first()


def get_tenants(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    keyword: Optional[str] = None,
    active: Optional[int] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
) -> tuple[List[SysTenant], int]:
    """获取租户列表

    Args:
        db: 数据库会话
        skip: 跳过的记录数，用于分页
        limit: 返回的最大记录数，用于分页
        keyword: 可选，搜索关键字，可用于按租户代号或名称搜索
        active: 可选，按租户状态筛选（0：失效；1：有效）
        sort_by: 可选，排序字段，可选值：id, ctime
        sort_order: 可选，排序方式，可选值：asc, desc
        start_time: 可选，创建时间的开始时间
        end_time: 可选，创建时间的结束时间

    Returns:
        租户列表和总数的元组
    """
    query = db.query(SysTenant)
    if keyword:
        query = query.filter(
            or_(
                SysTenant.code.like(f"%{keyword}%"), SysTenant.name.like(f"%{keyword}%")
            )
        )

    if active is not None:
        query = query.filter(SysTenant.active == active)

    # 按创建时间区间筛选
    if start_time:
        query = query.filter(SysTenant.ctime >= start_time)
    if end_time:
        query = query.filter(SysTenant.ctime <= end_time)

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(SysTenant.id.asc())
            else:
                query = query.order_by(SysTenant.id.desc())
        elif sort_by == "ctime":
            if sort_order == "asc":
                query = query.order_by(SysTenant.ctime.asc())
            else:
                query = query.order_by(SysTenant.ctime.desc())

    total = query.count()
    tenants = query.offset(skip).limit(limit).all()
    return tenants, total


def update_tenant(
    db: Session, tenant_id: int, tenant: TenantUpdate
) -> Optional[SysTenant]:
    """更新租户"""
    db_tenant = get_tenant(db, tenant_id)
    if not db_tenant:
        return None

    update_data = tenant.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_tenant, field, value)

    db.commit()
    db.refresh(db_tenant)
    return db_tenant


def delete_tenant(db: Session, tenant_id: int) -> bool:
    """删除租户"""
    db_tenant = get_tenant(db, tenant_id)
    if not db_tenant:
        return False

    db.delete(db_tenant)
    db.commit()
    return True
