from datetime import datetime
from typing import List, Optional

from sqlalchemy import or_
from sqlalchemy.orm import Session, joinedload

from app.core.security import get_password_hash
from app.models.models import SysUser, TntAdmin
from app.schemas.sys.tnt_admin import TntAdminCreate, TntAdminUpdate


def create_tnt_admin(db: Session, admin: TntAdminCreate) -> TntAdmin:
    """创建租户管理员"""
    try:
        # 创建系统用户
        db_user = SysUser(
            username=admin.username,
            passwd=get_password_hash(admin.password),
            token_version=0,
            active=True,
        )
        db.add(db_user)
        db.flush()  # 获取用户ID

        # 创建租户管理员
        db_admin = TntAdmin(
            tenant_id=admin.tenant_id,
            uid=db_user.id,
            name=admin.name,
            role=admin.role,
        )
        db.add(db_admin)
        db.commit()
        db.refresh(db_admin)

        # 重新查询以获取关联的用户信息
        return (
            db.query(TntAdmin)
            .options(joinedload(TntAdmin.user))
            .filter(TntAdmin.id == db_admin.id)
            .first()
        )
    except Exception as e:
        # 如果出现任何异常，回滚事务
        db.rollback()
        raise e


def get_tnt_admin(db: Session, admin_id: int) -> Optional[TntAdmin]:
    """获取租户管理员"""
    return (
        db.query(TntAdmin)
        .options(joinedload(TntAdmin.user))
        .filter(TntAdmin.id == admin_id)
        .first()
    )


def get_tnt_admin_by_username(db: Session, username: str) -> Optional[TntAdmin]:
    """通过用户名获取租户管理员"""
    return (
        db.query(TntAdmin)
        .join(SysUser, TntAdmin.uid == SysUser.id)
        .filter(SysUser.username == username)
        .first()
    )


def get_tnt_admins(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    keyword: Optional[str] = None,
    tenant_id: Optional[int] = None,
    active: Optional[int] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
) -> tuple[List[TntAdmin], int]:
    """获取租户管理员列表

    Args:
        db: 数据库会话
        skip: 跳过的记录数，用于分页
        limit: 返回的最大记录数，用于分页
        keyword: 可选，搜索关键字，可用于按用户名或姓名搜索
        tenant_id: 可选，租户ID筛选
        active: 可选，用户状态筛选（0：失效；1：有效）
        sort_by: 可选，排序字段，可选值：id, ctime
        sort_order: 可选，排序方式，可选值：asc, desc
        start_time: 可选，创建时间的开始时间
        end_time: 可选，创建时间的结束时间

    Returns:
        租户管理员列表和总数的元组
    """
    query = db.query(TntAdmin)

    # 当需要搜索用户名、按active筛选、按时间筛选或按ctime排序时，需要JOIN user表
    need_join_user = (
        keyword or active is not None or start_time or end_time or (sort_by == "ctime")
    )
    if need_join_user:
        query = query.join(SysUser, TntAdmin.uid == SysUser.id)

    if keyword:
        query = query.filter(
            or_(
                SysUser.username.like(f"%{keyword}%"),
                TntAdmin.name.like(f"%{keyword}%"),
            )
        )
    if tenant_id:
        query = query.filter(TntAdmin.tenant_id == tenant_id)
    if active is not None:
        query = query.filter(SysUser.active == active)

    # 按创建时间区间筛选
    if start_time:
        query = query.filter(SysUser.ctime >= start_time)
    if end_time:
        query = query.filter(SysUser.ctime <= end_time)

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntAdmin.id.asc())
            else:
                query = query.order_by(TntAdmin.id.desc())
        elif sort_by == "ctime":
            if sort_order == "asc":
                query = query.order_by(SysUser.ctime.asc())
            else:
                query = query.order_by(SysUser.ctime.desc())

    total = query.count()
    admins = query.offset(skip).limit(limit).all()
    return admins, total


def update_tnt_admin(
    db: Session, admin_id: int, admin: TntAdminUpdate
) -> Optional[TntAdmin]:
    """更新租户管理员"""
    db_admin = (
        db.query(TntAdmin)
        .options(joinedload(TntAdmin.user))
        .filter(TntAdmin.id == admin_id)
        .first()
    )
    if not db_admin:
        return None

    # 更新系统用户
    if admin.username or admin.password or admin.active is not None:
        user = db_admin.user  # 使用已经加载的关联对象
        if user:
            if admin.username:
                user.username = admin.username
            if admin.password:
                user.passwd = get_password_hash(admin.password)
                user.token_version += 1
            if admin.active is not None:
                user.active = admin.active

    # 更新租户管理员信息
    for field, value in admin.model_dump(exclude_unset=True).items():
        if field not in [
            "password",
            "username",
            "active",
        ]:  # 这些字段已经在上面处理过了
            setattr(db_admin, field, value)

    db.commit()
    db.refresh(db_admin)
    return db_admin


def delete_tnt_admin(db: Session, admin_id: int) -> bool:
    """删除租户管理员"""
    db_admin = (
        db.query(TntAdmin)
        .options(joinedload(TntAdmin.user))
        .filter(TntAdmin.id == admin_id)
        .first()
    )
    if not db_admin:
        return False

    # 删除关联的系统用户
    user = db_admin.user  # 使用已经加载的关联对象
    if user:
        db.delete(user)

    # 删除租户管理员
    db.delete(db_admin)
    db.commit()
    return True
