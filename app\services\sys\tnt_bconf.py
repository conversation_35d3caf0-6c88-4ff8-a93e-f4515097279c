from typing import List, Optional

from sqlalchemy import or_
from sqlalchemy.orm import Session, selectinload

from app.models.models import TntBconf
from app.schemas.sys.tnt_bconf import TntBconfCreate, TntBconfUpdate


def create_tnt_bconf(db: Session, bconf: TntBconfCreate) -> TntBconf:
    """创建租户机器人设置"""
    db_bconf = TntBconf(**bconf.model_dump())
    db.add(db_bconf)
    db.commit()
    db.refresh(db_bconf, ['bot', 'tenant'])  # 刷新时加载关联数据
    return db_bconf


def get_tnt_bconf(db: Session, tenant_id: int, key: str) -> Optional[TntBconf]:
    """获取租户机器人设置"""
    return (
        db.query(TntBconf)
        .options(selectinload(TntBconf.bot), selectinload(TntBconf.tenant))
        .filter(TntBconf.tenant_id == tenant_id, TntBconf.key == key)
        .first()
    )


def get_tnt_bconfs(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    keyword: Optional[str] = None,
    tenant_id: Optional[int] = None,
) -> tuple[List[TntBconf], int]:
    """获取租户机器人设置列表"""
    query = db.query(TntBconf).options(selectinload(TntBconf.bot), selectinload(TntBconf.tenant))
    if keyword:
        query = query.filter(
            or_(TntBconf.key.like(f"%{keyword}%"), TntBconf.notes.like(f"%{keyword}%"))
        )
    if tenant_id:
        query = query.filter(TntBconf.tenant_id == tenant_id)
    total = query.count()
    bconfs = query.offset(skip).limit(limit).all()
    return bconfs, total


def update_tnt_bconf(
    db: Session, tenant_id: int, key: str, bconf: TntBconfUpdate
) -> Optional[TntBconf]:
    """更新租户机器人设置"""
    db_bconf = get_tnt_bconf(db, tenant_id, key)
    if not db_bconf:
        return None

    update_data = bconf.model_dump(exclude_unset=True)
    
    # 如果要更新key，需要特殊处理（因为key是主键）
    if 'key' in update_data and update_data['key'] != key:
        new_key = update_data.pop('key')
        
        # 检查新key是否已存在
        existing_bconf = get_tnt_bconf(db, tenant_id, new_key)
        if existing_bconf:
            raise ValueError(f"租户 {tenant_id} 的配置键名 '{new_key}' 已存在")
        
        # 创建新记录
        new_bconf_data = {
            'tenant_id': tenant_id,
            'key': new_key,
            'bid': update_data.get('bid', db_bconf.bid),
            'notes': update_data.get('notes', db_bconf.notes),
        }
        new_db_bconf = TntBconf(**new_bconf_data)
        
        # 删除旧记录，添加新记录
        db.delete(db_bconf)
        db.add(new_db_bconf)
        db.commit()
        db.refresh(new_db_bconf, ['bot', 'tenant'])
        return new_db_bconf
    else:
        # 常规更新
        for field, value in update_data.items():
            setattr(db_bconf, field, value)
        
        db.commit()
        db.refresh(db_bconf, ['bot', 'tenant'])  # 刷新时加载关联数据
        return db_bconf


def delete_tnt_bconf(db: Session, tenant_id: int, key: str) -> bool:
    """删除租户机器人设置"""
    db_bconf = get_tnt_bconf(db, tenant_id, key)
    if not db_bconf:
        return False

    db.delete(db_bconf)
    db.commit()
    return True
