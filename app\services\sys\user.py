from datetime import datetime
from typing import List, Optional, Tuple

from sqlalchemy import or_
from sqlalchemy.orm import Session

from app.core.security import get_password_hash
from app.models.models import SysUser
from app.schemas.sys.user import SysUserCreate, SysUserUpdate


def create_user(db: Session, user: SysUserCreate) -> SysUser:
    """创建用户"""
    db_user = SysUser(
        username=user.username,
        passwd=get_password_hash(user.password),
        token_version=0,
        active=1,
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def get_user(db: Session, user_id: int) -> Optional[SysUser]:
    """获取用户"""
    return db.query(SysUser).filter(SysUser.id == user_id).first()


def get_user_by_username(db: Session, username: str) -> Optional[SysUser]:
    """根据用户名获取用户"""
    return db.query(SysUser).filter(SysUser.username == username).first()


def get_users(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    keyword: Optional[str] = None,
    active: Optional[int] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
) -> Tuple[List[SysUser], int]:
    """获取用户列表

    Args:
        db: 数据库会话
        skip: 跳过的记录数，用于分页
        limit: 返回的最大记录数，用于分页
        keyword: 可选，搜索关键字，可用于按用户名搜索
        active: 可选，按用户状态筛选（0：失效；1：有效）
        sort_by: 可选，排序字段，可选值：id, ctime
        sort_order: 可选，排序方式，可选值：asc, desc
        start_time: 可选，创建时间的开始时间
        end_time: 可选，创建时间的结束时间

    Returns:
        用户列表和总数的元组
    """
    query = db.query(SysUser)

    if keyword:
        query = query.filter(SysUser.username.like(f"%{keyword}%"))

    if active is not None:
        query = query.filter(SysUser.active == active)

    # 按创建时间区间筛选
    if start_time:
        query = query.filter(SysUser.ctime >= start_time)
    if end_time:
        query = query.filter(SysUser.ctime <= end_time)

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(SysUser.id.asc())
            else:
                query = query.order_by(SysUser.id.desc())
        elif sort_by == "ctime":
            if sort_order == "asc":
                query = query.order_by(SysUser.ctime.asc())
            else:
                query = query.order_by(SysUser.ctime.desc())

    total = query.count()
    users = query.offset(skip).limit(limit).all()
    return users, total


def update_user(db: Session, user_id: int, user: SysUserUpdate) -> Optional[SysUser]:
    """更新用户"""
    db_user = get_user(db, user_id)
    if not db_user:
        return None

    # 更新字段
    update_data = user.model_dump(exclude_unset=True)

    # 记录原始激活状态
    original_active = db_user.active

    # 特殊处理密码字段
    if "password" in update_data:
        db_user.passwd = get_password_hash(update_data["password"])
        db_user.token_version += 1  # 密码更新时增加token版本号
        del update_data["password"]

    # 更新其他字段
    for field, value in update_data.items():
        setattr(db_user, field, value)

    # 如果用户被停用，增加token版本号强制登出
    if "active" in update_data and original_active == 1 and update_data["active"] == 0:
        db_user.token_version += 1

    db.commit()
    db.refresh(db_user)
    return db_user


def delete_user(db: Session, user_id: int) -> bool:
    """删除用户"""
    db_user = get_user(db, user_id)
    if not db_user:
        return False

    db.delete(db_user)
    db.commit()
    return True
