from datetime import datetime
from typing import List, Optional

from sqlalchemy.orm import Session

from app.models.models import TntTeacher
from app.schemas.teacher import TeacherCreate, TeacherUpdate


def get_teachers(
    db: Session,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    name: Optional[str] = None,
    gender: Optional[int] = None,
    intro: Optional[str] = None,
    notes: Optional[str] = None,
) -> tuple[List[TntTeacher], int]:
    """获取教师列表

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        skip: 跳过的记录数，用于分页
        limit: 返回的最大记录数，用于分页
        sort_by: 可选，排序字段，可选值：id, ctime
        sort_order: 可选，排序方式，可选值：asc, desc
        start_time: 可选，创建时间的开始时间
        end_time: 可选，创建时间的结束时间
        name: 可选，按姓名搜索
        gender: 可选，按性别筛选
        intro: 可选，按简介搜索
        notes: 可选，按备注搜索

    Returns:
        教师列表和总数的元组
    """
    query = db.query(TntTeacher).filter(TntTeacher.tenant_id == tenant_id)
    
    # 按创建时间区间筛选
    if start_time:
        query = query.filter(TntTeacher.ctime >= start_time)
    if end_time:
        query = query.filter(TntTeacher.ctime <= end_time)
    
    # 按姓名搜索
    if name:
        query = query.filter(TntTeacher.name.contains(name))
    
    # 按性别筛选
    if gender is not None:
        query = query.filter(TntTeacher.gender == gender)
    
    # 按简介搜索
    if intro:
        query = query.filter(TntTeacher.intro.contains(intro))
    
    # 按备注搜索
    if notes:
        query = query.filter(TntTeacher.notes.contains(notes))

    # 获取总数
    total = query.count()

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntTeacher.id.asc())
            else:
                query = query.order_by(TntTeacher.id.desc())
        elif sort_by == "ctime":
            if sort_order == "asc":
                query = query.order_by(TntTeacher.ctime.asc())
            else:
                query = query.order_by(TntTeacher.ctime.desc())

    teachers = query.offset(skip).limit(limit).all()
    return teachers, total


def get_teacher(db: Session, teacher_id: int, tenant_id: Optional[int] = None) -> Optional[TntTeacher]:
    """根据ID获取教师"""
    query = db.query(TntTeacher).filter(TntTeacher.id == teacher_id)
    if tenant_id is not None:
        query = query.filter(TntTeacher.tenant_id == tenant_id)
    return query.first()


def create_teacher(db: Session, teacher_in: TeacherCreate) -> TntTeacher:
    """创建教师"""
    db_teacher = TntTeacher(**teacher_in.model_dump())
    db.add(db_teacher)
    db.commit()
    db.refresh(db_teacher)
    return db_teacher


def update_teacher(
    db: Session, teacher_id: int, teacher_in: TeacherUpdate, tenant_id: Optional[int] = None
) -> Optional[TntTeacher]:
    """更新教师"""
    query = db.query(TntTeacher).filter(TntTeacher.id == teacher_id)
    if tenant_id is not None:
        query = query.filter(TntTeacher.tenant_id == tenant_id)
    
    db_teacher = query.first()
    if not db_teacher:
        return None

    update_data = teacher_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_teacher, field, value)

    db.commit()
    db.refresh(db_teacher)
    return db_teacher


def delete_teacher(db: Session, teacher_id: int) -> bool:
    """删除教师"""
    db_teacher = db.query(TntTeacher).filter(TntTeacher.id == teacher_id).first()
    if not db_teacher:
        return False

    db.delete(db_teacher)
    db.commit()
    return True
