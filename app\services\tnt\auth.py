from typing import Optional, <PERSON><PERSON>

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON>asswordBearer
from jose import JWTError, jwt
from pydantic import ValidationError
from sqlalchemy.orm import Session, joinedload

from app.core.config import settings
from app.core.security import create_access_token
from app.db.session import get_db
from app.models.models import SysUser, TntAdmin
from app.schemas.tnt.auth import TntAdminPasswordUpdate, TntAdminTokenPayload
from app.services.auth import authenticate_user, update_user_password

reusable_oauth2 = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/tnt/auth/login")


def get_current_tnt_admin(
    db: Session = Depends(get_db), token: str = Depends(reusable_oauth2)
) -> TntAdmin:
    """获取当前租户管理员"""
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = TntAdminTokenPayload(**payload)
    except (JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    admin = db.query(TntAdmin).filter(TntAdmin.uid == token_data.uid).first()
    if not admin:
        raise HTTPException(status_code=404, detail="Not found")
    if not admin.user.active:
        raise HTTPException(status_code=400, detail="Inactive")

    return admin


def login(
    db: Session, username: str, password: str
) -> Tuple[Optional[TntAdmin], Optional[str], Optional[str]]:
    """租户管理员登录

    Returns:
        Tuple[Optional[TntAdmin], Optional[str], Optional[str]]: (admin, token, error_message)
    """
    user = authenticate_user(db, username, password)
    if not user:
        return None, None, "用户名或密码错误"

    admin = db.query(TntAdmin).filter(TntAdmin.uid == user.id).first()
    if not admin:
        return None, None, "该用户不是租户管理员"

    if not user.active:
        return None, None, "用户已被禁用"

    token = create_access_token(
        data={
            "uid": user.id,
            "utype": "tnt",
        }
    )
    return admin, token, None


def logout(db: Session, user_id: int) -> None:
    """租户管理员登出"""
    user = db.query(SysUser).filter(SysUser.id == user_id).first()
    if user:
        user.token_version += 1
        db.commit()


def get_tnt_admin_profile(db: Session, admin_id: int) -> Optional[TntAdmin]:
    """获取租户管理员个人信息"""
    return (
        db.query(TntAdmin)
        .options(joinedload(TntAdmin.user))
        .filter(TntAdmin.id == admin_id)
        .first()
    )


def update_tnt_admin_password(
    db: Session, admin_id: int, password_update: TntAdminPasswordUpdate
) -> bool:
    """更新租户管理员密码"""
    admin = get_tnt_admin_profile(db, admin_id)
    if not admin:
        return False

    return update_user_password(
        db, admin.uid, password_update.old_password, password_update.new_password
    )
