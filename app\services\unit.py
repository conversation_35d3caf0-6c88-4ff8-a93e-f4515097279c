from typing import List, Optional, Tuple

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from app.models.models import TntUnit, TntWorksheetAsm
from app.schemas.unit import UnitCreate, UnitUpdate
from app.schemas.worksheet import UnitOrderItem


def get_units_by_worksheet(
    db: Session,
    tenant_id: int,
    worksheet_id: int,
) -> List[TntUnit]:
    """获取指定作业单下的单元列表（按priority排序）"""
    return (
        db.query(TntUnit)
        .filter(TntUnit.tenant_id == tenant_id, TntUnit.wid == worksheet_id)
        .order_by(TntUnit.priority.asc())
        .all()
    )


def get_units(
    db: Session,
    tenant_id: int,
    worksheet_id: Optional[int] = None,
    skip: int = 0,
    limit: int = 100,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
) -> List[TntUnit]:
    """获取单元列表"""
    query = db.query(TntUnit).filter(TntUnit.tenant_id == tenant_id)

    if worksheet_id is not None:
        query = query.filter(TntUnit.wid == worksheet_id)

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntUnit.id.asc())
            else:
                query = query.order_by(TntUnit.id.desc())
        pass

    return query.offset(skip).limit(limit).all()


def get_unit(db: Session, unit_id: int, worksheet_id: Optional[int] = None, tenant_id: Optional[int] = None) -> Optional[TntUnit]:
    """根据ID获取单元"""
    query = db.query(TntUnit).filter(TntUnit.id == unit_id)
    if worksheet_id is not None:
        query = query.filter(TntUnit.wid == worksheet_id)
    if tenant_id is not None:
        query = query.filter(TntUnit.tenant_id == tenant_id)
    return query.first()


def create_unit(db: Session, unit_in: UnitCreate) -> TntUnit:
    """创建单元"""
    # 获取当前租户下同一作业单的 priority 的最大值
    max_priority = db.query(TntUnit).filter(
        TntUnit.tenant_id == unit_in.tenant_id,
        TntUnit.wid == unit_in.wid
    ).order_by(TntUnit.priority.desc()).first()
    
    # 设置新的 priority 值
    new_priority = (max_priority.priority + 1) if max_priority else 1
    
    # 创建单元数据，覆盖 priority 值
    unit_data = unit_in.model_dump()
    unit_data['priority'] = new_priority
    
    db_unit = TntUnit(**unit_data)
    db.add(db_unit)
    db.commit()
    db.refresh(db_unit)
    return db_unit


def update_unit(db: Session, unit_id: int, unit_in: UnitUpdate, worksheet_id: Optional[int] = None, tenant_id: Optional[int] = None) -> Optional[TntUnit]:
    """更新单元"""
    query = db.query(TntUnit).filter(TntUnit.id == unit_id)
    if worksheet_id is not None:
        query = query.filter(TntUnit.wid == worksheet_id)
    if tenant_id is not None:
        query = query.filter(TntUnit.tenant_id == tenant_id)
    
    db_unit = query.first()
    if not db_unit:
        return None

    update_data = unit_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_unit, field, value)

    db.commit()
    db.refresh(db_unit)
    return db_unit


def delete_unit(db: Session, unit_id: int, worksheet_id: Optional[int] = None, tenant_id: Optional[int] = None) -> bool:
    """删除单元"""
    query = db.query(TntUnit).filter(TntUnit.id == unit_id)
    if worksheet_id is not None:
        query = query.filter(TntUnit.wid == worksheet_id)
    if tenant_id is not None:
        query = query.filter(TntUnit.tenant_id == tenant_id)
    
    db_unit = query.first()
    if not db_unit:
        return False

    db.delete(db_unit)
    db.commit()
    return True


def batch_update_unit_order(
    db: Session, tenant_id: int, worksheet_id: int, unit_orders: List[UnitOrderItem]
) -> Tuple[List[TntUnit], int, int]:
    """批量更新单元顺序

    Returns:
        Tuple[更新后的单元列表, 成功更新数量, 总请求数量]
    """
    success_count = 0
    updated_units = []

    try:
        # 在一个事务中执行所有更新
        for order_item in unit_orders:
            # 验证单元是否属于该租户和工作表
            db_unit = (
                db.query(TntUnit)
                .filter(
                    TntUnit.id == order_item.id,
                    TntUnit.tenant_id == tenant_id,
                    TntUnit.wid == worksheet_id,
                )
                .first()
            )

            if db_unit:
                db_unit.priority = order_item.priority
                updated_units.append(db_unit)
                success_count += 1

        # 所有更新完成后一次性提交
        if updated_units:
            db.commit()
            # 刷新所有更新的单元数据
            for unit in updated_units:
                db.refresh(unit)
                
    except Exception as e:
        # 如果有任何错误，回滚事务
        db.rollback()
        raise e

    return updated_units, success_count, len(unit_orders)


def batch_delete_units(
    db: Session, tenant_id: int, worksheet_id: int, unit_ids: List[int]
) -> Tuple[List[int], int, int]:
    """批量删除单元（先删除关联的worksheet_asm）

    Returns:
        Tuple[成功删除的单元ID列表, 成功删除的单元数量, 同时删除的构成关系数量]
    """
    deleted_unit_ids = []
    deleted_asm_count = 0
    
    try:
        # 开始事务
        for unit_id in unit_ids:
            # 验证单元是否属于该租户和工作表
            db_unit = (
                db.query(TntUnit)
                .filter(
                    TntUnit.id == unit_id,
                    TntUnit.tenant_id == tenant_id,
                    TntUnit.wid == worksheet_id,
                )
                .first()
            )

            if db_unit:
                # 先删除该单元关联的所有worksheet_asm
                asm_query = db.query(TntWorksheetAsm).filter(
                    TntWorksheetAsm.tenant_id == tenant_id,
                    TntWorksheetAsm.wid == worksheet_id,
                    TntWorksheetAsm.uid == unit_id,
                )
                
                # 统计要删除的asm数量
                asm_count = asm_query.count()
                deleted_asm_count += asm_count
                
                # 删除关联的worksheet_asm
                asm_query.delete(synchronize_session=False)
                
                # 删除单元
                db.delete(db_unit)
                deleted_unit_ids.append(unit_id)

        # 提交事务
        db.commit()
        
    except SQLAlchemyError:
        # 出错时回滚
        db.rollback()
        raise

    return deleted_unit_ids, len(deleted_unit_ids), deleted_asm_count
