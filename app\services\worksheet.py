from datetime import datetime
from typing import List, Optional, Tuple

from sqlalchemy.orm import Session, joinedload

from app.models.models import TntWorksheet, TntExercise
from app.schemas.worksheet import WorksheetCreate, WorksheetUpdate


def get_worksheets(
    db: Session,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    title: Optional[str] = None,
    published: Optional[bool] = None,
    active: Optional[bool] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
) -> Tuple[List[TntWorksheet], int]:
    """获取工作表列表

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        skip: 跳过的记录数，用于分页
        limit: 返回的最大记录数，用于分页
        title: 可选，按工作表标题搜索，支持模糊匹配
        published: 可选，按发布状态搜索
        active: 可选，按激活状态搜索
        sort_by: 可选，排序字段，可选值：id, ctime
        sort_order: 可选，排序方式，可选值：asc, desc
        start_time: 可选，创建时间的开始时间
        end_time: 可选，创建时间的结束时间

    Returns:
        (工作表列表, 总数)
    """
    # 基础查询，联合查询exercise表
    query = db.query(TntWorksheet).join(TntExercise).filter(
        TntWorksheet.tenant_id == tenant_id
    ).options(joinedload(TntWorksheet.exercise))

    # 按标题搜索
    if title:
        query = query.filter(TntExercise.title.like(f"%{title}%"))

    # 按发布状态搜索
    if published is not None:
        query = query.filter(TntExercise.published == published)

    # 按激活状态搜索
    if active is not None:
        query = query.filter(TntExercise.active == active)

    # 按创建时间区间筛选
    if start_time:
        query = query.filter(TntExercise.ctime >= start_time)
    if end_time:
        query = query.filter(TntExercise.ctime <= end_time)

    # 计算总数
    total = query.count()

    # 处理排序
    if sort_by and sort_order:
        if sort_by == "id":
            if sort_order == "asc":
                query = query.order_by(TntWorksheet.id.asc())
            else:
                query = query.order_by(TntWorksheet.id.desc())
        elif sort_by == "ctime":
            if sort_order == "asc":
                query = query.order_by(TntExercise.ctime.asc())
            else:
                query = query.order_by(TntExercise.ctime.desc())

    worksheets = query.offset(skip).limit(limit).all()
    return worksheets, total


def get_worksheet(db: Session, worksheet_id: int, tenant_id: Optional[int] = None) -> Optional[TntWorksheet]:
    """根据ID获取工作表"""
    query = db.query(TntWorksheet).join(TntExercise).filter(
        TntWorksheet.id == worksheet_id
    ).options(joinedload(TntWorksheet.exercise))
    
    if tenant_id is not None:
        query = query.filter(TntWorksheet.tenant_id == tenant_id)
    return query.first()


def create_worksheet(db: Session, worksheet_in: WorksheetCreate) -> TntWorksheet:
    """创建工作表"""
    try:
        # 开始事务
        # 首先创建exercise，type=1（作业单）
        exercise_data = {
            "tenant_id": worksheet_in.tenant_id,
            "title": worksheet_in.title,
            "type": 1,  # 作业单类型
            "intro": worksheet_in.intro,
            "duration": worksheet_in.duration,
            "version": worksheet_in.version,
            "bgtext": worksheet_in.bgtext,
            "bgvideo": worksheet_in.bgvideo,
            "notes": worksheet_in.notes,
            "published": worksheet_in.published,
        }
        
        db_exercise = TntExercise(**exercise_data)
        db.add(db_exercise)
        db.flush()  # 刷新以获取exercise的ID，但不提交
        
        # 创建worksheet
        db_worksheet = TntWorksheet(
            tenant_id=worksheet_in.tenant_id,
            eid=db_exercise.id
        )
        db.add(db_worksheet)
        
        # 提交事务
        db.commit()
        
        # 刷新对象以获取最新数据
        db.refresh(db_worksheet)
        db.refresh(db_exercise)
        
        # 加载关联数据
        db_worksheet = db.query(TntWorksheet).filter(
            TntWorksheet.id == db_worksheet.id
        ).options(joinedload(TntWorksheet.exercise)).first()
        
        return db_worksheet
        
    except Exception as e:
        db.rollback()
        raise e


def update_worksheet(
    db: Session, worksheet_id: int, worksheet_in: WorksheetUpdate, tenant_id: Optional[int] = None
) -> Optional[TntWorksheet]:
    """更新工作表"""
    query = db.query(TntWorksheet).join(TntExercise).filter(
        TntWorksheet.id == worksheet_id
    ).options(joinedload(TntWorksheet.exercise))
    
    if tenant_id is not None:
        query = query.filter(TntWorksheet.tenant_id == tenant_id)
    
    db_worksheet = query.first()
    if not db_worksheet:
        return None

    try:
        # 更新exercise相关字段
        exercise_update_data = worksheet_in.model_dump(
            exclude_unset=True,
            include={'title', 'intro', 'duration', 'version', 'bgtext', 'bgvideo', 'notes', 'published', 'active'}
        )
        
        if exercise_update_data:
            for field, value in exercise_update_data.items():
                setattr(db_worksheet.exercise, field, value)

        db.commit()
        
        # 重新加载数据
        db.refresh(db_worksheet)
        return db_worksheet
        
    except Exception as e:
        db.rollback()
        raise e


def delete_worksheet(db: Session, worksheet_id: int, tenant_id: Optional[int] = None) -> bool:
    """删除工作表"""
    query = db.query(TntWorksheet).filter(TntWorksheet.id == worksheet_id)
    if tenant_id is not None:
        query = query.filter(TntWorksheet.tenant_id == tenant_id)
    
    db_worksheet = query.first()
    if not db_worksheet:
        return False

    try:
        # 在事务中删除worksheet和对应的exercise
        exercise_id = db_worksheet.eid
        
        # 删除worksheet
        db.delete(db_worksheet)
        
        # 删除对应的exercise
        db_exercise = db.query(TntExercise).filter(TntExercise.id == exercise_id).first()
        if db_exercise:
            db.delete(db_exercise)
        
        db.commit()
        return True
        
    except Exception as e:
        db.rollback()
        raise e
