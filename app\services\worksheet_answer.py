from typing import List, Optional

from sqlalchemy.orm import Session

from app.models.models import TntWorksheetAnswer
from app.schemas.worksheet_answer import WorksheetAnswerCreate, WorksheetAnswerUpdate


def get_worksheet_answers(
    db: Session,
    tenant_id: int,
    skip: int = 0,
    limit: int = 100,
    worksheet_id: Optional[int] = None,
    student_id: Optional[int] = None,
    active: Optional[int] = None,
) -> List[TntWorksheetAnswer]:
    """获取工作表答案列表"""
    query = db.query(TntWorksheetAnswer).filter(
        TntWorksheetAnswer.tenant_id == tenant_id
    )

    if worksheet_id is not None:
        query = query.filter(TntWorksheetAnswer.worksheet_id == worksheet_id)

    return query.offset(skip).limit(limit).all()


def get_worksheet_answer(
    db: Session, worksheet_answer_id: int, worksheet_id: Optional[int] = None, 
    student_id: Optional[int] = None, tenant_id: Optional[int] = None
) -> Optional[TntWorksheetAnswer]:
    """根据ID获取工作表答案"""
    query = db.query(TntWorksheetAnswer).filter(TntWorksheetAnswer.id == worksheet_answer_id)
    if tenant_id is not None:
        query = query.filter(TntWorksheetAnswer.tenant_id == tenant_id)
    if worksheet_id is not None:
        query = query.filter(TntWorksheetAnswer.worksheet_id == worksheet_id)
    return query.first()


def create_worksheet_answer(
    db: Session, worksheet_answer_in: WorksheetAnswerCreate
) -> TntWorksheetAnswer:
    """创建工作表答案"""
    db_worksheet_answer = TntWorksheetAnswer(**worksheet_answer_in.model_dump())
    db.add(db_worksheet_answer)
    db.commit()
    db.refresh(db_worksheet_answer)
    return db_worksheet_answer


def update_worksheet_answer(
    db: Session, worksheet_answer_id: int, worksheet_answer_in: WorksheetAnswerUpdate,
    worksheet_id: Optional[int] = None, student_id: Optional[int] = None, 
    tenant_id: Optional[int] = None
) -> Optional[TntWorksheetAnswer]:
    """更新工作表答案"""
    query = db.query(TntWorksheetAnswer).filter(TntWorksheetAnswer.id == worksheet_answer_id)
    if tenant_id is not None:
        query = query.filter(TntWorksheetAnswer.tenant_id == tenant_id)
    if worksheet_id is not None:
        query = query.filter(TntWorksheetAnswer.worksheet_id == worksheet_id)
    
    db_worksheet_answer = query.first()
    if not db_worksheet_answer:
        return None

    update_data = worksheet_answer_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_worksheet_answer, field, value)

    db.commit()
    db.refresh(db_worksheet_answer)
    return db_worksheet_answer


def delete_worksheet_answer(
    db: Session, worksheet_answer_id: int, worksheet_id: Optional[int] = None,
    student_id: Optional[int] = None, tenant_id: Optional[int] = None
) -> bool:
    """删除工作表答案"""
    query = db.query(TntWorksheetAnswer).filter(TntWorksheetAnswer.id == worksheet_answer_id)
    if tenant_id is not None:
        query = query.filter(TntWorksheetAnswer.tenant_id == tenant_id)
    if worksheet_id is not None:
        query = query.filter(TntWorksheetAnswer.worksheet_id == worksheet_id)
    
    db_worksheet_answer = query.first()
    if not db_worksheet_answer:
        return False

    db.delete(db_worksheet_answer)
    db.commit()
    return True
