from typing import List, Tuple, Optional

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from app.models.models import TntUnit, TntQuestion, TntWorksheetAsm
from app.schemas.worksheet import WorksheetAsmOrderItem, WorksheetAsmCreate, UnitQuestionItem



def get_questions_by_worksheet_unit(
    db: Session,
    tenant_id: int,
    worksheet_id: int,
    unit_id: int,
) -> List[TntQuestion]:
    """通过作业单构成关系表获取指定工作表下指定单元的问题列表（按priority排序）"""
    return (
        db.query(TntQuestion)
        .join(TntWorksheetAsm, TntQuestion.id == TntWorksheetAsm.qid)
        .filter(
            TntWorksheetAsm.tenant_id == tenant_id,
            TntWorksheetAsm.wid == worksheet_id,
            TntWorksheetAsm.uid == unit_id,
            TntQuestion.tenant_id == tenant_id,
        )
        .order_by(TntWorksheetAsm.priority.asc())
        .all()
    )


def get_worksheet_unit_questions_with_asm(
    db: Session,
    tenant_id: int,
    worksheet_id: int,
    unit_id: int,
) -> List[TntWorksheetAsm]:
    """获取指定工作表下指定单元的问题列表（包含asm关系信息，按priority排序）"""
    return (
        db.query(TntWorksheetAsm)
        .join(TntQuestion, TntWorksheetAsm.qid == TntQuestion.id)
        .filter(
            TntWorksheetAsm.tenant_id == tenant_id,
            TntWorksheetAsm.wid == worksheet_id,
            TntWorksheetAsm.uid == unit_id,
            TntQuestion.tenant_id == tenant_id,
        )
        .order_by(TntWorksheetAsm.priority.asc())
        .all()
    )


def get_worksheet_structure(
    db: Session,
    tenant_id: int,
    worksheet_id: int,
) -> List[TntWorksheetAsm]:
    """获取工作表的完整结构（按priority排序）"""
    return (
        db.query(TntWorksheetAsm)
        .filter(
            TntWorksheetAsm.tenant_id == tenant_id,
            TntWorksheetAsm.wid == worksheet_id,
        )
        .order_by(TntWorksheetAsm.priority.asc())
        .all()
    )


def batch_update_worksheet_asm_order(
    db: Session, 
    tenant_id: int, 
    worksheet_id: int, 
    unit_id: Optional[int], 
    asm_orders: List[WorksheetAsmOrderItem]
) -> Tuple[List[TntWorksheetAsm], int, int]:
    """批量更新作业单构成关系顺序

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        worksheet_id: 工作表ID
        unit_id: 单元ID，可选，如果提供则只更新该单元下的问题顺序
        asm_orders: 构成关系顺序列表

    Returns:
        Tuple[更新后的asm关系列表, 成功更新数量, 总请求数量]
    """
    success_count = 0
    updated_asms = []

    try:
        # 在一个事务中执行所有更新
        for order_item in asm_orders:
            # 构建查询条件
            query = db.query(TntWorksheetAsm).filter(
                TntWorksheetAsm.id == order_item.id,
                TntWorksheetAsm.tenant_id == tenant_id,
                TntWorksheetAsm.wid == worksheet_id,
            )
            
            # 如果指定了单元ID，则添加单元过滤条件
            if unit_id is not None:
                query = query.filter(TntWorksheetAsm.uid == unit_id)
            
            db_asm = query.first()

            if db_asm:
                db_asm.priority = order_item.priority
                success_count += 1

        # 所有更新完成后一次性提交
        if success_count > 0:
            db.commit()

            # 获取更新后的asm关系列表
            if unit_id is not None:
                # 如果指定了单元ID，获取该单元下的asm关系
                updated_asms = get_worksheet_unit_questions_with_asm(
                    db=db,
                    tenant_id=tenant_id,
                    worksheet_id=worksheet_id,
                    unit_id=unit_id,
                )
            else:
                # 如果没有指定单元ID，获取整个工作表的asm关系
                updated_asms = (
                    db.query(TntWorksheetAsm)
                    .join(TntQuestion, TntWorksheetAsm.qid == TntQuestion.id)
                    .filter(
                        TntWorksheetAsm.tenant_id == tenant_id,
                        TntWorksheetAsm.wid == worksheet_id,
                        TntQuestion.tenant_id == tenant_id,
                    )
                    .order_by(TntWorksheetAsm.priority.asc())
                    .all()
                )
                
    except Exception as e:
        # 如果有任何错误，回滚事务
        db.rollback()
        raise e

    return updated_asms, success_count, len(asm_orders)


def batch_delete_worksheet_asms(
    db: Session, 
    tenant_id: int, 
    worksheet_id: int, 
    unit_id: Optional[int], 
    asm_ids: List[int]
) -> Tuple[List[int], int]:
    """批量删除作业单构成关系

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        worksheet_id: 工作表ID
        unit_id: 单元ID，可选，如果提供则只删除该单元下的构成关系
        asm_ids: 构成关系ID列表

    Returns:
        Tuple[成功删除的构成关系ID列表, 成功删除的数量]
    """
    deleted_asm_ids = []
    
    try:
        # 开始事务
        for asm_id in asm_ids:
            # 构建查询条件
            query = db.query(TntWorksheetAsm).filter(
                TntWorksheetAsm.id == asm_id,
                TntWorksheetAsm.tenant_id == tenant_id,
                TntWorksheetAsm.wid == worksheet_id,
            )
            
            # 如果指定了单元ID，则添加单元过滤条件
            if unit_id is not None:
                query = query.filter(TntWorksheetAsm.uid == unit_id)
            
            db_asm = query.first()

            if db_asm:
                db.delete(db_asm)
                deleted_asm_ids.append(asm_id)

        # 提交事务
        db.commit()
        
    except SQLAlchemyError:
        # 出错时回滚
        db.rollback()
        raise

    return deleted_asm_ids, len(deleted_asm_ids)


def create_worksheet_asm(
    db: Session, 
    asm_in: WorksheetAsmCreate
) -> TntWorksheetAsm:
    """创建作业单构成关系

    Args:
        db: 数据库会话
        asm_in: 构成关系创建信息

    Returns:
        创建的构成关系对象
    """
    # 获取当前租户下同一工作表同一单元的 priority 的最大值
    max_priority = db.query(TntWorksheetAsm).filter(
        TntWorksheetAsm.tenant_id == asm_in.tenant_id,
        TntWorksheetAsm.wid == asm_in.wid,
        TntWorksheetAsm.uid == asm_in.uid
    ).order_by(TntWorksheetAsm.priority.desc()).first()
    
    # 设置新的 priority 值
    new_priority = (max_priority.priority + 1) if max_priority else 1
    
    # 创建构成关系数据，覆盖 priority 值
    asm_data = asm_in.model_dump()
    asm_data['priority'] = new_priority
    
    db_asm = TntWorksheetAsm(**asm_data)
    db.add(db_asm)
    db.commit()
    db.refresh(db_asm)
    
    return db_asm


def batch_move_worksheet_asms(
    db: Session, 
    tenant_id: int, 
    worksheet_id: int, 
    target_unit_id: int, 
    asm_ids: List[int]
) -> Tuple[List[int], int]:
    """批量移动作业单构成关系到指定单元

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        worksheet_id: 工作表ID
        target_unit_id: 目标单元ID
        asm_ids: 要移动的构成关系ID列表

    Returns:
        Tuple[成功移动的构成关系ID列表, 成功移动的数量]
    """
    moved_asm_ids = []
    
    try:
        # 验证目标单元是否存在且属于指定租户和工作表
        target_unit = db.query(TntUnit).filter(
            TntUnit.id == target_unit_id,
            TntUnit.tenant_id == tenant_id,
            TntUnit.wid == worksheet_id,
        ).first()
        
        if not target_unit:
            raise ValueError(f"目标单元不存在或不属于指定租户和工作表")
        
        # 开始事务
        for asm_id in asm_ids:
            # 查找要移动的构成关系
            db_asm = db.query(TntWorksheetAsm).filter(
                TntWorksheetAsm.id == asm_id,
                TntWorksheetAsm.tenant_id == tenant_id,
                TntWorksheetAsm.wid == worksheet_id,
            ).first()

            if db_asm:
                # 更新单元ID为目标单元ID
                db_asm.uid = target_unit_id
                moved_asm_ids.append(asm_id)

        # 提交事务
        db.commit()
        
    except SQLAlchemyError:
        # 出错时回滚
        db.rollback()
        raise

    return moved_asm_ids, len(moved_asm_ids)


def batch_update_unit_questions(
    db: Session,
    tenant_id: int,
    worksheet_id: int,
    unit_id: int,
    questions: List[UnitQuestionItem]
) -> Tuple[int, int, int, List[TntWorksheetAsm]]:
    """批量更新单元问题（添加/删除/更新 worksheet_asm 表）

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        worksheet_id: 工作表ID
        unit_id: 单元ID
        questions: 问题列表

    Returns:
        Tuple[新增数量, 更新数量, 删除数量, 最终的asm关系列表]
    """
    added_count = 0
    updated_count = 0
    deleted_count = 0
    
    try:
        # 获取当前单元下所有现有的问题关系
        existing_asms = db.query(TntWorksheetAsm).filter(
            TntWorksheetAsm.tenant_id == tenant_id,
            TntWorksheetAsm.wid == worksheet_id,
            TntWorksheetAsm.uid == unit_id,
        ).all()
        
        # 创建映射便于快速查找
        existing_qid_to_asm = {asm.qid: asm for asm in existing_asms}
        request_qid_to_question = {q.id: q for q in questions}
        
        # 处理新增和更新
        for question in questions:
            if question.id in existing_qid_to_asm:
                # 更新现有关系的优先级
                existing_asm = existing_qid_to_asm[question.id]
                if existing_asm.priority != question.priority:
                    existing_asm.priority = question.priority
                    updated_count += 1
            else:
                # 新增关系
                new_asm = TntWorksheetAsm(
                    tenant_id=tenant_id,
                    wid=worksheet_id,
                    uid=unit_id,
                    qid=question.id,
                    priority=question.priority
                )
                db.add(new_asm)
                added_count += 1
        
        # 处理删除：删除不在请求列表中的现有关系
        for existing_asm in existing_asms:
            if existing_asm.qid not in request_qid_to_question:
                db.delete(existing_asm)
                deleted_count += 1
        
        # 提交事务
        db.commit()
        
        # 获取更新后的问题关系列表
        final_asms = get_worksheet_unit_questions_with_asm(
            db=db,
            tenant_id=tenant_id,
            worksheet_id=worksheet_id,
            unit_id=unit_id,
        )
        
    except SQLAlchemyError:
        # 出错时回滚
        db.rollback()
        raise
    
    return added_count, updated_count, deleted_count, final_asms 